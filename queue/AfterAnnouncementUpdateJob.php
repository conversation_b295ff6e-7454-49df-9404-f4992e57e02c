<?php

namespace queue;

use common\helpers\DebugHelper;
use common\libs\Cache;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\ColumnAuto\JobAutoClassify;
use yii\base\BaseObject;
use yii\base\Exception;

class AfterAnnouncementUpdateJob extends BaseObject implements \yii\queue\JobInterface
{
    const TYPE_ANNOUNCEMENT = 1;
    const TYPE_JOB          = 2;
    public $type;
    public $id;

    /**
     * @throws Exception
     */
    public function execute($queue): bool
    {
        try {
            if ($this->type == self::TYPE_ANNOUNCEMENT) {
                $this->announcement();
            } else {
                $this->job();
            }
        } catch (\Exception $e) {
            // 把queue里面的错误记录下来
        }

        return true;
    }

    /**
     * @throws Exception
     */
    private function announcement()
    {
        try {
            $app = new AnnouncementAutoClassify($this->id);
            $app->run();

            $key = Cache::ALL_ANNOUNCEMENT_AFTER_RUN_ID_KEY . ':' . $this->id;
            Cache::delete($key);
        } catch (\Exception $e) {
            // 把queue里面的错误记录下来
        }

        return true;
    }

    private function job()
    {
        try {
            $app = new JobAutoClassify($this->id);
            $app->run();

            $key = Cache::ALL_JOB_AFTER_RUN_ID_KEY . ':' . $this->id;
            Cache::delete($key);
        } catch (\Exception $e) {
            // 把queue里面的错误记录下来
        }

        return true;
    }
}