<?php

namespace timer\controllers;

use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyTopEquityRecord;
use Faker\Provider\Base;

/**
 * 处理职位投递权益失效定时任务类
 * php timer_yii job-apply-top-equity/run
 */
class JobApplyTopEquityController extends BaseTimerController
{
    /** @var int 失效天数 */
    private $days = 10;

    public function actionRun()
    {
        //处理指定时间时间段内的职位投递权益失效
        //1、先计算时间开始时间与结束时间
        $start_time = date('Y-m-d H:i:s', strtotime('-' . $this->days . ' day'));
        //2、获取在时间范围内没有失效的权益投递
        $list = BaseJobApplyTopEquityRecord::find()
            ->select([
                'id',
                'apply_id',
            ])
            ->andWhere([
                'equity_status' => BaseJobApplyTopEquityRecord::EQUITY_STATUS_EFFECT,
            ])
            ->andWhere([
                '<=',
                'add_time',
                $start_time,
            ])
            ->asArray()
            ->all();
        //3、失效
        foreach ($list as $item) {
            BaseJobApplyTopEquityRecord::expireEquity($item['apply_id'],
                BaseJobApplyTopEquityRecord::EXPIRE_TYPE_SYSTEM);
            BaseJobApply::expireEquity($item['apply_id']);
            self::log('职位投递权益ID：' . $item['id'] . ';失效完成；投递ID为：' . $item['apply_id']);
        }
        self::log('处理完毕');
    }
}
