<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\models\Major;
use yii\console\Response;
use yii\db\Expression;

class BaseMajor extends Major
{
    /** @var int 等级 */
    const MAJOR_LEVEL_ONE = 1;
    const MAJOR_LEVEL_TWO = 2;

    const ADD_UNLIMITED_YES = 1;
    const ADD_UNLIMITED_NO  = 0;

    const HIGHEST_LEVEL = 3;

    const LIMIT_NUM = 10;

    const HIDE_MAJOR_ID = [
        87,
        519,
        520,
        521,
        522,
        523,
        524,
        525,
        526,
        527,
        528,
        529,
    ];

    /**
     * 根据id获取名称
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    public static function getMajorName($id)
    {
        $info = Cache::hGet(Cache::ALL_TABLE_MAJOR_KEY, $id);
        if (empty($info)) {
            $info = self::find()
                ->where([
                    'id'     => $id,
                    'status' => self::STATUS_ACTIVE,
                ])
                ->select('id,name,level,parent_id,code')
                ->asArray()
                ->one();
            Cache::hSet(Cache::ALL_TABLE_MAJOR_KEY, $id, json_encode($info));
        } else {
            $info = json_decode($info, true);
        }

        return ArrayHelper::getValue($info, 'name');
        //        $info = self::find()
        //            ->where(['id' => $id])
        //            ->select('name')
        //            ->asArray()
        //            ->one();

        //        return ArrayHelper::getValue($info, 'name');
    }

    /**
     * 多个id，获取名称字符串
     * @param $ids
     * @return mixed
     * @throws \Exception
     */
    public static function getAllMajorName($ids)
    {
        //        返回结构 ['id1'=>'value1','id2'=>'false','id3'=>'value3']
        // $data = Cache::hMGet(Cache::ALL_TABLE_MAJOR_KEY, $ids);
        //
        // //foreach检查是否所有都拿到缓存
        // $new_data = [];
        // foreach ($data as $key => $value) {
        //     if (empty($value) || $value == false) {
        //         //补一下缓存
        //         $info = self::find()
        //             ->where([
        //                 'id' => $key,
        //             ])
        //             ->select('id,name,level,parent_id,code')
        //             ->asArray()
        //             ->one();
        //         Cache::hSet(Cache::ALL_TABLE_MAJOR_KEY, $key, json_encode($info));
        //         //重新赋值
        //         $new_data[$key] = $info;
        //     } else {
        //         $new_data[$key] = $value;
        //     }
        // }
        //
        // return empty($new_data) ? '' : implode(',', array_column($new_data, 'name'));

        $info = self::find()
            ->where([
                'in',
                'id',
                $ids,
            ])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->select('name')
            ->asArray()
            ->all();

        $majorName = '';
        foreach ($info as $key => $item) {
            $majorName .= $item['name'] . ',';
        }

        return empty($majorName) ? $majorName : substr($majorName, 0, -1);
    }

    /**
     * 多个id，获取名称字符串
     * @param $ids
     * @return mixed
     * @throws \Exception
     */
    public static function getAllMajorNameRedis($ids)
    {
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }
        //        返回结构 ['id1'=>'value1','id2'=>'false','id3'=>'value3']
        $data = Cache::hMGet(Cache::ALL_TABLE_MAJOR_KEY, $ids);
        //可以下返回值中是否有null
        $nullIds = array_keys($data, null);
        if (count($nullIds) > 0) {
            foreach ($nullIds as $id) {
                //补一下缓存
                $info = self::find()
                    ->where([
                        'id' => $id,
                    ])
                    ->select('id,name,level,parent_id,code')
                    ->asArray()
                    ->one();
                Cache::hSet(Cache::ALL_TABLE_MAJOR_KEY, $id, json_encode($info));
            }
            $data = Cache::hMGet(Cache::ALL_TABLE_MAJOR_KEY, $ids);
        }

        return empty($data) ? '' : implode(',', array_column($data, 'name'));
    }

    /**
     * 获取二级学科id---这个目前应用位置不多，暂时不做缓存
     * @param $code
     * @return array
     */
    public static function getLevel2MajorById($code)
    {
        $level1Id = BaseMajor::find()
            ->select('id')
            ->where([
                'code'   => $code,
                'status' => self::STATUS_ACTIVE,
                'level'  => 1,
            ])
            ->asArray()
            ->one();

        $level2Ids = BaseMajor::find()
            ->select('id')
            ->where([
                'parent_id' => $level1Id,
                'status'    => self::STATUS_ACTIVE,
                'level'     => 2,
            ])
            ->asArray()
            ->all();

        $majorId = [];
        foreach ($level2Ids as $item) {
            $majorId[] = $item['id'];
        }

        return $majorId;
    }

    /**
     * 获取专业列表，包括子级（这里只有二级，筛选使用，id，name模式）
     * @return array
     */
    public static function getAllListByLevel2()
    {
        // 先拿出第一级
        $parentList = self::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                'parent_id' => 0,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->orderBy('sort desc,id')
            ->asArray()
            ->all();

        $list = [];
        foreach ($parentList as $item) {
            $list[] = [
                'k'        => $item['id'],
                'v'        => $item['name'],
                'children' => self::find()
                    ->select([
                        'k' => 'id',
                        'v' => 'name',
                    ])
                    ->where([
                        'parent_id' => $item['id'],
                        'status'    => self::STATUS_ACTIVE,
                    ])
                    ->orderBy('sort desc,id')
                    ->asArray()
                    ->all(),
            ];
        }

        return $list;
    }

    public static function getAllListByLevel3()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'level',
        ];

        $list      = self::find()
            ->select($select)
            ->where([
                'status' => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();
        $majorList = ArrayHelper::objMoreArr($list);

        return $majorList;
    }

    public static function getPersonMajorListByLevel3()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'level',
        ];

        $list = self::find()
            ->select($select)
            ->where([
                'status' => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();
        foreach ($list as $k => $v) {
            if ($v['v'] == '专业不限') {
                unset($list[$k]);
            }
        }
        $majorList = ArrayHelper::objMoreArr($list);

        return $majorList;
    }

    public static function getAllForJobSelect()
    {
        // 先拿出第一级
        $parentList = self::find()
            ->select([
                'id',
                'name',
                'parent_id',
            ])
            ->where([
                'parent_id' => 0,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->orderBy('sort desc,id')
            ->asArray()
            ->all();

        $list = [];
        foreach ($parentList as $item) {
            $children = array_merge([
                [
                    'k'           => $item['id'],
                    'v'           => $item['name'] . '（全部）',
                    'parentId'    => $item['parent_id'],
                    'topParentId' => $item['id'],
                ],
            ], self::find()
                ->select([
                    'k'           => 'id',
                    'v'           => 'name',
                    'parentId'    => 'parent_id',
                    'topParentId' => 'parent_id',
                ])
                ->where([
                    'parent_id' => $item['id'],
                    'status'    => self::STATUS_ACTIVE,
                ])
                ->orderBy('sort desc,id')
                ->asArray()
                ->all());
            $list[]   = [
                'k'        => $item['id'],
                'v'        => $item['name'],
                'parentId' => $item['parent_id'],
                'children' => $children,
            ];
        }

        return $list;
    }

    /**
     * 获取前两级所有的专业（不包含子级，k，v模式）
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getLevel2MajorList()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'code',
        ];

        return self::find()
            ->select($select)
            ->where([
                'status' => 1,
                'level'  => [
                    1,
                    2,
                ],
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取所有的专业（不包含子级）
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getAllMajorList()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'code',
            'level',
        ];

        return self::find()
            ->select($select)
            ->where(['status' => 1])
            ->asArray()
            ->all();
    }

    /**
     * 获取求职者可选专业（不包含专业不限）
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getPersonMajorList()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'code',
            'level',
        ];

        $list = self::find()
            ->select($select)
            ->where(['status' => 1])
            ->asArray()
            ->all();
        foreach ($list as $k => $v) {
            if ($v['v'] == '专业不限') {
                unset($list[$k]);
            }
        }

        return $list;
    }

    /**
     * 获取专业列表（供搜索用，因为需跳转上级，做了特殊处理）
     * @param int $addUnlimited
     * @return array
     */
    public static function getMajorList($parentId = '', int $addUnlimited = self::ADD_UNLIMITED_NO, $popover = 2): array
    {
        //判断当前id是否是最后一级
        if (!empty($parentId) && $popover == 2) {
            $parentInfo = self::find()
                ->where(['id' => $parentId])
                ->andWhere(['status' => 1])
                ->select('level,parent_id')
                ->asArray()
                ->one();
            if ($parentInfo['level'] == 2) {
                //最后一级了
                $andFilterWhere = ['parent_id' => $parentInfo['parent_id']];
            } else {
                $andFilterWhere = ['parent_id' => $parentId];
            }
        } else {
            $andFilterWhere = ['parent_id' => 0];
        }

        $select = [
            'id',
            'name',
            'parent_id as parentId',
        ];

        $list = self::find()
            ->where([
                'in',
                'level',
                [
                    1,
                    2,
                ],
            ])
            ->andWhere(['status' => 1])
            ->andFilterWhere($andFilterWhere)
            ->select($select)
            ->asArray()
            ->indexBy('id')
            ->all();

        if ($addUnlimited == self::ADD_UNLIMITED_YES) {
            array_unshift($list, [
                'id'   => 0,
                'name' => '不限',
            ]);
        }

        return $list;
    }

    /**
     * 获取搜索列表上级的id（由于选中最后一级的参数后，搜索id为最后一级的，因此要往上跳2级，才能显示正确的列表）
     * @param $currentId
     * @return mixed
     */
    public static function getCurrentSearchParentId($currentId)
    {
        $currentInfo = self::find()
            ->where(['id' => $currentId])
            ->andWhere(['status' => 1])
            ->select([
                'id',
                'level',
                'parent_id',
            ])
            ->asArray()
            ->one();
        //判断当前id的等级
        if ($currentInfo['level'] == 2) {
            $parentInfo = self::find()
                ->where(['id' => $currentInfo['parent_id']])
                ->select('parent_id')
                ->asArray()
                ->one();
            $parentId   = $parentInfo['parent_id'];
        } else {
            $parentId = $currentInfo['parent_id'];
        }

        return $parentId;
    }

    /**
     * 多个id，获取名称字符串带栏目链接
     * @param $ids
     * @return mixed
     * @throws \Exception
     */
    public static function getAllMajorLinkList($ids)
    {
        $info = self::find()
            ->where([
                'in',
                'id',
                $ids,
            ])
            ->andWhere(['status' => 1])
            ->select([
                'name',
                'id',
            ])
            ->asArray()
            ->all();

        $count = sizeof($info);
        $list  = [];

        foreach ($info as $k => $item) {
            if ($k + 1 < $count) {
                $list[$k]['majorText'] = $item['name'] . ',';
            } else {
                $list[$k]['majorText'] = $item['name'];
            }
            $list[$k]['url'] = BaseHomeColumnDictionaryRelationship::getUrl(BaseHomeColumnDictionaryRelationship::TYPE_MAJOR,
                $item['id']);
        }

        return $list;
    }

    /**
     * 获取搜索列表上级的id，多选
     * @param $currentId
     * @return mixed
     */
    public static function getCurrentSearchListParentId($currentId)
    {
        $currentList = self::find()
            ->where(['id' => $currentId])
            ->andWhere(['status' => 1])
            ->select([
                'id',
                'level',
                'parent_id',
            ])
            ->asArray()
            ->all();
        //判断当前id的等级
        $parentIdList = [];
        foreach ($currentList as $currentInfo) {
            if ($currentInfo['level'] == 2) {
                $parentIdList[] = $currentInfo['parent_id'];
            } else {
                $parentIdList[] = $currentInfo['id'];
            }
        }

        return $parentIdList;
    }

    public static function aiRecognition($text)
    {
        // 找到全部学科来缓存?
        $list = self::find()
            ->select([
                'id',
                'name',
                'level',
            ])
            ->where(['status' => 1])
            ->asArray()
            ->all();

        $data = [];
        foreach ($list as $item) {
            $data[$item['name']][] = [
                'id'    => $item['id'],
                'level' => $item['level'],
                'name'  => $item['name'],
            ];
        }

        // 文本里面匹配上面的key
        $result = [];
        foreach ($data as $key => $item) {
            if (strpos($text, $key) !== false) {
                // 如果是一级,看匹配?
                $result[] = $item;
            }
        }

        return $result;
    }

    /**
     * @param $ids
     * 根据专业去找全部3级的id
     */
    public static function getAllLevel3($ids)
    {
        $idsArray = explode(',', $ids);
        // 按照id排序一下
        sort($idsArray, SORT_NUMERIC);

        $key = Cache::ALL_MAJOR_SEARCH_IDS_TO_LEVEL3_KEY . ':' . implode('_', $idsArray);

        $result = Cache::get($key);
        if ($result) {
            return json_decode($result, true);
        }

        $list = self::find()
            ->where(['id' => $idsArray])
            ->andWhere(['status' => 1])
            ->select([
                'id',
                'name',
                'level',
            ])
            ->asArray()
            ->all();

        $level3List = [];
        $level2List = [];
        $level1List = [];
        foreach ($list as $item) {
            if ($item['level'] == 1) {
                $level1List[] = $item['id'];
            } elseif ($item['level'] == 2) {
                $level2List[] = $item['id'];
            } elseif ($item['level'] == 3) {
                $level3List[] = $item['id'];
            }
        }

        // 找level2对应的level3
        if ($level2List) {
            $list       = self::find()
                ->where(['parent_id' => $level2List])
                ->andWhere(['status' => 1])
                ->select([
                    'id',
                    'name',
                    'level',
                ])
                ->asArray()
                ->all();
            $ids        = ArrayHelper::getColumn($list, 'id');
            $level3List = array_merge($level3List, $ids);
        }

        if ($level1List) {
            $level2      = self::find()
                ->where(['parent_id' => $level1List])
                ->andWhere(['status' => 1])
                ->select([
                    'id',
                    'name',
                    'level',
                ])
                ->asArray()
                ->all();
            $level2Array = ArrayHelper::getColumn($level2, 'id');

            $list = self::find()
                ->where(['parent_id' => $level2Array])
                ->andWhere(['status' => 1])
                ->select([
                    'id',
                    'name',
                    'level',
                ])
                ->asArray()
                ->all();

            $ids = ArrayHelper::getColumn($list, 'id');

            $level3List = array_merge($level3List, $ids);
        }

        sort($level3List, SORT_NUMERIC);

        $json = json_encode($level3List);
        // 保留一天就可以了
        Cache::set($key, $json, 86400);

        return $level3List;
    }

    /**
     * 获取稀缺学科id列表
     * @return array
     */
    public static function getRareMajorList()
    {
        $configList = \Yii::$app->params['rareMajor'];
        //        $allMajor   = [];
        //        foreach ($configList as $major) {
        //            array_push($allMajor, $major['id']);
        //            if ($major['level'] == 2) {
        //                //还需要查下一级的ids
        //                $childList = self::find()
        //                    ->where(['parent_id' => $major['id']])
        //                    ->select('id')
        //                    ->asArray()
        //                    ->all();
        //                $allMajor  = array_merge($allMajor, array_column($childList, 'id'));
        //            }
        //        }
        $level2Arr = array_column($configList, 'id');
        //获取二级学科下的三级学科id
        $level3Arr = self::find()
            ->where([
                'in',
                'parent_id',
                $level2Arr,
            ])
            ->andWhere(['status' => 1])
            ->select('id')
            ->asArray()
            ->column();
        $allMajor  = array_merge($level2Arr, $level3Arr);

        //做下去重，给的数据三级可能存在二级下
        return array_unique($allMajor);
    }

    /**
     * 获取单个上级的id
     * @param $currentId
     * @return mixed
     */
    public static function getOneParentId($currentId)
    {
        $currentInfo = self::find()
            ->where(['id' => $currentId])
            ->andWhere(['status' => 1])
            ->select([
                'id',
                'level',
                'parent_id',
            ])
            ->asArray()
            ->one();
        //判断当前id的等级
        if ($currentInfo['level'] == 2) {
            $parentId = $currentId;
        } else {
            $parentId = $currentInfo['parent_id'];
        }

        return $parentId;
    }

    /**
     * 获取专业列表--二三级同层次
     * @return Response|\yii\web\Response
     */
    public static function getHierarchyMajorList()
    {
        $list = ArrayHelper::objMoreArr(BaseMajor::getAllMajorList());

        foreach ($list as $k => $v) {
            $children = $v['children'];
            foreach ($children as &$item) {
                foreach ($item['children'] as $kk => $vv) {
                    $item['children'][$kk]['topParentId'] = $item['parentId'];
                }
            }
            //这里做分组
            $childrenHierarchyList = array_chunk($children, 4);
            $list[$k]['children']  = ArrayHelper::arrayAddNneDimensional($childrenHierarchyList, $v['k']);
        }

        return $list;
    }

    /**
     * 获取专业三级数据
     * @param string|array $levelTwoIds
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getLevelThreeList($levelTwoIds = '')
    {
        $query = self::find()
            ->select([
                'id',
                'name',
                'level',
                'code',
                'sort',
            ])
            ->andWhere([
                'level'  => 3,
                'status' => self::STATUS_ACTIVE,
            ]);
        if (!empty($levelTwoIds)) {
            $query->andWhere(['parent_id' => $levelTwoIds]);
        }

        return $query->asArray()
            ->all();
    }

    public static function getTop($id)
    {
        $major = self::find()
            ->select([
                'id',
                'parent_id',
                'level',
                'name',
            ])
            ->where(['id' => $id])
            ->andWhere(['status' => 1])
            ->asArray()
            ->one();

        if ($major['level'] == 1) {
            return $major;
        } elseif ($major['level'] == 2) {
            return self::find()
                ->select([
                    'id',
                    'parent_id',
                    'level',
                    'name',
                ])
                ->where(['id' => $major['parent_id']])
                ->andWhere(['status' => 1])
                ->asArray()
                ->one();
        } elseif ($major['level'] == 3) {
            $level2 = self::find()
                ->select([
                    'id',
                    'parent_id',
                    'level',
                ])
                ->where(['id' => $major['parent_id']])
                ->andWhere(['status' => 1])
                ->asArray()
                ->one();
            $level1 = self::find()
                ->select([
                    'id',
                    'parent_id',
                    'level',
                    'name',
                ])
                ->where(['id' => $level2['parent_id']])
                ->andWhere(['status' => 1])
                ->asArray()
                ->one();

            return $level1;
        }
    }
}