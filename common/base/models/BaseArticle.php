<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\helpers\UUIDHelper;
use common\models\Announcement;
use common\models\Article;
use yii\base\Exception;
use yii\helpers\Url;

class BaseArticle extends Article
{

    const TYPE_ANNOUNCEMENT               = 1;
    const TYPE_NEWS                       = 2;
    const TYPE_DAILY_ANNOUNCEMENT_SUMMARY = 3;

    const IS_DELETE_YES = 1; //是否删除，是
    const IS_DELETE_NO  = 2; //是否删除，否

    const STATUS_ACTIVE = 1;//状态，在线

    //显示状态
    const IS_SHOW_YES  = 1;   //显示
    const IS_SHOW_NO   = 2;   //隐藏
    const IS_SHOW_LIST = [
        self::IS_SHOW_YES => '显示',
        self::IS_SHOW_NO  => '隐藏',
    ];

    // 特色标签
    const ATTRIBUTE_TAG_RETURNEES = 1;
    const ATTRIBUTE_TAG_YEAR      = 2;
    const ATTRIBUTE_TAG_LIST      = [
        self::ATTRIBUTE_TAG_RETURNEES => '高才海外',
        self::ATTRIBUTE_TAG_YEAR      => '年度招聘',
    ];

    // 推荐位
    const ATTRIBUTE_RECOMMEND_INFO       = 1;
    const ATTRIBUTE_RECOMMEND_HOTSPOT    = 2;
    const ATTRIBUTE_RECOMMEND_MARVELLOUS = 3;
    const ATTRIBUTE_RECOMMEND_LIST       = [
        self::ATTRIBUTE_RECOMMEND_INFO       => '推荐信息',
        self::ATTRIBUTE_RECOMMEND_HOTSPOT    => '热点信息',
        self::ATTRIBUTE_RECOMMEND_MARVELLOUS => '精彩文章',
    ];

    //招聘状态
    const STATUS_ONLINE  = 1; //在线
    const STATUS_OFFLINE = 2; //已下线
    const STATUS_STAGING = 3; //- 草稿/等待发布/保存
    const STATUS_DELETE  = 9; //删除

    const STATUS_LIST = [
        self::STATUS_ONLINE  => '在线',
        self::STATUS_OFFLINE => '已下线',
        self::STATUS_STAGING => '待发布',
        self::STATUS_DELETE  => '已删除',
    ];

    // // 因为有一个getAttribute的基础方法,所以这里改名一下
    // public function getAtt($type = 1)
    // {
    //     return $this->hasMany(BaseArticleAttribute::className(), ['article_id' => 'id'])
    //         ->where([
    //             'article.is_delete' => self::IS_DELETE_NO,
    //             'article.status'    => self::STATUS_ONLINE,
    //             'article.is_show'   => self::IS_SHOW_YES,
    //         ]);
    // }

    public static function replaceIllegalStyle($content)
    {
        // $content = str_replace('times=""', '', $content);
        // $content = str_replace('new=""', '', $content);
        // $content = str_replace('roman";=""', '', $content);
        // $content = str_replace('mso-hansi-font-family:"', '', $content);
        // $content = str_replace('roman";mso-hansi-font-family:="" "', '', $content);
        // $content = str_replace('roman""=""', '', $content);
        // $content = str_replace('roman"; ', '', $content);
        // $content = str_replace('roman";mso-fareast-font-family:微软雅黑"', '', $content);
        // $content = str_replace('roman";mso-bidi-font-family:"', '', $content);
        // $content = str_replace('=""', '', $content);

        // 获取span里面的文本
        // preg_match_all('/<span[^>]*([\s\S]*?)<\/span>/i', $content, $matches);

        // 获取style到>的内容
        // preg_match_all('/style[^>]*([\s\S]*?)>/i', $content, $matches);
        // bb($matches);
        // 替换style到>的内容
        // $content = preg_replace('/style[^>]*([\s\S]*?)>/', '>', $content);
        // 去掉空span
        // $content = preg_replace('/<span[^>]*>([\s\S]*?)<\/span>/', '$1', $content);

        // img标签全部加上rich-img的class
        $content = str_replace('<img', '<img class="rich-img"', $content);

        // 把style里面的双引号替换成单引号
        // $content = preg_replace('/style=\".*?\"/', '', $content);
        // bb($content);

        return $content;
    }

    public static function getDetailUrl($id, $type)
    {
        switch ($type) {
            case self::TYPE_ANNOUNCEMENT:
                $realId = BaseAnnouncement::findOneVal(['article_id' => $id], 'id');

                return BaseAnnouncement::getDetailUrl($realId);
            case self::TYPE_NEWS:
                $realId = BaseNews::findOneVal(['article_id' => $id], 'id');

                return BaseNews::getDetailUrl($realId);
            case self::TYPE_DAILY_ANNOUNCEMENT_SUMMARY:
                $realId = BaseDailyAnnouncementSummary::findOneVal(['article_id' => $id], 'id');

                return '/daily/detail/' . $realId . '.html';
        }
    }

    /**
     * 获取栏目名称（面包屑导航）
     * @param $id
     * @return mixed
     * @throws Exception
     */
    public static function getHomeColumnInfo($articleId)
    {
        $articleInfo = self::find()
            ->where(['id' => $articleId])
            ->select([
                'home_column_id',
            ])
            ->asArray()
            ->one();
        $info        = [
            'url'  => Url::toRoute([
                '/home/<USER>',
                'id' => $articleInfo['home_column_id'],
            ]),
            'name' => BaseHomeColumn::findOneVal(['id' => ArrayHelper::getValue($articleInfo, 'home_column_id')],
                'name'),
        ];

        return $info;
    }

    /**
     * 校验公告标题唯一性
     * @param $title
     */
    public static function checkTitleOnly($title, $articleId = 0)
    {
        $title = trim($title);
        if (empty($title)) {
            return false;
        }

        if ($articleId) {
            $data = self::find()
                ->alias('a')
                ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
                ->select('b.id,a.title')
                ->where([
                    'a.title'     => $title,
                    'a.is_delete' => BaseArticle::IS_DELETE_NO,
                    'a.is_show'   => BaseArticle::IS_DELETE_YES,
                ])
                ->andWhere([
                    '<>',
                    'a.id',
                    $articleId,
                ])
                ->asArray()
                ->one();
        } else {
            $data = self::find()
                ->alias('a')
                ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
                ->select('b.id,a.title')
                ->where([
                    'a.title'     => $title,
                    'a.is_delete' => BaseArticle::IS_DELETE_NO,
                    'a.is_show'   => BaseArticle::IS_DELETE_YES,
                ])
                ->one();
        }

        if ($data) {
            return [
                'titleOnly' => true,
                'uid'       => UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $data['id']),
            ];
        }

        return [
            'titleOnly' => false,
            'uid'       => '',
        ];
    }

    /**
     * 文章对应公告
     * @return \yii\db\ActiveQuery
     */
    public function getAnnouncement()
    {
        return $this->hasOne(BaseAnnouncement::class, ['article_id' => 'id'])
            ->select([
                'id',
                'add_time',
                'update_time',
                'audit_status',
                'title',
                'article_id',
                'member_id',
                'company_id',
                'create_type',
                'creator_id',
                'work_area_id',
                'period_date',
                'apply_type',
                'apply_address',
                'read_permissions',
                'major_ids',
                'relation_company_ids',
                'offline_time',
                'status',
                'is_consume_release',
                'offline_type',
                'offline_reason',
                'home_sort',
                'file_ids',
            ]);
    }

    /**
     * 获取栏目名称（面包屑导航）
     * @param $articleId
     * @return mixed
     * @throws \Exception
     */
    public static function getHomeColumnShow($articleId)
    {
        $articleInfo = self::find()
            ->where(['id' => $articleId])
            ->select([
                'home_column_id',
            ])
            ->asArray()
            ->one();
        $info[]      = [
            'url'  => Url::toRoute([
                '/home/<USER>',
                'id' => $articleInfo['home_column_id'],
            ]),
            'name' => BaseHomeColumn::findOneVal(['id' => ArrayHelper::getValue($articleInfo, 'home_column_id')],
                'name'),
        ];

        //这里加个地区二级栏目的过滤

        $articleColumnList = BaseArticleColumn::find()
            ->select(['column_id'])
            ->where([
                'article_id' => $articleId,
            ])
            ->asArray()
            ->all();
        $regionIds         = array_column($articleColumnList, 'column_id');

        $regionList = BaseHomeColumn::find()
            ->select([
                'name',
                'id',
            ])
            ->where([
                'id'                => $regionIds,
                'operate_attribute' => BaseHomeColumn::OPERATE_ATTRIBUTE_AREA_SECOND,
            ])
            ->asArray()
            ->all();

        foreach ($regionList as $item) {
            $info[] = [
                'name' => $item['name'],
                'url'  => Url::toRoute([
                    '/home/<USER>',
                    'id' => $item['id'],
                ]),
            ];
        }

        return $info;
    }
}