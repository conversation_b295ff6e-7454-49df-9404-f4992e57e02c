<?php

namespace common\base\models;

use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\models\HomePosition;
use common\models\Showcase;
use yii\base\Exception;
use yii\db\Expression;
use yii\db\conditions\AndCondition;
use yii;

class BaseShowcase extends Showcase
{
    const STATUS_OFFLINE = 0;      //已下线
    const STATUS_ONLINE  = 1;      //生效中
    const STATUS_WAIT    = 2;      //未生效
    const STATUS_DELETE  = 9;      //已删除
    //广告条数
    const  ADVERT_LIMIT_NUM = 5;

    const CREATOR_TYPE_PLATFORM = 1;      //创建人类型--平台
    const CREATOR_TYPE_SELF     = 2;      //创建人类型--其他

    const TYPE_CLICK = 1; //统计类型，点击
    const TYPE_READ  = 2; //统计类型，阅读

    const IS_PACKAGE_YES = 1; //是否为打包
    const IS_PACKAGE_NO  = 2; //是否为打包

    const STATUS_LIST = [
        self::STATUS_OFFLINE => '已下线',
        self::STATUS_ONLINE  => '生效中',
        self::STATUS_WAIT    => '未生效',
        self::STATUS_DELETE  => '已删除',
    ];

    //广告位类型1付费  2RPO 3异议 4客情 5推广 6高级 7旧链 8免费 9其他
    const TYPE_PAY       = 1;
    const TYPE_RPO       = 2;
    const TYPE_DISSENT   = 3;
    const TYPE_EMO       = 4;
    const TYPE_PROMOTION = 5;
    const TYPE_ADV       = 6;
    const TYPE_OLD       = 7;
    const TYPE_FREE      = 8;
    const TYPE_REST      = 9;
    const TYPE_NAME      = [
        self::TYPE_PAY       => '付费',
        self::TYPE_RPO       => 'RPO',
        self::TYPE_DISSENT   => '异议',
        self::TYPE_EMO       => '客情',
        self::TYPE_PROMOTION => '推广',
        self::TYPE_ADV       => '高级',
        self::TYPE_OLD       => '旧链',
        self::TYPE_FREE      => '免费',
        self::TYPE_REST      => '其他',
    ];

    const IS_SHOW_YES = 1;
    const IS_SHOW_NO  = 2;

    const TARGET_LINK_TYPE_STATION = 1; //小程序--站内指定页面
    const TARGET_LINK_TYPE_WEBPAGE = 2; //小程序--网页
    const TARGET_LINK_TYPE_VIDEO   = 3; //小程序--视频号
    const TARGET_LINK_TYPE_MINI    = 4; //小程序--小程序

    //跳转类型
    const TARGET_LINK_TYPE_LIST = [
        self::TARGET_LINK_TYPE_STATION => '站内指定页面',
        self::TARGET_LINK_TYPE_WEBPAGE => '网页',
        self::TARGET_LINK_TYPE_VIDEO   => '视频号',
        self::TARGET_LINK_TYPE_MINI    => '小程序',
    ];

    const PAGE_LINK_TYPE_JOB_DETAIL          = 1; //页面类型--职位详情
    const PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL = 2; //页面类型--公告详情
    const PAGE_LINK_TYPE_COMPANY_DETAIL      = 3; //页面类型--单位详情
    const PAGE_LINK_TYPE_JOB_SEARCH          = 4; //页面类型--职位搜索结果页
    const PAGE_LINK_TYPE_ANNOUNCEMENT_SEARCH = 5; //页面类型--公告搜索结果页
    const PAGE_LINK_TYPE_COMPANY_SEARCH      = 6; //页面类型--单位搜索结果页
    const PAGE_LINK_TYPE_CHANNEL_MAIN        = 7; //页面类型--视频号主页
    const PAGE_LINK_TYPE_CHANNEL_DETAIL      = 8; //页面类型--视频id
    const PAGE_LINK_TYPE_ACTIVITY_DETAIL     = 9; //页面类型--活动详情
    const PAGE_LINK_TYPE_SPECIAL_DETAIL      = 10;//页面类型--专场详情

    //页面类型
    const PAGE_LINK_TYPE_LIST = [
        self::PAGE_LINK_TYPE_JOB_DETAIL          => '职位详情',
        self::PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL => '公告详情',
        self::PAGE_LINK_TYPE_COMPANY_DETAIL      => '单位详情',
        self::PAGE_LINK_TYPE_JOB_SEARCH          => '职位搜索结果页',
        self::PAGE_LINK_TYPE_ANNOUNCEMENT_SEARCH => '公告搜索结果页',
        self::PAGE_LINK_TYPE_COMPANY_SEARCH      => '单位搜索结果页',
        self::PAGE_LINK_TYPE_CHANNEL_MAIN        => '视频号主页',
        self::PAGE_LINK_TYPE_CHANNEL_DETAIL      => '视频ID',
        self::PAGE_LINK_TYPE_ACTIVITY_DETAIL     => '活动详情',
        self::PAGE_LINK_TYPE_SPECIAL_DETAIL      => '专场详情',
    ];

    public static function getAdvertList()
    {
        //todo：暂时写死
        $home_position_id = 1;
        $list             = self::find()
            ->where(['home_position_id' => $home_position_id])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->select([
                'image_link as imageLink',
                'id',
                'title',
                'image_url as imageUrl',
                'target_link as targetLink',
                'image_url as imageUrl',
            ])
            ->limit(self::ADVERT_LIMIT_NUM)
            ->asArray()
            ->all();
        foreach ($list as $k => &$v) {
            if (!empty($v['imageLink'])) {
                $v['imgUrl'] = $v['imageLink'];
            } else {
                $v['imgUrl'] = FileHelper::getFullUrl($v['imageUrl']);
            }
        }

        return $list;
    }

    /**
     * 创建一条广告
     * @param $data
     * @throws Exception
     */
    public static function createShowcase($data)
    {
        $model                             = new self();
        $model->add_time                   = $data['add_time'];
        $model->status                     = $data['status'];
        $model->is_show                    = $data['is_show'];
        $model->update_time                = $data['update_time'];
        $model->company_name               = $data['company_name'] ?: '';
        $model->title                      = $data['title'] ?: '';
        $model->sub_title                  = $data['sub_title'] ?: '';
        $model->second_title               = $data['second_title'] ?: '';
        $model->image_url                  = $data['image_url'] ?: '';
        $model->image_link                 = $data['image_link'] ?: '';
        $model->image_alt                  = $data['image_alt'] ?: '';
        $model->online_time                = $data['online_time'] ?: '';
        $model->offline_time               = $data['offline_time'] ?: '';
        $model->target_link                = $data['target_link'] ?: '';
        $model->home_position_id           = $data['home_position_id'] ?: '';
        $model->contact_home_position_id   = $data['contact_home_position_id'] ?: '';
        $model->contact_home_position_sort = $data['contact_home_position_sort'] ?: '';
        $model->sort                       = $data['sort'] ?: '';
        $model->describe                   = $data['describe'] ?: '';
        $model->creator_type               = $data['creator_type'] ?: '';
        $model->creator                    = $data['creator'] ?: '';
        $model->creator_id                 = $data['creator_id'] ?: '';
        $model->target_link_type           = $data['target_link_type'] ?: 0;
        $model->page_link_type             = $data['page_link_type'] ?: 0;
        $model->type                       = $data['type_string'] ?: '';
        //2.0添加
        $model->company_id      = $data['company_id'] ?: '';
        $model->other_image_url = $data['other_image_url'] ?: '';

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return $model->id;
    }

    /**
     * 编辑广告
     * @throws Exception
     */
    public static function editInfo($data, $condition)
    {
        $baseShowcase = new self();
        if (!self::updateAll($data, $condition)) {
            throw new Exception($baseShowcase->getFirstErrorsMessage());
        }
    }

    /**
     * 每次某个广告位更新以后,重新进行刷新
     * @param $key
     */
    public static function refreshCache($homePositionId)
    {
        $number = HomePosition::findOneVal(['id' => $homePositionId], 'number');
        if (!$number) {
            return false;
        }

        $config = Yii::$app->params['homePosition']['showcase'];
        $limit  = $config[$number]['count'];
        if (!$limit) {
            return false;
        }

        return self::setByPositionConfig($homePositionId, $number, $limit);
    }

    /**
     * 获取广告原数据
     * @param $key
     * @param $limit
     * @return array|mixed|yii\db\ActiveRecord[]
     */
    public static function getByKey($key, $limit = 0)
    {
        $positionId = HomePosition::findOneVal(['number' => $key], 'id');
        if (!$positionId) {
            return [];
        }

        return self::getByPositionConfig($positionId, $key, $limit);
    }

    /**
     * 广告位数据格式化
     * @param $key
     * @param $limit
     * @return array
     */
    public static function getShowcaseFormat($key, $limit = 0)
    {
        $info = self::getByKey($key, $limit);
        $list = [];
        if (!$info) {
            return $list;
        }
        foreach ($info as $item) {
            $list[] = [
                'image'         => $item['image_url'] ?: '',
                'url'           => $item['url'] ?: '',
                'number'        => $item['number'] ?: '',
                'id'            => $item['id'] ?: '',
                'title'         => $item['title'] ?: '',
                'subTitle'      => $item['sub_title'] ?: '',
                'companyId'     => $item['company_id'] ?: '',
                'secondTitle'   => $item['second_title'] ?: '',
                'otherImageUrl' => $item['other_image_url'] ?: '',
            ];
        }

        return $list;
    }

    /**
     * @param $positionId
     * @param $key
     * @param $limit
     */
    public static function getByPositionConfig($positionId, $key, $limit = 0)
    {
        // 暂时屏蔽掉缓存部分
        $cacheKey = Cache::PC_HOME_POSITION_SHOWCASE_KEY . ':' . $key;
        // 测试阶段先关掉
        $list = Cache::get($cacheKey);
        if ($list) {
            return json_decode($list, true);
        }

        return self::setByPositionConfig($positionId, $key, $limit);
    }

    /**
     * @param $positionId
     * @param $key
     * @param $limit
     */
    public static function setByPositionConfig($positionId, $key, $limit)
    {
        $cacheKey = Cache::PC_HOME_POSITION_SHOWCASE_KEY . ':' . $key;

        $query = self::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
                'a.sub_title',
                'a.second_title',
                'a.image_link',
                'a.image_url',
                'a.image_alt',
                'a.target_link',
                'a.target_link_type',
                'a.page_link_type',
                'b.platform_type',
                'a.other_image_url',
                'a.company_id',
                'a.describe',
                new Expression("'$key' as number"),
            ])
            ->innerJoin(['b' => BaseHomePosition::tableName()], 'a.home_position_id = b.id')
            ->where([
                'a.home_position_id' => $positionId,
                'a.status'           => self::STATUS_ONLINE,
                'a.is_show'          => self::IS_SHOW_YES,
            ])
            ->orderBy('a.sort desc,a.id desc');

        if ($limit) {
            $query = $query->limit($limit);
        }

        $data = $query->asArray()
            ->all();

        foreach ($data as &$showcase) {
            //这里处理下广告图片信息，如果有link，用link，没有的话，用url,需补足url
            $showcase['image_link']       = $showcase['image_link'] ?: FileHelper::getFullUrl($showcase['image_url']);
            $showcase['real_target_link'] = $showcase['target_link'];
            $showcase['image_link']       = $showcase['image_link'] ?: FileHelper::getFullUrl($showcase['image_url']);
            $showcase['other_image_url']  = $showcase['other_image_url'] ?: FileHelper::getFullUrl($showcase['other_image_url']);
            // 小程序的视频号不需要转换
            if ($showcase['target_link_type'] != self::TARGET_LINK_TYPE_VIDEO) {
                if (!empty($showcase['target_link'])) {
                    //由于现在存在空链接的可能性，这里加个判断
                    // $showcase['target_link'] = UrlHelper::fix($showcase['target_link']);
                    $showcase['target_link'] = self::dataToUrl($showcase);
                }
            }

            if ($showcase['company_id']) {
                $showcase['company_name']     = BaseCompany::findOneVal(['id' => $showcase['company_id']], 'full_name');
                $showcase['company_logo_url'] = BaseCompany::findOneVal(['id' => $showcase['company_id']], 'logo_url');
            }

            // 补一个url方便使用
            $showcase['url'] = $showcase['target_link'];
        }

        if (!$data) {
            return [];
        }

        Cache::set($cacheKey, json_encode($data), 300);

        return $data;
    }

    /**
     * 根据广告位的数据所需要的url
     */
    public static function dataToUrl($data)
    {
        $platformType = $data['platform_type'];
        $targetLink   = $data['target_link'];
        $targetType   = $data['target_link_type'];
        $pageLinkType = $data['page_link_type'];

        if ($platformType == BaseHomePosition::PLATFORM_MINI_APP) {
            // 小程序
            switch ($targetType) {
                case self::TARGET_LINK_TYPE_STATION:
                    // 小程序站内url,这里要根据情况来变成小程序的url
                    $url = self::urlToMiniRouter($pageLinkType, $targetLink);
                    break;

                case self::TARGET_LINK_TYPE_WEBPAGE:
                    // 小程序站外url(其实就是自己的h5)
                    $url = $targetLink;
                    break;

                case self::TARGET_LINK_TYPE_VIDEO:
                    // 视频号
                    // 这里还存在一种情况,就是跳转到视频号主页的
                    if ($pageLinkType == self::PAGE_LINK_TYPE_CHANNEL_MAIN) {
                        $url = '';
                    } else {
                        $url = $targetLink;
                    }
                    break;

                case self::TARGET_LINK_TYPE_MINI:
                    // 小程序
                    $url = $targetLink;
                    break;
            }
        } else {
            $url = UrlHelper::fix($targetLink);
        }

        return $url;
    }

    /**
     * @throws Exception
     */
    public static function urlToMiniRouter($type, $url)
    {
        switch ($type) {
            case BaseShowcase::PAGE_LINK_TYPE_JOB_DETAIL:
                if (strlen($url) == 8 && mb_substr($url, 0, 1) == UUIDHelper::TYPE_JOB) {
                    $url = UUIDHelper::decryption($url);
                }
                $url = '/packages/job/detail/index?id=' . $url;
                break;
            case BaseShowcase::PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL:
                if (strlen($url) == 8 && mb_substr($url, 0, 1) == UUIDHelper::TYPE_ANNOUNCEMENT) {
                    $url = UUIDHelper::decryption($url);
                }
                $url = '/packages/announcement/detail/index?id=' . $url;
                break;
            case BaseShowcase::PAGE_LINK_TYPE_COMPANY_DETAIL:
                if (strlen($url) == 8 && mb_substr($url, 0, 1) == UUIDHelper::TYPE_COMPANY) {
                    $url = UUIDHelper::decryption($url);
                }
                $url = '/packages/company/detail/index?id=' . $url;
                break;
            case BaseShowcase::PAGE_LINK_TYPE_JOB_SEARCH:
                // 用问号未分隔符,截取为数组
                $arr = explode('?', $url);
                if ($arr[1]) {
                    // 链接是从url复制下来的,需要解码
                    $arr[1] = urldecode($arr[1]);
                }
                $url = '/packages/search/result/index?searchType=1&' . $arr[1];
                break;
            case BaseShowcase::PAGE_LINK_TYPE_ANNOUNCEMENT_SEARCH:
                $arr = explode('?', $url);
                if ($arr[1]) {
                    // 链接是从url复制下来的,需要解码
                    $arr[1] = urldecode($arr[1]);
                }
                $url = '/packages/search/result/index?searchType=2&' . $arr[1];
                break;
            case BaseShowcase::PAGE_LINK_TYPE_COMPANY_SEARCH:
                $arr = explode('?', $url);
                if ($arr[1]) {
                    // 链接是从url复制下来的,需要解码
                    $arr[1] = urldecode($arr[1]);
                }
                $url = '/packages/search/result/index?searchType=3&' . $arr[1];
                break;
            case BaseShowcase::PAGE_LINK_TYPE_ACTIVITY_DETAIL:
                $url = '/packages/discover/activity/index?id=' . $url;
                break;
            case BaseShowcase::PAGE_LINK_TYPE_SPECIAL_DETAIL:
                $url = '/packages/discover/special/index?id=' . $url;
                break;
        }

        return $url;
    }

    /**
     * 更新单位扩展表isPay字段
     * @return void
     */
    public static function updateCompanyStatIsPay($companyIds = [])
    {
        $companyIds = array_unique(array_filter($companyIds));
        if (!$companyIds) {
            return;
        }

        foreach ($companyIds as $companyId) {
            // 减少赋值的时间
            if (!Cache::setnx(sprintf(Cache::UPDATE_COMPANY_STAT, $companyId))) {
                continue;
            }
            $key = sprintf(Cache::UPDATE_COMPANY_STAT, $companyId);

            Cache::expire($key, 3);
            $where = [
                [
                    '=',
                    's.status',
                    BaseShowcase::STATUS_ONLINE,
                ],
                [
                    '=',
                    's.company_id',
                    $companyId,
                ],
            ];
            // 查询生效中的广告
            $showCaseList = BaseShowcase::find()
                ->alias('s')
                ->select([
                    'h.platform_type',
                    's.type',
                ])
                ->leftJoin(['h' => BaseHomePosition::tableName()], 'h.id = s.home_position_id')
                ->where(new AndCondition($where))
                ->asArray()
                ->all();

            $isPay         = 2; // 存在付费广告
            $isBoshihouPay = 2; // 博士后付费广告

            foreach ($showCaseList as $showCaseInfo) {
                $types = explode(',', $showCaseInfo['type']);

                if (in_array(BaseShowcase::TYPE_PAY, $types)) {
                    $isPay = 1;
                    if ($showCaseInfo['platform_type'] == BaseHomePosition::PLATFORM_BO_SHI_HOU) {
                        $isBoshihouPay = 1;
                        break;
                    }
                }
            }
            $statModel = BaseCompanyStatData::findOne(['company_id' => $companyId]);
            if (!$statModel) {
                $statModel             = new BaseCompanyStatData();
                $statModel->company_id = $companyId;
            }
            $statModel->is_pay          = $isPay;
            $statModel->is_boshihou_pay = $isBoshihouPay;

            $statModel->save();
            //单位下公告
            BaseAnnouncementExtra::updateAll([
                'is_pay'          => $isPay,
                'is_boshihou_pay' => $isBoshihouPay,
            ], ['company_id' => $companyId]);
            //单位下的职位
            BaseJobExtra::updateAll([
                'is_pay'          => $isPay,
                'is_boshihou_pay' => $isBoshihouPay,
            ], ['company_id' => $companyId]);

            Cache::delete($key);
        }
    }

    /**
     * 获取推荐单位数据
     *
     * @param string $key 广告位标识，默认为A1
     * @param int $limit 限制数量，默认为10
     * @return array 格式化后的推荐单位数据
     */
    public static function getRecommendCompanyData($key = 'A1', $limit = 10)
    {
        try {
            // 根据广告位配置获取推荐单位数据
            $showcaseData = self::getByKey($key, $limit);

            $recommendList = [];
            foreach ($showcaseData as $item) {
                $recommendList[] = [
                    'id'          => $item['id'],
                    'title'       => $item['title'],
                    'subTitle'    => $item['sub_title'] ?? '',
                    'image'       => $item['image_url'] ?? '',
                    'url'         => $item['url'] ?? '',
                    'companyId'   => $item['company_id'] ?? '',
                    'companyName' => $item['company_name'] ?? '',
                    'companyLogo' => BaseCompany::getLogoFullUrl($item['company_logo_url'] ?? ''),
                ];
            }

            return $recommendList;
        } catch (\Exception $e) {
            return [];
        }
    }

}