<?php

namespace common\base\models;

use common\helpers\DebugHelper;
use common\models\AdminDownloadTask;
use queue\Producer;

class BaseAdminDownloadTask extends AdminDownloadTask
{

    // 等待执行
    const STATUS_WAIT = 9;
    // 完成执行
    const STATUS_FINISH = 1;
    // 执行中
    const STATUS_IN_EXECUTE = 2;
    // 执行失败
    const STATUS_FAIL = -1;

    const STATUS_LIST = [
        self::STATUS_WAIT       => '等待下载',
        self::STATUS_FINISH     => '完成下载',
        self::STATUS_IN_EXECUTE => '下载中',
        self::STATUS_FAIL       => '下载失败',
    ];

    const TYPE_COMPANY_LIST                = 1;
    const TYPE_FREE_COMPANY_LIST           = 2;
    const TYPE_REGISTRATION_FORM_LIST      = 3;
    const TYPE_REGISTRATION_FORM_FILE_LIST = 4;
    const TYPE_MY_COMPANY_LIST             = 5;
    const TYPE_DELIVERY_INVITE_LIST        = 6;
    const TYPE_ORDER_PERSON_LIST           = 7;
    const TYPE_PERSON_EQUITY_LIST          = 8;
    const TYPE_COOPERATION_JOB_LIST        = 9;
    const TYPE_SLICE_JOB_LIST              = 10;
    const TYPE_STATEMENT_REPORT_BUILDER    = 11;
    const TYPE_PAY_TRANSFORM_LOG           = 12;
    const TYPE_ACTIVITY_FORM_OPTION_SIGN   = 13;
    const TYPE_RESUME_EQUITY_ADMIN_SETTING = 14;
    const TYPE_SEO_HOT_WORD                = 15;
    const TYPE_NEW_RESUME_SHARE            = 16;
    const TYPE_COMPANY_GROUP_LIST          = 17;
    const TYPE_ANNOUNCEMENT_LIST           = 18;

    // 比较特殊的，系统使用，并不是业务需求
    const TYPE_NGINX_LOG = 101;
    // 识别字典
    const TYPE_MAJOR_AI_LOG = 102;
    // 人才识别
    const TYPE_TALENT_EXPORT = 103;

    //Name
    const TYPE_ANNOUNCEMENT_LIST_NAME           = '公告列表';
    const TYPE_COMPANY_LIST_NAME                = '单位列表';
    const TYPE_FREE_COMPANY_LIST_NAME           = '全部免费单位';
    const TYPE_REGISTRATION_FORM_LIST_NAME      = '活动表单报名表';
    const TYPE_REGISTRATION_FORM_FILE_LIST_NAME = '活动表单报名表附件';
    const TYPE_MY_COMPANY_LIST_NAME             = '我的单位信息';
    const TYPE_DELIVERY_INVITE_LIST_NAME        = '投递邀约列表';
    const TYPE_ORDER_PERSON_LIST_NAME           = '求职者订单列表';
    const TYPE_PERSON_EQUITY_LIST_NAME          = '求职者权益明细列表';
    const TYPE_JOB_LIST_NAME                    = '合作单位职位列表';
    const TYPE_SLICE_JOB_LIST_NAME              = '会员查询职位列表';
    const TYPE_STATEMENT_REPORT_BUILDER_NAME    = '报表';
    const TYPE_PAY_TRANSFORM_LOG_NAME           = '用户支付转化日志埋点';
    const TYPE_ACTIVITY_FORM_OPTION_SIGN_NAME   = '活动表单场次签到';
    const TYPE_RESUME_EQUITY_ADMIN_SETTING_NAME = '人才套餐配置';
    const TYPE_SEO_HOT_WORD_NAME                = '热词列表';
    const TYPE_NEW_RESUME_SHARE_NAME            = '分享列表';
    const TYPE_COMPANY_GROUP_LIST_NAME          = '单位群组列表';
    const TYPE_TALENT_EXPORT_NAME               = '人才数据导出';

    // 比较特殊的，系统使用，并不是业务需求
    const TYPE_NGINX_LOG_NAME = 'nginx日志';
    // 识别字典
    const TYPE_MAJOR_AI_LOG_NAME = '识别字典';

    const TYPE_LIST = [
        self::TYPE_ANNOUNCEMENT_LIST           => self::TYPE_ANNOUNCEMENT_LIST_NAME,
        self::TYPE_COMPANY_LIST                => self::TYPE_COMPANY_LIST_NAME,
        self::TYPE_FREE_COMPANY_LIST           => self::TYPE_FREE_COMPANY_LIST_NAME,
        self::TYPE_REGISTRATION_FORM_LIST      => self::TYPE_REGISTRATION_FORM_LIST_NAME,
        self::TYPE_REGISTRATION_FORM_FILE_LIST => self::TYPE_REGISTRATION_FORM_FILE_LIST_NAME,
        self::TYPE_MY_COMPANY_LIST             => self::TYPE_MY_COMPANY_LIST_NAME,
        self::TYPE_DELIVERY_INVITE_LIST        => self::TYPE_DELIVERY_INVITE_LIST_NAME,
        self::TYPE_ORDER_PERSON_LIST           => self::TYPE_ORDER_PERSON_LIST_NAME,
        self::TYPE_PERSON_EQUITY_LIST          => self::TYPE_PERSON_EQUITY_LIST_NAME,
        self::TYPE_COOPERATION_JOB_LIST        => self::TYPE_JOB_LIST_NAME,
        self::TYPE_SLICE_JOB_LIST              => self::TYPE_SLICE_JOB_LIST_NAME,
        self::TYPE_STATEMENT_REPORT_BUILDER    => self::TYPE_STATEMENT_REPORT_BUILDER_NAME,
        self::TYPE_PAY_TRANSFORM_LOG           => self::TYPE_PAY_TRANSFORM_LOG_NAME,
        self::TYPE_ACTIVITY_FORM_OPTION_SIGN   => self::TYPE_ACTIVITY_FORM_OPTION_SIGN_NAME,
        self::TYPE_RESUME_EQUITY_ADMIN_SETTING => self::TYPE_RESUME_EQUITY_ADMIN_SETTING_NAME,
        self::TYPE_SEO_HOT_WORD                => self::TYPE_SEO_HOT_WORD_NAME,
        self::TYPE_NEW_RESUME_SHARE            => self::TYPE_NEW_RESUME_SHARE_NAME,
        self::TYPE_COMPANY_GROUP_LIST          => self::TYPE_COMPANY_GROUP_LIST_NAME,
        self::TYPE_TALENT_EXPORT               => self::TYPE_TALENT_EXPORT_NAME,
        self::TYPE_NGINX_LOG                   => self::TYPE_NGINX_LOG_NAME,
        self::TYPE_MAJOR_AI_LOG                => self::TYPE_MAJOR_AI_LOG_NAME,
    ];

    public static function createCompanyList($adminId)
    {
        $model           = new self();
        $model->type     = self::TYPE_COMPANY_LIST;
        $model->status   = self::STATUS_WAIT;
        $model->admin_id = $adminId;
        $model->save();
    }

    public static function check($admin)
    {
        if (self::find()
                ->where([
                    'admin_id' => $admin,
                    'status'   => self::STATUS_WAIT,
                ])
                ->count() >= 3) {
            return false;
        }
    }

    public static function create($adminId, $type, $params)
    {
        $model           = new BaseAdminDownloadTask();
        $model->type     = $type;
        $model->admin_id = $adminId;
        if ($params) {
            $params        = json_encode($params);
            $model->params = $params;
        }
        $model->status = BaseAdminDownloadTask::STATUS_WAIT;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }
        $id = $model->id;

        Producer::adminDownloadTask($id);
    }

}