<?php

namespace common\base\models;

use common\helpers\DebugHelper;
use common\models\ArticleColumn;
use yii\base\Exception;
use Yii;

class BaseArticleColumn extends ArticleColumn
{
    /**
     * 创建文章栏目id
     * @param int   $articlId
     * @param array $columnIdsArr
     * @return bool
     * @throws Exception
     */
    public static function createColumnId($articlId, $columnIdsArr)
    {
        if ($articlId) {
            $columnAll = self::findAll(['article_id' => $articlId]);
            //如果该公告选择了属性，删除原来的新增后面的
            if ($columnAll) {
                self::deleteAll([
                    'article_id' => $articlId,
                ]);
            }
            //如果有提交副栏目
            if ($columnIdsArr['homeSubColumnIds']) {
                $columnIdStr = $columnIdsArr['homeColumnId'] . ',' . $columnIdsArr['homeSubColumnIds'];
                $columnId    = array_merge(array_unique(explode(',', $columnIdStr)));
            } else {
                $columnId = $columnIdsArr['homeColumnId'];
            }

            if (is_array($columnId)) {
                foreach ($columnId as $v) {
                    $model             = new self();
                    $model->column_id  = $v;
                    $model->article_id = $articlId;
                    $model->add_time   = CUR_DATETIME;
                    if (!$model->save()) {
                        throw new Exception($model->getFirstErrorsMessage());
                    }
                }
            } else {
                $model             = new self();
                $model->column_id  = $columnId;
                $model->article_id = $articlId;
                $model->add_time   = CUR_DATETIME;
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * 很多时候都只是需要title和url和时间,所以在这里封装一下
     */
    public static function getForHomeByColumnId($columnId, $limit = 10)
    {
        $query = self::find()
            ->select('b.id,b.type,b.title,refresh_time')
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->where([
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'a.column_id' => $columnId,
            ])
            ->orderBy('refresh_time desc');
        if ($limit) {
            $query->limit($limit);
        }

        $list = $query->asArray()
            ->all();

        return $list;
    }

    /**
     * 重新建立文章和栏目之间的关系
     * @param $columnIdArr
     * @param $articleId
     */
    public static function rebuild($articleId, $columnIdArr)
    {
        if (!$articleId) {
            throw new Exception('文章id不能为空');
        }
        if (!$columnIdArr) {
            return;
        }
        //去重$columnIdArr
        $columnIdArr = array_unique($columnIdArr);

        $data = [];
        $key  = [
            'add_time',
            'article_id',
            'column_id',
        ];

        // 先删除全部
        BaseArticleColumn::deleteAll(['article_id' => $articleId]);
        // aa($key);
        // bb($data);
        foreach ($columnIdArr as $item) {
            if (BaseArticleColumn::findOne([
                'article_id' => $articleId,
                'column_id'  => $item,
            ])) {
                continue;
            }
            if ($item) {
                $data[] = [
                    CUR_DATETIME,
                    $articleId,
                    $item,
                ];
            }
        }

        // 后批量写入
        Yii::$app->db->createCommand()
            ->batchInsert(BaseArticleColumn::tableName(), $key, $data)
            ->execute();
    }

    /**
     * 获取公告文章的栏目
     * @param $articleId
     * @return false|string
     * @throws Exception
     */
    public static function getArticleColumn($articleId, $connect = ';')
    {
        if (!$articleId) {
            throw new Exception('文章id不能为空');
        }

        $list = self::find()
            ->select('column_id')
            ->where(['article_id' => $articleId])
            ->asArray()
            ->all();

        $columnName = '';
        foreach ($list as $item) {
            $columnName .= BaseHomeColumn::findOneVal([
                    'id' => $item['column_id'],
                ], 'name') . $connect;
        }

        return substr($columnName, 0, -1) ?: '-';
    }

}