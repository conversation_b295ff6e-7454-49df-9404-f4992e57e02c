<?php

namespace common\base\models;

use common\models\CompanyPackageConfig;
use yii\base\Exception;
use yii;

class BaseCompanyPackageConfig extends CompanyPackageConfig
{

    const STATUS_ACTIVE   = 1;
    const STATUS_INACTIVE = 2;

    const COMPANY_ROLE_EXPIRE = -1; //过期会员
    const COMPANY_ROLE_FREE   = 1; //免费会员
    const COMPANY_ROLE_SENIOR = 2; //高级会员
    const COMPANY_ROLE_LIST   = [
        self::COMPANY_ROLE_EXPIRE => '过期会员',
        self::COMPANY_ROLE_FREE   => '免费会员',
        self::COMPANY_ROLE_SENIOR => '高级会员',
    ];

    const IS_POOL_YES = 1; //是否有人才库，有
    const IS_POOL_NO  = 2; //是否有人才库，无

    const SET_DOWNLOAD_AMOUNT = 1;//设置下载点数
    const SET_CHAT_AMOUNT     = 2;//设置直聊点数

    const SET_SMS_AMOUNT = 3; //短信条数

    const SET_OTHER_LIST = [
        self::SET_DOWNLOAD_AMOUNT => '简历下载点数',
        self::SET_CHAT_AMOUNT     => '直聊点数',
        self::SET_SMS_AMOUNT      => '短信条数',
    ];

    /**
     * 创建企业套餐信息
     * @param $data
     * @throws Exception
     */
    public static function createCompanyPackageConfig($data)
    {
        $model                                    = new self();
        $model->add_time                          = $data['add_time'];
        $model->name                              = $data['name'];
        $model->code                              = $data['code'];
        $model->job_amount                        = $data['job_amount'];
        $model->announcement_amount               = $data['announcement_amount'];
        $model->job_refresh_amount                = $data['job_refresh_amount'];
        $model->announcement_refresh_amount       = $data['announcement_refresh_amount'];
        $model->job_refresh_interval_day          = $data['job_refresh_interval_day'];
        $model->announcement_refresh_interval_day = $data['announcement_refresh_interval_day'];
        $model->announcement_release_interval_day = $data['announcement_release_interval_day'];
        $model->job_release_interval_day          = $data['job_release_interval_day'];
        $model->member_id                         = $data['member_id'];
        $model->company_id                        = $data['company_id'];
        $model->package_amount                    = $data['package_amount'];
        $model->expire_time                       = $data['expire_time'];
        $model->effect_time                       = $data['effect_time'];
        $model->package_id                        = $data['package_id'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    public static function setCompanyRoleMess($companyId)
    {
        $data                = self::getCompanyRoleMess($companyId);
        $model               = BaseCompany::findOne($companyId);
        $model->package_type = $data['company_role'];
        $model->save();
    }

    /**
     * 获取单位会员信息
     */
    public static function getCompanyRoleMess($companyId): array
    {
        $list = BaseCompanyPackageConfig::find()
            ->select([
                'code',
                'expire_time',
                'package_id'
            ])
            ->where([
                'company_id' => $companyId,
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one();

        if (!$list) {
            $effectTime  = BaseCompany::findOneVal(['id' => $companyId], 'add_time');
            $expireTime  = date('Y-m-d h-i-s', (strtotime($effectTime) + 3600 * 24 * 180));
            $packageType = BaseCompany::PACKAGE_TYPE_FREE;
        } else {
            $packageTypeInfo = BaseCompanyPackageSystemConfig::getPackageType($list['package_id']);
            $packageType = $packageTypeInfo['packageType'];
            $expireTime = $list['expire_time'];
        }

        if (strtotime($expireTime) < time()) {
            $packageType = BaseCompany::PACKAGE_TYPE_OVER;
        }

        return [
            'package_type'      => $packageType,
            'package_type_name' => BaseCompany::PACKAGE_TYPE_LIST[$packageType],
            'expire_time'       => $list['expire_time'],
        ];
    }

    /**
     * 获取企业套餐信息
     * @throws Exception
     */
    public static function getCompanyPackageConfig($companyId)
    {
        $companyPackageConfig = BaseCompanyPackageConfig::find()
            ->select([
                'id',
                'add_time',
                'status',
                'name',
                'code',
                'job_amount',
                'announcement_amount',
                'job_refresh_amount',
                'announcement_refresh_amount',
                'job_refresh_interval_day',
                'announcement_refresh_interval_day',
                'resume_download_amount',
                'job_release_interval_day',
                'package_amount',
                'expire_time',
                'effect_time',
                'package_id',
                'chat_amount',
                'sms_amount',
            ])
            ->where([
                'company_id' => $companyId,
                'status'     => 1,
            ])
            ->asArray()
            ->one();

        if ($companyPackageConfig) {
            $companyRole = 1;
            if ($companyPackageConfig['package_id'] > 0) {
                $companyRole = 2;
                if (strtotime($companyPackageConfig['expire_time']) < time()) {
                    $companyRole = -1;
                }
            }

            //当前角色
            switch ($companyRole) {
                case 1:
                    $companyRoleName = '免费会员';
                    break;
                case 2:
                    $companyRoleName = '高级会员';
                    break;
                case -1:
                    $companyRoleName = '过期会员';
                    break;
                default:
                    $companyRoleName = '免费会员';
            }

            $companyPackageConfig['companyRole']     = $companyRole;
            $companyPackageConfig['companyRoleName'] = $companyRoleName;

            return $companyPackageConfig;
        } else {
            //todo 这里企业之前未配置权益，获取免费会员配置 --另外给单位配置免费会员的权益
            //            try {
            //                self::setCurrencyCompanyPackageConfig($companyId);
            //            } catch (Exception $e) {
            //                throw new Exception($e->getMessage());
            //            }
            //            //递归
            //            self::getCompanyPackageConfig($companyId);
            return [];
        }
    }

    /**
     * 获取企业套餐信息
     * @throws Exception
     */
    public static function checkJobRefresh($companyId, $amount)
    {
        // 首先找到该企业的套餐
        $config = self::findOne([
            'company_id' => $companyId,
            'status'     => self::STATUS_ACTIVE,
        ]);

        if (!$config) {
            throw new \Exception('套餐不存在');
        }

        if ($config->effect_time > CUR_DATETIME) {
            throw new Exception('套餐还没生效');
        }

        if ($config->expire_time < CUR_DATETIME) {
            throw new Exception('套餐已过期');
        }

        if ($amount > $config->job_refresh_amount) {
            throw new Exception('您的刷新次数已达上限');
        }
    }

    public static function consumptionJobRefresh($companyId, $amount)
    {
        // 首先找到该企业的套餐
        $config = self::findOne([
            'company_id' => $companyId,
            'status'     => self::STATUS_ACTIVE,
        ]);

        $companyModel = BaseCompany::findOne(['id' => $companyId]);
        $memberId     = $companyModel->member_id;
        $memberModel  = BaseMember::findOne($memberId);

        if (!$config) {
            throw new \Exception('套餐不存在');
        }

        if ($config->effect_time > CUR_DATETIME) {
            throw new Exception('套餐还没生效');
        }

        if ($config->expire_time < CUR_DATETIME) {
            throw new Exception('套餐已过期');
        }

        $oldAmount = $config->job_refresh_amount;
        // 扣除发布次数
        $config->updateCounters([
            'job_refresh_amount' => -$amount,
        ]);
        $type              = BaseCompanyPackageChangeLog::TYPE_JOB_REFRESH;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $config->job_amount,
            $packageConfigName['announcement_amount']         => $config->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $config->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $config->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'change_amount'   => 1,
            'surplus'         => $config->job_refresh_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $memberId,
            'member_name'     => $memberModel->username ?: '',
            'company_id'      => $companyModel->id ?: '',
            'company_name'    => $companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $config->job_refresh_amount . '',
            'handler_type'    => BaseCompanyPackageChangeLog::HANDLER_TYPE_PERSON,
            'handler_id'      => $memberId,
            'handler'         => $companyModel->full_name ?: '',
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
    }

    /**
     * 配置企业免费会员信息
     * @throws Exception
     */
    public static function setCurrencyCompanyPackageConfig($companyId)
    {
        $companyPackageConfig = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);
        if ($companyPackageConfig) {
            return true;
        }

        //企业信息
        $companyInfo = BaseCompany::find()
            ->where(['id' => $companyId])
            ->select([
                'full_name',
                'member_id',
            ])
            ->asArray()
            ->one();

        //用户信息
        $memberInfo = BaseMember::find()
            ->where(['id' => $companyInfo['member_id']])
            ->select('username')
            ->asArray()
            ->one();

        //管理员（操作人员信息）
        $adminId   = Yii::$app->user->id;
        $adminInfo = BaseAdmin::find()
            ->where(['id' => $adminId])
            ->select('username')
            ->asArray()
            ->one();

        $nowTime                   = CUR_DATETIME;
        $jobAmount                 = BaseCompanyPackageSystemConfig::JOB_AMOUNT_SYSTEM;
        $announcementAmount        = BaseCompanyPackageSystemConfig::ANNOUNCEMENT_AMOUNT_SYSTEM;
        $jobRefreshAmount          = BaseCompanyPackageSystemConfig::JOB_REFRESH_AMOUNT_SYSTEM;
        $announcementRefreshAmount = BaseCompanyPackageSystemConfig::ANNOUNCEMENT_REFRESH_AMOUNT_SYSTEM;
        $packageAmountSystem       = BaseCompanyPackageSystemConfig::PACKAGE_AMOUNT_SYSTEM;
        $expireTime                = date('Y-m-d H:i:s',
            strtotime($nowTime) + BaseCompanyPackageChangeLog::PACKAGE_UNIT_DAY * $packageAmountSystem * 3600 * 24);
        $packageConfigName         = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus            = [
            $packageConfigName['job_amount']                  => $jobAmount,
            $packageConfigName['announcement_amount']         => $announcementAmount,
            $packageConfigName['job_refresh_amount']          => $jobRefreshAmount,
            $packageConfigName['announcement_refresh_amount'] => $announcementRefreshAmount,
        ];
        $packageSurplus            = json_encode($packageSurplus);

        //todo 企业套餐开通记录数据
        $companyPackageConfigLogData = [
            'add_time'                          => $nowTime,
            'name'                              => '免费会员',
            'code'                              => '',
            'job_amount'                        => $jobAmount,
            'announcement_amount'               => $announcementAmount,
            'job_refresh_amount'                => $jobRefreshAmount,
            'announcement_refresh_amount'       => $announcementRefreshAmount,
            'job_refresh_interval_day'          => BaseCompanyPackageChangeLog::JOB_REFRESH_INTERVAL_DAY,
            'announcement_refresh_interval_day' => BaseCompanyPackageChangeLog::ANNOUNCEMENT_REFRESH_INTERVAL_DAY,
            'announcement_release_interval_day' => BaseCompanyPackageChangeLog::ANNOUNCEMENT_RELEASE_INTERVAL_DAY,
            'job_release_interval_day'          => BaseCompanyPackageChangeLog::JOB_RELEASE_INTERVAL_DAY,
            'effect_time'                       => $nowTime,
            'expire_time'                       => $expireTime,
            'package_amount'                    => $packageAmountSystem,
            'price'                             => '0',
            'package_id'                        => '',
            'member_id'                         => $companyInfo['member_id'],
            'company_id'                        => $companyId,
            'company_name'                      => $companyInfo['full_name'],
            'handle_before'                     => '未配置',
            'handle_after'                      => '免费会员',
            'handler'                           => $adminInfo['username'],
            'handler_id'                        => $adminId,
            'remark'                            => '系统赠送免费会员',
        ];

        //todo 套餐变更记录--一分四
        $rollingList = [
            [
                'change_amount' => $jobAmount,
                'surplus'       => $jobAmount,
                'type'          => BaseCompanyPackageChangeLog::TYPE_JOB_RELEASE,
                'handle_before' => '0',
                'handle_after'  => (string)$jobAmount,
            ],
            [
                'change_amount' => $announcementAmount,
                'surplus'       => $announcementAmount,
                'type'          => BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_RELEASE,
                'handle_before' => '0',
                'handle_after'  => (string)$announcementAmount,
            ],
            [
                'change_amount' => $jobRefreshAmount,
                'surplus'       => $jobRefreshAmount,
                'type'          => BaseCompanyPackageChangeLog::TYPE_JOB_REFRESH,
                'handle_before' => '0',
                'handle_after'  => (string)$jobRefreshAmount,
            ],
            [
                'change_amount' => $announcementRefreshAmount,
                'surplus'       => $announcementRefreshAmount,
                'type'          => BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REFRESH,
                'handle_before' => '0',
                'handle_after'  => (string)$announcementRefreshAmount,
            ],
        ];

        //todo 企业套餐变更数据
        $companyPackageChangeLogData = [
            'add_time'        => $nowTime,
            'type'            => BaseCompanyPackageChangeLog::TYPE_SYSTEM_CONFIG,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_ADD,
            'package_surplus' => $packageSurplus,
            'member_id'       => $companyInfo['member_id'],
            'member_name'     => $memberInfo['username'],
            'company_id'      => $companyId,
            'company_name'    => $companyInfo['full_name'],
            'handler_type'    => BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM,
            'handler'         => $adminInfo['username'],
            'handler_id'      => $adminId,
            'content'         => BaseCompanyPackageChangeLog::CONTENT_FREE_TITLE,
            'remark'          => '系统赠送：免费会员',
        ];

        //todo 企业配置套餐数据
        $companyPackageConfigData = [
            'add_time'                          => $nowTime,
            'name'                              => '免费会员',
            'code'                              => '-',
            'job_amount'                        => $jobAmount,
            'announcement_amount'               => $announcementAmount,
            'job_refresh_amount'                => $jobRefreshAmount,
            'announcement_refresh_amount'       => $announcementRefreshAmount,
            'job_refresh_interval_day'          => BaseCompanyPackageChangeLog::JOB_REFRESH_INTERVAL_DAY,
            'announcement_refresh_interval_day' => BaseCompanyPackageChangeLog::ANNOUNCEMENT_REFRESH_INTERVAL_DAY,
            'announcement_release_interval_day' => BaseCompanyPackageChangeLog::ANNOUNCEMENT_RELEASE_INTERVAL_DAY,
            'job_release_interval_day'          => BaseCompanyPackageChangeLog::JOB_RELEASE_INTERVAL_DAY,
            'member_id'                         => $companyInfo['member_id'],
            'company_id'                        => $companyId,
            'package_amount'                    => $packageAmountSystem,
            'expire_time'                       => $expireTime,
            'effect_time'                       => $nowTime,
            'package_id'                        => 0,
        ];

        foreach ($rollingList as $item) {
            $temp = array_merge($companyPackageChangeLogData, $item);
            BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($temp);
        }

        //todo 数据入表 企业套餐开通记录、套餐变更记录、企业套餐
        BaseCompanyPackageConfigLog::createCompanyPackageConfigLog($companyPackageConfigLogData);

        //todo 这里拿单位原先配置
        $companyPackageConfig = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);
        if ($companyPackageConfig) {
            BaseCompanyPackageConfig::updateAll($companyPackageConfigData, ['company_id' => $companyId]);
        } else {
            BaseCompanyPackageConfig::createCompanyPackageConfig($companyPackageConfigData);
        }
    }

    /**
     * 单个更改（消费）企业权益套餐
     * @throws Exception
     */
    public static function changeCompanyPackageConfig($field, $amount, $companyId): bool
    {
        // todo 数据校验
        $checkDate = [
            'job_amount',
            'announcement_amount',
            'job_refresh_amount',
            'announcement_refresh_amount',
        ];
        $dateTime  = CUR_DATETIME;

        $companyPackageConfig = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);

        if (!in_array($field, $checkDate)) {
            throw new Exception('消费类型不存在');
        }
        if (!$companyPackageConfig) {
            throw new Exception('企业当前未配置权益套餐');
        }

        if ($dateTime < $companyPackageConfig['effect_time'] || $dateTime > $companyPackageConfig['expire_time']) {
            throw new Exception('企业当前权益套餐不在有效期内');
        }
        if ($amount > $companyPackageConfig[$field]) {
            $fieldName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME[$field];
            throw new Exception('当前套餐次数不足,', $fieldName . '剩余数量：' . $companyPackageConfig[$field]);
        }

        // todo 修改套餐内容
        $fieldSurplus                 = $companyPackageConfig[$field] - $amount;
        $handleBefore                 = (string)$companyPackageConfig[$field];
        $companyPackageConfig->$field = $fieldSurplus;
        if (!$companyPackageConfig->save()) {
            throw new Exception($companyPackageConfig->getFirstErrorsMessage());
        } else {
            // 消耗套餐记录
            $type        = '';
            $content     = '';
            $memberInfo  = BaseMember::findOne(['id' => $companyPackageConfig['member_id']]);
            $companyInfo = BaseCompany::findOne(['id' => $companyId]);

            switch ($field) {
                case 'job_amount':
                    $type    = BaseCompanyPackageChangeLog::TYPE_JOB_RELEASE;
                    $content = "职位发布消耗：" . $amount . '条';
                    break;
                case 'announcement_amount':
                    $type    = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_RELEASE;
                    $content = "公告发布消耗：" . $amount . '条';
                    break;
                case 'job_refresh_amount':
                    $type    = BaseCompanyPackageChangeLog::TYPE_JOB_REFRESH;
                    $content = "职位刷新消耗：" . $amount . '条';
                    break;
                case 'announcement_refresh_amount':
                    $type    = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REFRESH;
                    $content = "公告刷新消耗：" . $amount . '条';
                    break;
            }

            $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
            $packageSurplus    = [
                $packageConfigName['job_amount']                  => $companyPackageConfig['job_amount'],
                $packageConfigName['announcement_amount']         => $companyPackageConfig['announcement_amount'],
                $packageConfigName['job_refresh_amount']          => $companyPackageConfig['job_refresh_amount'],
                $packageConfigName['announcement_refresh_amount'] => $companyPackageConfig['announcement_refresh_amount'],
            ];
            $packageSurplus    = json_encode($packageSurplus);

            //企业套餐变更数据
            $data = [
                'add_time'        => $dateTime,
                'type'            => $type,
                'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
                'change_amount'   => $amount,
                'surplus'         => $fieldSurplus,
                'package_surplus' => $packageSurplus,
                'member_id'       => $companyPackageConfig['member_id'],
                'member_name'     => $memberInfo['username'],
                'company_id'      => $companyId,
                'company_name'    => $companyInfo['full_name'],
                'handle_before'   => $handleBefore,
                'handle_after'    => (string)$fieldSurplus,
                'handler_type'    => BaseCompanyPackageChangeLog::HANDLER_TYPE_PERSON,
                'handler'         => $memberInfo['username'],
                'handler_id'      => $companyPackageConfig['member_id'],
                'content'         => $content,
                'remark'          => '',
            ];

            BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);

            return true;
        }
    }

    /**
     * 上线单位未配置会员
     * @throws Exception
     */
    public static function companyAllocation()
    {
        //已审核通过的单位未配置免费会员权益的单位(合作单位)
        $list = BaseCompany::find()
            ->alias('c')
            ->leftJoin(['p' => BaseCompanyPackageConfig::tableName()], 'p.company_id=c.id')
            ->select([
                'c.id',
                'count(p.id) as amount',
            ])
            ->where([
                'c.status'         => BaseCompany::STATUS_ACTIVE,
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->groupBy('c.id')
            ->having(' amount<1 ')
            ->asArray()
            ->all();

        foreach ($list as $v) {
            try {
                self::setCurrencyCompanyPackageConfig($v['id']);
            } catch (Exception $e) {
                throw new Exception($e->getMessage());
            }
        }
    }

    /**
     * 获取单位会员套餐配置
     */
    public static function getCompanyPackageConfigInfo($companyId)
    {
        $companyPackageConfig = BaseCompanyPackageConfig::find()
            ->alias('p')
            ->leftJoin(['s' => BaseCompanyPackageSystemConfig::tableName()], 's.id = p.package_id')
            ->select([
                'p.id',
                'p.name',
                'p.add_time',
                'p.job_amount',
                'p.announcement_amount',
                'p.job_refresh_amount',
                'p.announcement_refresh_amount',
                'p.resume_download_amount',
                'p.expire_time',
                'p.effect_time',
                'p.package_id',
                'p.package_amount',
                's.job_amount as job_amount_system',
                's.announcement_amount as announcement_amount_system',
                's.job_refresh_amount as job_refresh_amount_system',
                's.announcement_refresh_amount as announcement_refresh_amount_system',
                's.resume_download_amount as resume_download_amount_system',
                's.base_job_amount',
                's.base_announcement_amount',
                's.base_job_refresh_amount',
                's.base_announcement_refresh_amount',
                's.base_resume_download_amount',
            ])
            ->where(['p.company_id' => $companyId])
            ->orderBy('p.id desc')
            ->asArray()
            ->one();

        $totalPackageAmount             = $companyPackageConfig['package_amount'] ?: 1;
        $totalJobAmount                 = $totalPackageAmount * $companyPackageConfig['job_amount_system'] + $companyPackageConfig['base_job_amount'];
        $totalAnnouncementAmount        = $totalPackageAmount * $companyPackageConfig['announcement_amount_system'] + $companyPackageConfig['base_announcement_amount'];
        $totalJobRefreshAmount          = $totalPackageAmount * $companyPackageConfig['job_refresh_amount_system'] + $companyPackageConfig['base_job_refresh_amount'];
        $totalAnnouncementRefreshAmount = $totalPackageAmount * $companyPackageConfig['announcement_refresh_amount_system'] + $companyPackageConfig['base_announcement_refresh_amount'];
        $totalResumeDownloadAmount      = $totalPackageAmount * $companyPackageConfig['resume_download_amount_system'] + $companyPackageConfig['base_resume_download_amount'];
        //塞进去
        $companyPackageConfig['total_package_amount']              = $totalPackageAmount;
        $companyPackageConfig['total_job_amount']                  = $totalJobAmount;
        $companyPackageConfig['total_announcement_amount']         = $totalAnnouncementAmount;
        $companyPackageConfig['total_job_refresh_amount']          = $totalJobRefreshAmount;
        $companyPackageConfig['total_announcement_refresh_amount'] = $totalAnnouncementRefreshAmount;
        $companyPackageConfig['total_resume_download_amount']      = $totalResumeDownloadAmount;

        return $companyPackageConfig;
    }

    /**
     * 获取单位会员套餐配置
     */
    public static function getCompanyPackageConfigDetail($companyId)
    {
        $companyPackage = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);
        if (!$companyPackage) {
            return [
                'announcement_amount'         => 0,
                'job_amount'                  => 0,
                'job_refresh_amount'          => 0,
                'announcement_refresh_amount' => 0,
                'refresh_amount'              => 0,
                'resume_download_amount'      => 0,
                'invite_source_amount'        => 0,
                'chat_amount'                 => 0,
                'sms_amount'                  => 0,
                'invite_source_amount_text'   => '次/本周',
            ];
        }
        //看看当单位是否有邀约资源配置
        $invite_source_info = BaseCompanyInviteSystemConfig::find()
            ->where([
                'company_id' => $companyId,
                'is_delete'  => BaseCompanyInviteSystemConfig::STATUS_NORMAL,
            ])
            ->one();
        if ($invite_source_info) {
            $time       = BaseCompanyInviteSystemConfig::getTime($invite_source_info['type']);
            $limitCount = $invite_source_info['invite_number'];
        } else {
            $time         = BaseCompanyInviteSystemConfig::getTime(BaseCompanyInviteSystemConfig::TYPE_WEEK);
            $companyModel = BaseCompany::findOne(['id' => $companyId]);
            if ($companyModel->status == BaseCompany::STATUS_ACTIVE) {
                $limitCount = BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_COMPANY_INVITE_COUNT_DAY_KEY);
            } else {
                $limitCount = 0;
            }
        }

        // 检查本周是否超过了邀约次数
        $checkCount             = BaseResumeLibraryInviteLog::find()
            ->andWhere([
                'company_id' => $companyId,
            ])
            ->andWhere([
                'between',
                'add_time',
                $time['start_time'],
                $time['end_time'],
            ])
            ->count();
        $inviteSourceAmount     = $limitCount - $checkCount > 0 ? $limitCount - $checkCount : 0;
        $inviteSourceAmountText = '次/' . $time['name'];
        // 获取单位信息
        $companyInfo = BaseCompany::findOne($companyId);
        if ($companyInfo->package_type != BaseCompany::PACKAGE_TYPE_SENIOR) {
            $inviteSourceAmount     = 0;
            $inviteSourceAmountText = '次/本周';
        }

        return [
            'announcement_amount'         => $companyPackage->announcement_amount,
            'job_amount'                  => $companyPackage->job_amount,
            'job_refresh_amount'          => $companyPackage->job_refresh_amount,
            'announcement_refresh_amount' => $companyPackage->announcement_refresh_amount,
            'refresh_amount'              => $companyPackage->job_refresh_amount + $companyPackage->announcement_refresh_amount,
            'resume_download_amount'      => $companyPackage->resume_download_amount,
            'invite_source_amount'        => $inviteSourceAmount,
            'invite_source_amount_text'   => $inviteSourceAmountText,
            'chat_amount'                 => $companyPackage->chat_amount,
            'sms_amount'                  => $companyPackage->sms_amount,
        ];
    }
}