<?php
namespace common\libs\ColumnAuto;

use admin\models\RuleJob;
use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementEducationRelation;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCommon;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseJobBoshihou;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobColumn;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseShowcase;
use common\base\models\BaseWelfareLabel;
use common\helpers\DebugHelper;
use common\libs\Cache;
use common\models\AnnouncementAutoClassifyLog;
use common\models\Area;
use common\models\CategoryJob;
use common\models\JobAutoClassifyLog;
use common\models\Major;
use common\service\meilisearch\job\AddService;
use queue\MeilisearchJob;
use queue\Producer;
use yii\base\Exception;
use miniApp\models\Job as MiniAppJob;
use yii\db\conditions\AndCondition;

/**
 * 职位自动规则
 *
 * 触发重新计算
 * 所有计算规则必须是在审核通过后在生效（包含系统自动审核通过或者人工审核通过）
 * 单位的变动（类型/性质）
 * 触发下面所有的公告重新计算
 * 公告的变动不会触发重新计算
 * 特色标签不影响
 *  职位的变动
 *      下线不触发计算
 *      上线不触发计算
 *      审核通过出发计算
 *      变更触发重新计算
 *
 * 文档
 * https://docs.qq.com/sheet/DSmNpYUhwYXppWmR5?tab=c5v7aw&u=2dedde74e1b74a758afd3f2fb70948be
 */
class JobAutoClassify extends JobRule
{

    private $announcementId;
    private $jobId;

    /**
     * @var BaseCompany
     */
    private $companyModel;
    /**
     * @var BaseAnnouncement
     */
    private $announcementModel;
    /**
     * @var BaseJob
     */
    private $jobModel;

    /**
     * @var BaseArticle
     */
    private $articleModel;

    private $allColunmList = [];

    // 保存一些常常用到的属性
    private $companyType   = 0;
    private $abroadType    = BaseActiveRecord::BASE_TYPE_NO;
    private $provinceId    = 0;
    private $cityId        = 0;
    private $jobCategoryId = 0;
    private $educationType = 0;
    private $majorList     = [];
    private $columnList    = [];

    private $newColumnList = [];

    private $remark = '';

    const MATH_COMPANY_TYPE              = 1;
    const MATH_COMPANY_TYPE_AND_CITY     = 2;
    const MATH_COMPANY_TYPE_AND_PROVINCE = 3;
    const MATH_COMPANY_TYPE_AND_JOB_TYPE = 4;
    const MATH_CITY                      = 5;
    const MATH_AREA_AND_EDUCATION_ABROAD = 6;
    const MATH_COLUMN                    = 7;
    const MATH_COLUMN_OR_AREA            = 8;
    const MATH_MAJOR                     = 9;
    const MATH_EDUCATION                 = 10;
    const MATH_ABROAD_QIUXIAN            = 11;

    const MATH_TYPE_LIST = [
        self::MATH_COMPANY_TYPE              => '单位类型',
        self::MATH_COMPANY_TYPE_AND_CITY     => '单位类型和地区(城市)',
        self::MATH_COMPANY_TYPE_AND_PROVINCE => '单位类型和地区(省份)',
        self::MATH_COMPANY_TYPE_AND_JOB_TYPE => '单位类型和职位类型',
        self::MATH_CITY                      => '地区(城市)',
        self::MATH_AREA_AND_EDUCATION_ABROAD => '地区(省份)和(学历或海外)',
        self::MATH_COLUMN                    => '栏目',
        self::MATH_COLUMN_OR_AREA            => '栏目或地区',
        self::MATH_MAJOR                     => '学科',
        self::MATH_EDUCATION                 => '学历',
        self::MATH_ABROAD_QIUXIAN            => '海外求贤',
    ];

    /** @var int 博士后分类id */
    const BOSHIHOU_CATEGORY_ID = [
        29,
        263,
    ];

    public function __construct($jobId)
    {
        $this->jobId = $jobId;

        $this->jobModel = BaseJob::findOne($jobId);
        if (!$this->jobModel) {
            throw new Exception('职位不存在');
        }
        $this->announcementId = $this->jobModel->announcement_id;
        if ($this->announcementId) {
            $this->announcementModel = BaseAnnouncement::findOne($this->announcementId);
            if (!$this->announcementModel) {
                throw new Exception('公告不存在');
            }
            $this->articleModel = BaseArticle::findOne($this->announcementModel->article_id);
            if (!$this->articleModel) {
                throw new Exception('公告不存在');
            }
        }

        $this->companyModel = BaseCompany::findOne($this->jobModel->company_id);
        if (!$this->companyModel) {
            throw new Exception('企业不存在');
        }

        $this->companyType   = $this->companyModel->type;
        $this->companyNature = $this->companyModel->nature;
        $this->cityId        = $this->jobModel->city_id;
        $this->provinceId    = $this->jobModel->province_id;
        $this->jobCategoryId = $this->jobModel->job_category_id;
        $this->educationType = $this->jobModel->education_type;
        $this->abroadType    = $this->jobModel->abroad_type;
        $this->majorList     = explode(',', $this->jobModel->major_id);

        // 找文章栏目关联表
        $articleColumnList = BaseArticleColumn::find()
            ->select('column_id')
            ->where(['article_id' => $this->articleModel->id])
            ->asArray()
            ->column();

        $this->columnList = $articleColumnList;

        if ($this->provinceId == self::AREA_ABROAD_ID) {
            // 海外
            $this->abroadType = BaseActiveRecord::BASE_TYPE_YES;
        }

        $cacheColumnList = BaseHomeColumn::getCache();
        foreach ($cacheColumnList as $column) {
            $this->allColunmList[$column['id']] = $column;
        }
    }

    /**
     * 运行
     */
    public function run()
    {
        $this->setCompanyType();
        $this->setCompanyTypeAndCity();
        $this->setCompanyTypeAndProvince();
        $this->setCompanyTypeAndJobType();
        $this->setCity();
        $this->setAreaAndEducationAbroad();
        $this->setColumn();
        $this->setColumnOrArea();
        $this->setMajor();
        $this->setEducation();
        $this->setAbroadQinxian();
        //同步中间表数据---v2注释4行
        //        $this->updateJobWelfareTable();
        //        $this->updateJobMajorTable();
        //        $this->updateJobCategoryTable();
        //        $this->updateJobMiniAppType();
        // $this->updateJobCompanySort();
        //        $this->updateStatInfo();

        //更新公告编制--v2注释1行
        //        $this->updateAnnouncementEstablishment();
        //更新公告中间表
        // $this->updateJobAnnouncementRelation();
        // $this->updateJobAnnouncementAmount();
        // 职位PI更新---v2注释
        //        $this->updateJobPiFlag();
        // 更新博士后职位表---v2注释
        //        $this->updateJobBoshihouTable();

        // 更新职位搜索词----v2注释
        //        $this->updateJobSearchName();
        // 职位详情缓存处理
        // BaseJob::getJobDetail
        // miniApp\models\Job::getDetailService
        BaseJob::getJobDetail($this->jobId, '', true);
        MiniAppJob::getDetailService($this->jobId, BaseCommon::PLATFORM_MINI, 0, true);
        $this->update();
    }

    /**
     * 更新职位附属表的PI标识
     */
    private function updateJobPiFlag()
    {
        try {
            //先看下是否有附属记录
            $jobExtraInfo = BaseJobExtra::findOne(['job_id' => $this->jobModel->id]);
            if (!$jobExtraInfo) {
                //没有就补一下
                BaseJobExtra::insertData([
                    'announcement_id' => $this->jobModel->announcement_id,
                    'company_id'      => $this->jobModel->company_id,
                    'job_id'          => $this->jobModel->id,
                ]);
                $jobExtraInfo = BaseJobExtra::findOne(['job_id' => $this->jobModel->id]);
            }
            //看下自身是否有pi属性
            if (($this->jobModel->announcement_id > 0 && BaseArticleAttribute::find()
                        ->where([
                            'article_id' => $this->announcementModel->article_id,
                            'type'       => BaseArticleAttribute::ATTRIBUTE_PI,
                        ])
                        ->exists()) || BaseCompanyFeatureTagRelation::find()
                    ->where([
                        'company_id'     => $this->jobModel->company_id,
                        'feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                    ])
                    ->exists()) {
                $jobExtraInfo->is_pi = BaseJobExtra::IS_PI_YES;
            } else {
                $jobExtraInfo->is_pi = BaseJobExtra::IS_PI_NO;
            }
            if ($jobExtraInfo->is_pay == BaseJobExtra::IS_PAY_NO || $jobExtraInfo->is_boshihou_pay == BaseJobExtra::IS_PAY_NO) {
                BaseShowcase::updateCompanyStatIsPay([$this->jobModel->company_id]);
            }
            $jobExtraInfo->save();
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * 单位类型匹配
     */
    private function setCompanyType()
    {
        $rule        = self::COMPANY_TYPE_RULE;
        $companyType = $this->companyType;
        if (!$companyType) {
            return;
        }
        if (isset($rule[$companyType])) {
            // 有符合规则的栏目
            $columnId = $rule[$companyType]['columnId'];

            $this->setMath(self::MATH_COMPANY_TYPE, [
                'companyType' => $companyType,
            ], $columnId);
        }
    }

    private function setCompanyTypeAndCity()
    {
        $rule        = self::COMPANY_TYPE_AND_CITY_RULE;
        $companyType = $this->companyType;
        if (!$companyType) {
            return;
        }
        $cityRule = $rule[$this->cityId] ?? [];
        if (!$cityRule) {
            return;
        }
        // 循环cityRule
        foreach ($cityRule['list'] as $ruleItem) {
            $companyTypeList = $ruleItem['companyType'];
            if (in_array($companyType, $companyTypeList)) {
                $columnId = $ruleItem['columnId'];
                $this->setMath(self::MATH_COMPANY_TYPE_AND_CITY, [
                    'companyType' => $companyType,
                    'areaId'      => $this->cityId,
                ], $columnId);
            }
        }
    }

    private function setCompanyTypeAndProvince()
    {
        $rule        = self::COMPANY_TYPE_AND_PROVINCE_RULE;
        $companyType = $this->companyType;
        if (!$companyType) {
            return;
        }
        $provinceRule = $rule[$this->provinceId] ?? [];
        if (!$provinceRule) {
            return;
        }
        // 循环cityRule
        foreach ($provinceRule['list'] as $ruleItem) {
            $companyTypeList = $ruleItem['companyType'];
            if (in_array($companyType, $companyTypeList)) {
                $columnId = $ruleItem['columnId'];
                $this->setMath(self::MATH_COMPANY_TYPE_AND_PROVINCE, [
                    'companyType' => $companyType,
                    'areaId'      => $this->provinceId,
                ], $columnId);
            }
        }
    }

    private function setCompanyTypeAndJobType()
    {
        $rule        = self::COMPANY_TYPE_AND_JOB_TYPE_RULE;
        $companyType = $this->companyType;
        if (!$companyType) {
            return;
        }
        $jobCategoryId = $this->jobCategoryId;

        if (!$jobCategoryId) {
            return;
        }
        // 循环rule
        foreach ($rule as $columnId => $ruleItem) {
            $companyTypeList = $ruleItem['companyType'];
            $categoryJobList = $ruleItem['categoryJob'];
            // 同时符合两个条件的情况下才匹配
            if (in_array($companyType, $companyTypeList) && in_array($jobCategoryId, $categoryJobList)) {
                $this->setMath(self::MATH_COMPANY_TYPE_AND_JOB_TYPE, [
                    'companyType'   => $companyType,
                    'jobCategoryId' => $jobCategoryId,
                ], $columnId);
            }
        }
    }

    private function setCity()
    {
        $rule = self::CITY_RULE;
        // 还是做个保险吧,有可能加省份进去
        $cityId = $this->cityId;
        // 省份
        $provinceId = $this->provinceId;

        if ($cityId) {
            if (isset($rule[$this->cityId])) {
                $columnId = $rule[$this->cityId]['id'];
                $this->setMath(self::MATH_CITY, [
                    'areaId' => $this->cityId,
                ], $columnId);
            }
        }
        if ($provinceId) {
            if (isset($rule[$this->provinceId])) {
                $columnId = $rule[$this->provinceId];
                $this->setMath(self::MATH_CITY, [
                    'areaId' => $this->provinceId,
                ], $columnId);
            }
        }
    }

    private function setAreaAndEducationAbroad()
    {
        // 如果不是海外或者也不是要求博士的职位,直接返回
        if ($this->abroadType != BaseActiveRecord::BASE_TYPE_YES && $this->educationType != self::EDUCATION_DOCTOR_CODE) {
            return;
        }
        $rule = self::AREA_AND_EDUCATION_ABROAD_RULE;
        // 城市
        $cityId = $this->cityId;
        // 省份
        $provinceId = $this->provinceId;

        foreach ($rule as $columnId => $item) {
            $ruleAreaIds = $item['areaId'];
            if (in_array($cityId, $ruleAreaIds)) {
                $this->setMath(self::MATH_AREA_AND_EDUCATION_ABROAD, [
                    'areaId' => $cityId,
                ], $columnId);
            }

            if (in_array($provinceId, $ruleAreaIds)) {
                $this->setMath(self::MATH_AREA_AND_EDUCATION_ABROAD, [
                    'areaId' => $provinceId,
                ], $columnId);
            }
        }
    }

    private function setColumn()
    {
        $rule = self::COLUMN_RULE;
        foreach ($this->columnList as $item) {
            if (in_array($item, $rule)) {
                $this->setMath(self::MATH_COLUMN, [
                    'columnId' => $item,
                ], $item);
            }
        }
    }

    private function setColumnOrArea()
    {
        $rule = self::COLUMN_OR_AREA_RULE;
        foreach ($rule as $columnId => $item) {
            // 海外
            if (in_array($this->provinceId, $item['areaId'])) {
                $this->setMath(self::MATH_COLUMN_OR_AREA, [
                    'areaId' => $this->provinceId,
                ], $columnId);
            }

            // 栏目
            if (in_array($columnId, $this->columnList)) {
                $this->setMath(self::MATH_COLUMN_OR_AREA, [
                    'columnId' => $columnId,
                ], $columnId);
            }
        }
    }

    private function setMajor()
    {
        $rule = self::MAJOR_RULE;
        foreach ($rule as $majorId => $item) {
            if (in_array($majorId, $this->majorList)) {
                $this->setMath(self::MATH_MAJOR, [
                    'majorId' => $majorId,
                ], $item['columnId']);
            }
        }
    }

    private function setEducation()
    {
        $rule = self::EDUCATION_RULE;
        foreach ($rule as $columnId => $item) {
            if (in_array($this->educationType, $item['education'])) {
                $this->setMath(self::MATH_EDUCATION, [
                    'educationId' => $this->educationType,
                ], $columnId);
            }
        }
    }

    private function setAbroadQinxian()
    {
        // 这里是一个比较特殊的操作，需要找公告来处理栏目下的职位
        $announcementId = $this->jobModel->announcement_id;
        $jobId          = $this->jobId;
        if ($announcementId) {
            $key  = Cache::ALL_ABROAD_QIUXIAN_ANNOUNCEMENT_MATCH_JOB_KEY . ':' . $announcementId;
            $data = Cache::get($key);
            if ($data) {
                $data = json_decode($data, true);
                if (in_array($jobId, $data)) {
                    $this->setMath(self::MATH_ABROAD_QIUXIAN, ['announcementId' => $announcementId],
                        self::ABROAD_QIUXIAN_COLUMN_ID);
                }
            }
        }
    }

    private function addColumn($columnId)
    {
        $this->newColumnList[] = $columnId;
    }

    private function setRemark($str)
    {
        $this->remark .= $str . PHP_EOL;
    }

    /**
     * 根据不同的类型获取不同的符合条件的栏目的文案
     * @param $mathRule
     * @param $columnId
     */
    private function setMath($mathRule, $data, $columnId)
    {
        $ruleName   = self::MATH_TYPE_LIST[$mathRule];
        $str        = '符合' . $ruleName . '规则：';
        $columnName = $this->allColunmList[$columnId]['name'];

        switch ($mathRule) {
            case self::MATH_COMPANY_TYPE:
                $companyType     = $data['companyType'];
                $companyTypeName = BaseDictionary::getCompanyTypeName($companyType);
                $str             .= '单位类型为 ' . $companyTypeName . '(' . $companyType . ')';
                break;
            case self::MATH_COMPANY_TYPE_AND_CITY:
            case self::MATH_COMPANY_TYPE_AND_PROVINCE:
                $companyType     = $data['companyType'];
                $companyTypeName = BaseDictionary::getCompanyTypeName($companyType);
                $areaId          = $data['areaId'];
                $areaName        = BaseArea::getAreaName($areaId);
                $str             .= '单位类型为 ' . $companyTypeName . '(' . $companyType . ')' . '，地区为' . $areaName . '(' . $areaId . ')';
                break;
            case self::MATH_COMPANY_TYPE_AND_JOB_TYPE:
                $companyType     = $data['companyType'];
                $companyTypeName = BaseDictionary::getCompanyTypeName($companyType);
                $jobCategoryId   = $data['jobCategoryId'];
                $jobCategoryName = BaseCategoryJob::findOneVal($jobCategoryId, 'name');
                $str             .= '单位类型为 ' . $companyTypeName . '(' . $companyType . ')' . '，职位类别为' . $jobCategoryName . '(' . $jobCategoryId . ')';
                break;
            case self::MATH_CITY:
                $areaId = $data['areaId'];
                $str    .= '地区为' . BaseArea::getAreaName($areaId) . '(' . $areaId . ')';
                break;
            case self::MATH_AREA_AND_EDUCATION_ABROAD:
                $areaId = $data['areaId'];
                if ($this->abroadType == BaseActiveRecord::BASE_TYPE_YES) {
                    $str .= '职位要求为海外，';
                }
                if ($this->educationType == self::EDUCATION_DOCTOR_CODE) {
                    $str .= '职位要求为博士，';
                }
                $str .= '地区为' . BaseArea::getAreaName($areaId) . '(' . $areaId . ')';
                break;
            case self::MATH_COLUMN:
                $columnId = $data['columnId'];
                $str      .= '栏目为' . $this->allColunmList[$columnId]['name'] . '(' . $columnId . ')';
                break;
            case self::MATH_COLUMN_OR_AREA:
                $areaId = $data['areaId'];
                if ($data['areaId']) {
                    $str .= '地区为' . BaseArea::getAreaName($areaId) . '(' . $areaId . ')';
                }
                if ($data['columnId']) {
                    $str .= '栏目为' . $this->allColunmList[$columnId]['name'] . '(' . $columnId . ')';
                }
                break;
            case self::MATH_MAJOR;
                $majorId = $data['majorId'];
                $str     .= '专业为' . BaseMajor::findOneVal(['id' => $majorId], 'name') . '(' . $majorId . ')';
                break;
            case self::MATH_EDUCATION:
                $educationId = $data['educationId'];
                $str         .= '学历为' . BaseDictionary::getEducationName($educationId) . '(' . $educationId . ')';
                break;
            case self::MATH_ABROAD_QIUXIAN:
                $str .= '符合海外求贤规则,公告id为' . $data['announcementId'];
                break;
        }

        $str .= '，分配到栏目 ' . $columnName . '(' . $columnId . ')';
        $this->setRemark($str);
        $this->addColumn($columnId);
    }

    /**
     * 更新职位的专业中间表
     * @return bool
     */
    public function updateJobMajorTable()
    {
        //获取职位的专业
        $jobMajorIdsStr = $this->jobModel->major_id;
        //炸成数组
        $jobMajorIdsArr = explode(',', $jobMajorIdsStr);
        //去重
        $jobMajorIdsArr = array_unique($jobMajorIdsArr);
        //去掉隐藏的
        if (count(array_intersect($jobMajorIdsArr, BaseMajor::HIDE_MAJOR_ID)) > 0) {
            $jobMajorIdsArr           = array_diff($jobMajorIdsArr, BaseMajor::HIDE_MAJOR_ID);
            $this->jobModel->major_id = count($jobMajorIdsArr) > 0 ? implode(',', $jobMajorIdsArr) : '';
            $this->jobModel->save();
        }

        //删除职位的专业中间表数据
        BaseJobMajorRelation::deleteAll(['job_id' => $this->jobId]);

        //写入表中
        foreach ($jobMajorIdsArr as $jobMajorId) {
            $jobMajorInfo                   = BaseJobMajorRelation::findOne([
                'job_id'   => $this->jobId,
                'major_id' => $jobMajorId,
            ]);
            $level                          = BaseMajor::findOneVal(['id' => $jobMajorId], 'level');
            $jobMajorModel                  = $jobMajorInfo ?: new BaseJobMajorRelation();
            $jobMajorModel->announcement_id = $this->jobModel->announcement_id;
            $jobMajorModel->job_id          = $this->jobId;
            $jobMajorModel->major_id        = $jobMajorId;
            $jobMajorModel->level           = $level;
            $jobMajorModel->save();
            if ($level == 2) {
                //获取上级major
                $parent_id   = BaseMajor::findOneVal(['id' => $jobMajorId], 'parent_id');
                $parent_info = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobModel->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            } elseif ($level == 3) {
                //获取上级major
                $parent_id     = BaseMajor::findOneVal(['id' => $jobMajorId], 'parent_id');
                $parent_info_1 = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_id,
                    'level'    => 2,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobModel->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 2;
                    $jobMajorModel->save();
                }
                $parent_parent_id = BaseMajor::findOneVal(['id' => $parent_id], 'parent_id');
                $parent_info_1    = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobModel->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            }
        }

        return true;
    }

    /**
     * 更新职位的类型中间表
     * @return bool
     */
    public function updateJobCategoryTable()
    {
        //获取职位的类型
        $jobCategoryId = $this->jobModel->job_category_id;
        //获取职位的类型信息
        $categoryJobInfo = BaseCategoryJob::findOne(['id' => $jobCategoryId]);
        //删除职位的类型中间表数据
        BaseJobCategoryRelation::deleteAll(['job_id' => $this->jobId]);
        if ($categoryJobInfo->level == 2) {
            $categoryJobParentInfo = BaseCategoryJob::findOne(['id' => $categoryJobInfo->parent_id]);
            //是否存在信息
            $jobCategoryParentInfo             = BaseJobCategoryRelation::findOne([
                'job_id'      => $this->jobId,
                'category_id' => $categoryJobParentInfo->id,
            ]);
            $jobCategoryModel                  = $jobCategoryParentInfo ?: new BaseJobCategoryRelation();
            $jobCategoryModel->announcement_id = $this->jobModel->announcement_id;
            $jobCategoryModel->job_id          = $this->jobId;
            $jobCategoryModel->category_id     = $categoryJobParentInfo->id;
            $jobCategoryModel->level           = $categoryJobParentInfo->level;
            $jobCategoryModel->save();
        }
        //写入自己的信息
        $jobCategoryInfo                   = BaseJobCategoryRelation::findOne([
            'job_id'      => $this->jobId,
            'category_id' => $jobCategoryId,
        ]);
        $jobCategoryModel                  = $jobCategoryInfo ?: new BaseJobCategoryRelation();
        $jobCategoryModel->announcement_id = $this->jobModel->announcement_id;
        $jobCategoryModel->job_id          = $this->jobId;
        $jobCategoryModel->category_id     = $jobCategoryId;
        $jobCategoryModel->level           = $categoryJobInfo->level;
        $jobCategoryModel->save();

        return true;
    }

    /**
     * 更新职位的福利中间表
     * @return bool
     */
    public function updateJobWelfareTable()
    {
        //获取职位的福利
        $jobWelfareIdsStr = $this->jobModel->welfare_tag;
        //炸成数组
        $jobWelfareIdsArr = explode(',', $jobWelfareIdsStr);
        //去重
        $jobWelfareIdsArr = array_unique($jobWelfareIdsArr);
        //删除职位的福利中间表数据
        BaseJobWelfareRelation::deleteAll(['job_id' => $this->jobId]);
        //写入表中
        foreach ($jobWelfareIdsArr as $jobWelfareId) {
            $jobWelfareInfo                   = BaseJobWelfareRelation::findOne([
                'job_id'     => $this->jobId,
                'welfare_id' => $jobWelfareId,
            ]);
            $jobWelfareModel                  = $jobWelfareInfo ?: new BaseJobWelfareRelation();
            $jobWelfareModel->announcement_id = $this->jobModel->announcement_id;
            $jobWelfareModel->job_id          = $this->jobId;
            $jobWelfareModel->welfare_id      = $jobWelfareId;
            $jobWelfareModel->save();
        }

        return true;
    }

    /**
     * 更新职位是否是小程序职位
     * @throws \Exception
     */
    public function updateJobMiniAppType()
    {
        try {
            if ($this->jobModel->is_manual_tag == BaseJob::IS_MANUAL_TAG_NONE) {
                $model      = new RuleJob();
                $res        = $model->exec($this->jobId);
                $is_miniapp = $res ? BaseJob::IS_MINIAPP_YES : BaseJob::IS_MINIAPP_NO;
            } elseif ($this->jobModel->is_manual_tag == BaseJob::IS_MANUAL_TAG_YES) {
                $is_miniapp = BaseJob::IS_MINIAPP_YES;
            }
            if ($this->jobModel->is_miniapp != $is_miniapp) {
                $this->jobModel->is_miniapp = $is_miniapp;
                $this->jobModel->save();
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * 更新公告的在线职位数量与职位总数量
     * 缓存公告招聘人数
     * @return string|void
     */
    public function updateJobAnnouncementAmount()
    {
        //有公告才更新
        if ($this->announcementId > 0) {
            try {
                $announcementModel                    = BaseAnnouncement::findOne($this->announcementId);
                $announcementModel->online_job_amount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->count();
                $announcementModel->all_job_amount    = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => [
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->count();
                $announcementModel->save();

                //缓存公告招聘人数
                $key    = Cache::ALL_ANNOUNCEMENT_JOB_AMOUNT_KEY . ':' . $this->announcementId;
                $amount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                        'amount'          => '若干',
                    ])
                    ->exists() ? '若干' : BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->sum('amount');
                Cache::set($key, $amount);
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        }
    }

    /**
     * 更新职位公告地区、学历中间表
     * @return string|void
     */
    public function updateJobAnnouncementRelation()
    {
        //有公告才更新
        if ($this->announcementId > 0) {
            try {
                //删除职位公告地区中间表数据
                BaseAnnouncementAreaRelation::deleteAll(['announcement_id' => $this->announcementId]);
                //删除职位公告学历中间表数据
                BaseAnnouncementEducationRelation::deleteAll(['announcement_id' => $this->announcementId]);
                //获取公告下所有职位(在线、下线)
                $jobList        = BaseJob::find()
                    ->select([
                        'province_id',
                        'city_id',
                        'education_type',
                    ])
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => [
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                    ])
                    ->asArray()
                    ->all();
                $provinceIds    = array_unique(array_column($jobList, 'province_id'));
                $cityIds        = array_unique(array_column($jobList, 'city_id'));
                $educationCodes = array_unique(array_column($jobList, 'education_type'));

                //批量写入表中
                $areaInsert = [];
                foreach ($provinceIds as $provinceId) {
                    $item         = [
                        'announcement_id' => $this->announcementId,
                        'area_id'         => $provinceId,
                        'level'           => 1,
                    ];
                    $areaInsert[] = $item;
                }
                foreach ($cityIds as $cityId) {
                    $item         = [
                        'announcement_id' => $this->announcementId,
                        'area_id'         => $cityId,
                        'level'           => 2,
                    ];
                    $areaInsert[] = $item;
                }
                if ($areaInsert) {
                    BaseAnnouncementAreaRelation::getDb()
                        ->createCommand()
                        ->batchInsert(BaseAnnouncementAreaRelation::tableName(), [
                            'announcement_id',
                            'area_id',
                            'level',
                        ], $areaInsert)
                        ->execute();
                }

                //学历要求
                $educationInsert = [];
                foreach ($educationCodes as $educationCode) {
                    $item              = [
                        'announcement_id' => $this->announcementId,
                        'education_code'  => $educationCode,
                    ];
                    $educationInsert[] = $item;
                }
                if ($educationInsert) {
                    BaseAnnouncementEducationRelation::getDb()
                        ->createCommand()
                        ->batchInsert(BaseAnnouncementEducationRelation::tableName(), [
                            'announcement_id',
                            'education_code',
                        ], $educationInsert)
                        ->execute();
                }
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        }
    }

    public function updateAnnouncementEstablishment()
    {
        $announcementId = $this->jobModel->announcement_id;
        if ($announcementId) {
            //查询更新公告编制字段
            $establishmentType                     = BaseAnnouncement::getEstablishmentType($announcementId);
            $announcementModel                     = BaseAnnouncement::findOne($announcementId);
            $announcementModel->establishment_type = $establishmentType;
            $announcementModel->save();
        }
    }

    /**
     * 更新博士后职位表
     * @throws Exception
     */
    private function updateJobBoShiHouTable()
    {
        if (BaseJobBoshihou::find()
                ->where(['job_id' => $this->jobId])
                ->exists() && !in_array($this->jobModel->job_category_id, self::BOSHIHOU_CATEGORY_ID)) {
            //删除
            BaseJobBoshihou::deleteAll(['job_id' => $this->jobId]);
        }
        if (!BaseJobBoshihou::find()
                ->where(['job_id' => $this->jobId])
                ->exists() && in_array($this->jobModel->job_category_id, self::BOSHIHOU_CATEGORY_ID)) {
            // 添加
            $model         = new BaseJobBoshihou();
            $model->job_id = $this->jobId;
            $model->save();
        }

        return true;
    }

    private function updateJobSearchName()
    {
        $sql = "UPDATE job j
                JOIN company c ON j.company_id = c.id
                LEFT JOIN announcement a ON a.id = j.announcement_id
                SET j.search_name = CONCAT(j.name, ' ', c.full_name, ' ', j.department, ' ', IFNULL(a.title, ''))
                where j.id = {$this->jobId}";

        \Yii::$app->db->createCommand($sql)
            ->execute();
    }

    private function update()
    {
        // $homeColumnId = $this->articleModel->home_column_id;
        // $this->setRemark("主栏目id:$homeColumnId,名字:{$this->allColunmList[$homeColumnId]['name']}");
        // $this->addColumn($homeColumnId);
        // $homeSubColumnIds = explode(',', $this->articleModel->home_sub_column_ids);
        // foreach ($homeSubColumnIds as $homeSubColumnId) {
        //     $this->setRemark("副栏目id:$homeSubColumnId,名字:{$this->allColunmList[$homeSubColumnId]['name']}");
        //     $this->addColumn($homeSubColumnId);
        // }
        // $oldColumnId  = BaseArticleColumn::find()
        //     ->where(['article_id' => $this->articleModel->id])
        //     ->select('column_id')
        //     ->column();
        // $oldColumnArr = array_unique(array_merge([$homeColumnId], $homeSubColumnIds, $oldColumnId));
        // $newColumnArr = array_unique(array_merge([$homeColumnId], $homeSubColumnIds, $this->columnLevle1List,
        //     $this->columnLevle2List));
        //
        // BaseArticleColumn::rebuild($this->articleModel->id, $newColumnArr);
        //
        // // 最后写日志
        // $model                         = new AnnouncementAutoClassifyLog();
        // $model->before_home_column_ids = implode(',', $oldColumnArr);
        // $model->after_home_column_ids  = implode(',', $newColumnArr);
        // $model->announcement_id        = $this->announcementId;
        // $model->remark                 = $this->remark;
        // $model->save();

        $newColumnArr = array_unique($this->newColumnList);

        BaseJobColumn::rebuild($this->jobId, $newColumnArr);

        // // 最后写日志
        $model                         = new JobAutoClassifyLog();
        $model->before_home_column_ids = '';
        $model->after_home_column_ids  = implode(',', $newColumnArr);
        $model->job_id                 = $this->jobId;
        $model->remark                 = $this->remark;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        $this->addToMeilisearch();
    }

    /**
     * 添加职位到meilisearch
     * @return void
     */
    private function addToMeilisearch()
    {
        // $model = new AddService();
        // $model->saveById($this->jobId);
        Producer::meilisearch($this->jobId, MeilisearchJob::TYPE_JOB);
    }

    /**
     * 更新单位扩展表的职位相关
     * @return void
     */
    public function updateStatInfo()
    {
        $jobInfo = BaseJob::find()
            ->select([
                'company_id',
                'job_category_id',
                'status',
                'is_show',
            ])
            ->where([
                'id' => $this->jobId,
            ])
            ->asArray()
            ->one();

        $statModel = BaseCompanyStatData::find()
            ->where(['company_id' => $jobInfo['company_id']])
            ->one();

        if (!$statModel) {
            $statModel             = new BaseCompanyStatData();
            $statModel->company_id = $jobInfo['company_id'];
        }

        // 当前职位满足博士后栏目
        if (in_array($jobInfo['job_category_id'], self::BOSHIHOU_CATEGORY_ID) && in_array($jobInfo['status'], [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ]) && $jobInfo['is_show'] == BaseJob::IS_SHOW_YES) {
            $statModel->is_boshihou_column = BaseCompanyStatData::IS_BOSHIHOU_COLUMN;
        } else {
            // 查询这个单位满足上线的职位
            $boshihouExists = BaseJob::find()
                ->select(['id'])
                ->where([
                    'company_id'      => $jobInfo['company_id'],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'status'          => BaseJob::STATUS_ONLINE,
                    'job_category_id' => self::BOSHIHOU_CATEGORY_ID,
                ])
                ->exists();
            if ($boshihouExists) {
                $statModel->is_boshihou_column = BaseCompanyStatData::IS_BOSHIHOU_COLUMN;
            } else {
                $statModel->is_boshihou_column = BaseCompanyStatData::IS_NOT_BOSHIHOU_COLUMN;
            }
        }

        $onLineCount = BaseJob::find()
            ->where([
                'company_id' => $jobInfo['company_id'],
                'is_show'    => BaseJob::IS_SHOW_YES,
                'status'     => BaseJob::STATUS_ONLINE,
            ])
            ->count();

        $allCount = BaseJob::find()
            ->where([
                'company_id' => $jobInfo['company_id'],
                'is_show'    => BaseJob::IS_SHOW_YES,
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->count();

        $statModel->all_job_count    = $allCount;
        $statModel->online_job_count = $onLineCount;
        $statModel->save();
    }
}