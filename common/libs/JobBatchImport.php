<?php

namespace common\libs;

use admin\models\MajorAi;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\base\models\BaseMajorAiLog;
use common\base\models\BaseMember;
use common\base\models\BaseWelfareLabel;
use common\helpers\DebugHelper;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use common\models\Area;
use common\models\CategoryJob;
use common\models\Dictionary;
use common\models\Major;
use Vtiful\Kernel\Excel;
use yii\base\Exception;

/**
 * 职位批量导入EXCEL识别的方法
 *
 * 这里对于excel导入的那些规则做一份说明
 * 表头红色为必填
 *
 */
class JobBatchImport
{
    const PLATFORM_COMPANY     = 2;//单位端
    const PLATFORM_COMPANY_201 = 201;//站外类型单位
    const PLATFORM_COMPANY_202 = 202;//站内类型单位
    const PLATFORM_COMPANY_203 = 203;//站内+站外类型单位
    const PLATFORM_ADMIN       = 1;//运营后台
    // 表格头
    private $header = [];
    // 原始数据
    private $originalData = [];
    // 清洗以后的数据
    public $clearData = [];
    // 文件路径
    private $filePath = [];
    // 现在处理到了第几行
    private $columnIndex = '';
    // 现在处理到了第几列
    private $lineIndex = '';
    private $platformType;//操作平台
    private $totalNum;//当前表头数量

    private $errorMessage = '';

    private $errorColumn = '';

    private $searchMajorList       = [];
    private $searchMajorAiList     = [];
    private $searchJobCategoryList = [];
    private $searchEducationList   = [];
    private $searchAddressList     = [];
    private $searchTitleList       = [];
    private $searchAbroad          = [];
    private $searchPolitical       = [];
    private $searchExperienceList  = [];
    private $searchWelfare         = [];
    private $searchNature          = [];
    private $searchAge             = [];

    /**
     * 必须要有这几个字段,才认为这个有用,要不将跳过这一行数据
     */
    const VALID_LIST  = [];
    const HEAD_HEIGHT = 2;
    private $line_list       = [];
    private $temp_header_arr = [];//表头数据
    const TOTAL_LINE_LIST = [
        // 这个是普通文案,直接识别即可
        0  => [
            'key'       => 'name',
            'name'      => '职位名称',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        // 在excel里面,一级其实是为了帮助出二级,所以一级在识别的时候是可以无视的
        1  => [
            'key'       => 'jobCategoryLevel1',
            'name'      => '职位类型一级',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        // 这个是二级,必须要有,而且是需要到jobCategory里面去拿
        2  => [
            'key'       => 'jobCategoryId',
            'name'      => '职位类型二级',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        // 学历要求,这个是和我们的字典里面的学历要求一一对应,对应不上就不给录入
        3  => [
            'key'       => 'educationType',
            'name'      => '学历要求',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        // 需求专业这里就识别code
        4  => [
            'key'       => 'majorId',
            'name'      => '需求专业',
            'data_type' => 'string',
            'rules'     => [],
        ],
        5  => [
            'key'       => 'majorAi',
            'name'      => '识别学科',
            'data_type' => 'string',
            'rules'     => [],
        ],
        // 招聘人数必须是数字
        6  => [
            'key'       => 'amount',
            'name'      => '招聘人数',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        // 工作地点是城市,用；隔开,必须是city而不是其他地方
        7  => [
            'key'       => 'provinceId',
            'name'      => '工作地点省级',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        8  => [
            'key'       => 'cityId',
            'name'      => '工作地点市级',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        9  => [
            'key'       => 'periodDate',
            'name'      => '职位有效期',
            'data_type' => 'datetime',
            'rules'     => [],
        ],
        10 => [
            'key'       => 'minWageYear',
            'name'      => '最低年薪（W）',
            'data_type' => 'string',
            'rules'     => [],
        ],
        11 => [
            'key'       => 'maxWageYear',
            'name'      => '最高年薪（W）',
            'data_type' => 'string',
            'rules'     => [],
        ],
        12 => [
            'key'       => 'minWageMonth',
            'name'      => '最低月薪（K）',
            'data_type' => 'string',
            'rules'     => [],
        ],
        13 => [
            'key'       => 'maxWageMonth',
            'name'      => '最高月薪（K）',
            'data_type' => 'string',
            'rules'     => [],
        ],
        14 => [
            'key'       => 'experienceType',
            'name'      => '工作年限',
            'data_type' => 'string',
            'rules'     => [],
        ],
        15 => [
            'key'       => 'ageType',
            'name'      => '年龄要求',
            'data_type' => 'string',
            'rules'     => [],
        ],
        16 => [
            'key'       => 'titleType',
            'name'      => '职称',
            'data_type' => 'string',
            'rules'     => [],
        ],
        17 => [
            'key'       => 'abroadType',
            'name'      => '海外经历',
            'data_type' => 'string',
            'rules'     => [],
        ],
        18 => [
            'key'       => 'department',
            'name'      => '用人部门',
            'data_type' => 'string',
            'rules'     => [],
        ],
        19 => [
            'key'       => 'politicalType',
            'name'      => '政治面貌',
            'data_type' => 'string',
            'rules'     => [],
        ],
        20 => [
            'key'       => 'welfareTag',
            'name'      => '职位福利',
            'data_type' => 'string',
            'rules'     => [],
        ],
        21 => [
            'key'       => 'duty',
            'name'      => '岗位职责',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        22 => [
            'key'       => 'requirement',
            'name'      => '任职要求',
            'data_type' => 'string',
            'rules'     => ['require'],
        ],
        23 => [
            'key'       => 'remark',
            'name'      => '其他说明',
            'data_type' => 'string',
            'rules'     => [],
        ],
        24 => [
            'key'       => 'applyType',
            'name'      => '报名方式',
            'data_type' => 'string',
            'rules'     => [],
        ],
        25 => [
            'key'       => 'applyAddress',
            'name'      => '投递地址',
            'data_type' => 'string',
            'rules'     => [],
        ],
        26 => [
            'key'       => 'natureType',
            'name'      => '工作性质',
            'data_type' => 'string',
            'rules'     => [],
        ],
        27 => [
            'key'       => 'deliveryLimitTypeFile',
            'name'      => '投递应聘材料限制',
            'data_type' => 'string',
            'rules'     => [],
        ],
        28 => [
            'key'       => 'deliveryLimitTypeEducation',
            'name'      => '投递学历限制',
            'data_type' => 'string',
            'rules'     => [],
        ],
        29 => [
            'key'       => 'deliveryType',
            'name'      => '投递类型',
            'data_type' => 'string',
            'rules'     => [],
        ],
        30 => [
            'key'       => 'deliveryWay',
            'name'      => '投递方式',
            'data_type' => 'string',
            'rules'     => [],
        ],
        31 => [
            'key'       => 'extraNotifyAddress',
            'name'      => '通知邮箱地址',
            'data_type' => 'string',
            'rules'     => [],
        ],
        32 => [
            'key'       => 'establishmentType',
            'name'      => '职位编制',
            'data_type' => 'string',
            'rules'     => [],
        ],
        33 => [
            'key'       => 'contactSynergyEmail',
            'name'      => '协同子账号邮箱',
            'data_type' => 'string',
            'rules'     => [],
        ],
    ];

    //运营后台模板配置
    const ADMIN_LINE_LIST = [
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        31,
        32,
        33,
    ];
    //单位端模板配置
    const COMPANY_LINE_LIST = [
        0,
        1,
        2,
        3,
        4,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
    ];
    //站内模板 对应单位类型delivery_type=2 转换类型 202
    const COMPANY_LINE_LIST_202 = [
        0,
        1,
        2,
        3,
        4,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        26,
        27,
        31,
    ];
    //站内+站外 对应单位类型delivery_type=3 转换类型 203
    const COMPANY_LINE_LIST_203 = [
        0,
        1,
        2,
        3,
        4,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
    ];
    //特殊处理
    private $pushDeliveryWay = 30;

    // Excel表格职位一级分类名称与数据库职位一级分类名称对应关系
    const CATEGORY_LEVEL1_LIST = [
        [
            'excelCateName' => '教学岗1高校类',
            'dataCateName'  => '教学岗（高等院校）',
        ],
        [
            'excelCateName' => '教学岗2中小学及学前类',
            'dataCateName'  => '教学岗（中小学及幼儿园）',
        ],
        [
            'excelCateName' => '中高级管理岗1教育科研类',
            'dataCateName'  => '中高级管理岗（教育/科研机构）',
        ],
        [
            'excelCateName' => '中高级管理岗2企业',
            'dataCateName'  => '中高级管理岗（企业）',
        ],
        [
            'excelCateName' => '教学支撑岗1高校类',
            'dataCateName'  => '教学支撑岗（高等院校）',
        ],
        [
            'excelCateName' => '教学支撑岗2中小学及学前',
            'dataCateName'  => '教学支撑岗（中小学及幼儿园）',
        ],
        [
            'excelCateName' => '科学科研岗1教育科研卫生类',
            'dataCateName'  => '科学研究岗（教育/科研/卫生单位）',
        ],
        [
            'excelCateName' => '科学科研岗2企事业单位',
            'dataCateName'  => '科学研究岗（企事业单位）',
        ],
        [
            'excelCateName' => '公务员、事业单位工作人员',
            'dataCateName'  => '公务员/事业单位/军队工作人员',
        ],
        [
            'excelCateName' => '生物、医药、医疗器械专业岗',
            'dataCateName'  => '生物/医药/医疗器械专业岗',
        ],
        [
            'excelCateName' => '化工、轻工、食品专业岗',
            'dataCateName'  => '化工/轻工/食品专业岗',
        ],
        [
            'excelCateName' => '人力、行政、财务岗',
            'dataCateName'  => '人力/行政/财务岗',
        ],
        [
            'excelCateName' => '销售、商务、客服、招生岗',
            'dataCateName'  => '销售/商务/客服/招生岗',
        ],
        [
            'excelCateName' => '法务、翻译、咨询、教培岗',
            'dataCateName'  => '法务/翻译/咨询/教培岗',
        ],
        [
            'excelCateName' => '编辑、出版、传媒、文化岗',
            'dataCateName'  => '编辑/出版/传媒/文化岗',
        ],
        [
            'excelCateName' => '市场、公关、广告、会展岗',
            'dataCateName'  => '市场/公关/广告/会展岗',
        ],
        [
            'excelCateName' => '技术研究、开发、测试、运维岗',
            'dataCateName'  => '技术研究/开发/测试/运维岗',
        ],
        [
            'excelCateName' => '互联网产品、设计、运营岗',
            'dataCateName'  => '互联网产品/设计/运营岗',
        ],
        [
            'excelCateName' => '电子、通信、硬件、半导体岗',
            'dataCateName'  => '电子/通信/硬件/半导体岗',
        ],
        [
            'excelCateName' => '能源、矿产、电力、环保专业岗',
            'dataCateName'  => '能源/矿产/电力/环保专业岗',
        ],
        [
            'excelCateName' => '房地产、建筑、物业岗',
            'dataCateName'  => '房地产/建筑/物业专业岗',
        ],
        [
            'excelCateName' => '生产制造、机械、汽车专业岗',
            'dataCateName'  => '生产制造/机械/汽车专业岗',
        ],
        [
            'excelCateName' => '零售、生活服务、农林牧渔专业岗',
            'dataCateName'  => '零售/生活服务/农林牧渔专业岗',
        ],
        [
            'excelCateName' => '供应链、物流、运输、采购、贸易专业岗',
            'dataCateName'  => '供应链/物流/运输/采购/贸易专业岗',
        ],
        [
            'excelCateName' => '管培生、其他',
            'dataCateName'  => '管培生/其他',
        ],
    ];

    // 初始化
    public function identify($filePath, $type = self::PLATFORM_ADMIN)
    {
        $this->filePath = $filePath;
        switch ($type) {
            case self::PLATFORM_ADMIN:
                $this->platformType    = $type;
                $this->temp_header_arr = self::ADMIN_LINE_LIST;
                break;
            case self::PLATFORM_COMPANY:
                $this->platformType    = $type;
                $this->temp_header_arr = self::COMPANY_LINE_LIST;
                break;
            case self::PLATFORM_COMPANY_202:
                $this->platformType    = self::PLATFORM_COMPANY;
                $this->temp_header_arr = self::COMPANY_LINE_LIST_202;
                break;
            case self::PLATFORM_COMPANY_203:
                $this->platformType    = self::PLATFORM_COMPANY;
                $this->temp_header_arr = self::COMPANY_LINE_LIST_203;
                break;
            default:
                $this->temp_header_arr = [];
                break;
        }
        $this->totalNum = count($this->temp_header_arr);
        $this->templateHeader();
        $this->readData();
        $this->cleanData();
    }

    /**
     * 输入导入模板头配置
     * @throws Exception
     */
    private function templateHeader()
    {
        if ($this->totalNum <= 0) {
            throw new Exception('模板获取出错');
        }
        foreach ($this->temp_header_arr as $value) {
            array_push($this->line_list, self::TOTAL_LINE_LIST[$value]);
        }
    }

    /**
     * 读取数据
     * @throws Exception
     */
    private function readData()
    {
        $filePath   = $this->filePath;
        $file_array = explode('/', $filePath);
        $fileName   = array_pop($file_array); // 文件名称
        $fileArray  = explode('.', $fileName);

        // xlsx和xlsm格式
        if (!in_array('xlsx', explode('.', $fileName)) && !in_array('xlsm', explode('.', $fileName))) {
            throw new Exception('仅支持xlsx或xlsm文件导入');
        }

        $path = implode('/', $file_array);
        // 文件路径
        $config     = ['path' => $path];
        $excel      = new Excel($config);
        $columnType = [];
        foreach ($this->line_list as $item) {
            switch ($item['data_type']) {
                //case 'string':
                //    array_push($columnType,Excel::TYPE_STRING);
                //    break;
                case 'datetime':
                    array_push($columnType, Excel::TYPE_TIMESTAMP);
                    break;
                default:
                    array_push($columnType, Excel::TYPE_STRING);
                    break;
            }
        }
        $dataExcel          = $excel->openFile($fileName)
            ->openSheet()
            ->setType($columnType);
        $data               = $dataExcel->getSheetData(); // 获取表格内数据
        $this->header       = $data[1];
        $this->originalData = array_splice($data, 2);
        $headerCount        = count($this->header);
        //检查模板的使用情况
        if ($headerCount != $this->totalNum) {
            throw new Exception('模板出错，请下载新模板！');
        }

        foreach ($this->header as $key => $value) {
            if ($this->line_list[$key]['name'] != $value) {
                throw new Exception('模板的第' . ($key + 1) . '列出错，请下载新模板对比！');
            }
        }
    }

    /**
     * 清洗数据(做一些基本的判断,暂不涉及数据库)
     */
    private function cleanData()
    {
        $data    = $this->originalData;
        $newData = [];
        // 最终有效的data,这里的意思是有填入数据的data
        $effectiveData = [];
        $columnList    = $this->line_list;

        foreach ($data as $k => $item) {
            // 首先是必须去空格,不止单个两边的空格,
            $columnIndex = $k + self::HEAD_HEIGHT + 1;
            $newLineData = [];
            // 默认情况下,这个数据是无效的
            $flag = false;
            foreach ($item as $lineIndex => $i) {
                $column = $columnList[$lineIndex];
                $value  = trim($i);
                //$newLineData[$lineIndex] = [
                $newLineData[$column['key']] = [
                    'key'         => $column['key'],
                    'name'        => $column['name'],
                    'rules'       => $column['rules'],
                    'value'       => $value,
                    'columnIndex' => $columnIndex,
                    'lineIndex'   => $lineIndex + 1,
                ];

                if ($value) {
                    $flag = true;
                }
            }
            if ($flag) {
                $effectiveData[] = $newLineData;
            }
        }

        foreach ($effectiveData as $column) {
            foreach ($column as $line) {
                // 做一个简单的检查
                $this->checkRule($line);
            }
        }
        // 开始对数据进行清洗
        foreach ($effectiveData as &$column) {
            $wageArr        = [];
            $applyArr       = [];
            $jobCategoryArr = [];
            foreach ($column as &$line) {
                // 做一个简单的检查
                $key = $line['key'];
                switch ($key) {
                    case 'majorId':
                        // 需求专业
                        $line = $this->cleanMajor($line);
                        break;
                    case 'majorAi':
                        // 识别学科
                        $line = $this->cleanMajorAi($line);
                        break;
                    case 'educationType':
                        // 学历要求
                        $line = $this->cleanEducation($line);
                        break;
                    case 'amount':
                        // 招聘人数
                        $line = $this->cleanAmount($line);
                        break;
                    case 'provinceId':
                        // 城市省
                        $line = $this->cleanAddressProvinceId($line);
                        break;
                    case 'cityId':
                        // 城市市
                        $line = $this->cleanAddressCityId($line);
                        break;
                    case 'periodDate':
                        // 有效期
                        $line = $this->cleanPeriod($line);
                        break;
                    case 'experienceType':
                        // 工作年限,需要改造
                        $line = $this->cleanExperience($line);
                        break;
                    case 'ageType':
                        // 年龄
                        $line = $this->cleanAge($line);
                        break;
                    case 'titleType':
                        // 职称
                        $line = $this->cleanTitle($line);
                        break;
                    case 'abroadType':
                        $line = $this->cleanAbroad($line);
                        break;
                    case 'department':
                        $line = $this->cleanDepartment($line);
                        break;
                    case 'politicalType':
                        $line = $this->cleanPolitical($line);
                        break;
                    case 'welfareTag':
                        $line = $this->cleanWelfare($line);
                        break;
                    case 'duty':
                        $line = $this->cleanDuty($line);
                        break;
                    case 'requirement':
                        $line = $this->cleanRequirement($line);
                        break;
                    case 'remark':
                        $line = $this->cleanRemark($line);
                        break;
                    case 'natureType':
                        $line = $this->cleanNature($line);
                        break;
                    case 'deliveryLimitTypeFile':
                        $line = $this->cleanDeliveryLimitTypeFile($line);
                        break;
                    case 'deliveryLimitTypeEducation':
                        $line = $this->cleanDeliveryLimitTypeEducation($line);
                        break;
                    case 'deliveryType':
                        $line = $this->cleanDeliveryType($line);
                        break;
                    case 'minWageYear':
                    case 'maxWageYear':
                        if ($line['value'] > 0) {
                            $line['value'] = $line['value'] * 10000;
                        }
                        break;
                    case 'minWageMonth':
                    case 'maxWageMonth':
                        if ($line['value'] > 0) {
                            $line['value'] = $line['value'] * 1000;
                        }
                        break;
                    case 'establishmentType':
                        $line = $this->cleanJobOrganizationType($line);
                        break;
                    case 'contactSynergyEmail':
                        $line = $this->cleanContactSynergyEmail($line);
                        break;
                    default:
                        break;
                }
            }
            //这里验证一下省份与城市是否是一起的
            $column = $this->verifyArea($column);
            // 这里时候需要处理两个比较特殊的,一个是薪酬,一个是应聘方式+地址
            $column = $this->cleanApply($column);

            $column = $this->cleanWage($column);
            // 职位类型--注意：这个方法有写死的列有变动要注意
            $column = $this->cleanJobCategory($column);
        }
        $this->clearData = $effectiveData;
    }

    /**
     * 清洗协同子账号邮箱
     * @param $line
     * @return mixed
     * @throws Exception
     */
    private function cleanContactSynergyEmail($line)
    {
        if (!empty($line['value'])) {
            //先榨成数组
            $data = explode(',', $line['value']);
            //判断长度必须小于等于三个
            if (count($data) > 3) {
                $this->error($line, '协同子账号邮箱最多三个');
            }
            //去重
            $data = array_unique($data);
            //验证邮箱格式
            foreach ($data as $item) {
                if (!ValidateHelper::isEmail($item)) {
                    $this->error($line, '协同子账号' . $item . '邮箱格式不正确');
                }
            }
            //去重的邮箱赋值回去
            $line['value'] = implode(',', $data);
        }

        return $line;
    }

    /**
     * 验证一下省份与城市是否是一起的
     */
    private function verifyArea($column)
    {
        $province_id = $column['provinceId']['value'];
        $city_id     = $column['cityId']['value'];
        $info        = BaseArea::findOne([
            'id'        => $city_id,
            'parent_id' => $province_id,
            'status'    => BaseArea::STATUS_ACTIVE,
        ]);
        if (!$info) {
            $this->error($column['provinceId'], '填写的省份与城市不匹配，请核查！');
        }

        return $column;
    }

    /**
     * 职位编制数据清洗
     * @param $line
     * @return mixed
     */
    private function cleanJobOrganizationType($line)
    {
        $data = $line['value'];
        if (!$data) {
            return $line;
        }
        $data      = str_replace(';', '；', $line['value']);
        $dataArr   = explode(';', $data);
        $dataArr   = array_unique($dataArr);
        $dataIdArr = [];
        foreach ($dataArr as $name) {
            array_push($dataIdArr, BaseDictionary::getCodeByName(BaseDictionary::TYPE_ESTABLISHMENT, $name));
        }
        $data          = implode('；', $dataIdArr);
        $line['value'] = $data;

        return $line;
    }

    /**
     * @param $value
     *              　　if(preg_match("/^[a-zd]*$/i",   "fd4fd34"))
     */
    private function cleanMajor($line)
    {
        $data = str_replace(';', '；', $line['value']);

        // 专业可以为空
        if (!$data) {
            return $line;
        }

        if ($data == '专业不限') {
            $line['value'] = BaseDictionary::STATUS_UNLIMITED;

            return $line;
        }
        $dataArr = explode('；', $data);

        $dataList = [];
        $majorOne = [];
        $majorArr = [];
        foreach ($dataArr as $key => $item) {
            // 正则提取里面的code
            preg_match_all('/[0-9a-zA-Z]+/', $item, $match);
            $code = $match[0][0];
            if (!$code) {
                $this->error($line, '专业代码不正确');
            }

            // 找这个code在系统里面是否存在
            $dataId = Major::findOneVal([
                'code'   => $code,
                'status' => 1,
            ], 'id');
            if (!$dataId) {
                $this->error($line, '专业代码不正确' . $data . '不存在');
            }

            // 这里是查询等级
            $level = Major::findOneVal([
                'code'   => $code,
                'status' => 1,
            ], 'level');

            // 这里如果是大类，就获取大类下所有的二级id
            if ($level == 1) {
                // 获取所有二级id
                $majorIds                     = BaseMajor::getLevel2MajorById($code);
                $majorArr[]                   = $majorIds;
                $this->searchMajorList[$code] = $majorArr;
            }

            $majorOne[]                   = $dataId;
            $this->searchMajorList[$code] = $dataId;
            if ($level == 1) {
                unset($majorOne[$key]);
            }
        }

        $majorAllOne = [];
        foreach ($majorArr as $value) {
            foreach ($value as $id) {
                $majorAllOne[] = $id;
            }
        }

        $dataList = array_merge($majorOne, $majorAllOne);
        // 去重
        $line['value'] = array_unique($dataList);

        return $line;
    }

    /**
     * @param $value
     */
    private function cleanAddressProvinceId($line)
    {
        $data = $line['value'];
        if (!$data) {
            $this->error($line, '填写错误');
        }

        // 找这个code在系统里面是否存在
        $dataId = Area::findOneVal([
            'name'   => $data,
            'status' => BaseArea::STATUS_ACTIVE,
            'level'  => 1,
        ], 'id');

        if (!$dataId) {
            $this->error($line, '填写错误' . $data . '不存在');
        }
        $line['value'] = $dataId;

        return $line;
    }

    /**
     * @param $value
     */
    private function cleanAddressCityId($line)
    {
        $data = $line['value'];
        if (!$data) {
            $this->error($line, '填写错误');
        }
        // 正则提取里面的code
        // 在里面了,就不需要再次去拿,重复了
        if ($this->searchAddressList[$data]) {
            $line['value'] = $this->searchAddressList[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = Area::findOneVal([
                'name'   => $data,
                'status' => BaseArea::STATUS_ACTIVE,
                'level'  => 2,
            ], 'id');

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']                  = $dataId;
            $this->searchAddressList[$data] = $dataId;
        }

        return $line;
    }

    /**
     * @param $value
     */
    private function cleanEducation($line)
    {
        $data = $line['value'];
        if (!$data) {
            $this->error($line, '填写错误');
        }
        // 正则提取里面的code
        // 在里面了,就不需要再次去拿,重复了
        if ($this->searchEducationList[$data]) {
            $line['value'] = $this->searchEducationList[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_EDUCATION, $data);

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']                    = $dataId;
            $this->searchEducationList[$data] = $dataId;
        }

        return $line;
    }

    /**
     * @param $value
     */
    private function cleanJobCategory($column)
    {
        $jobCategoryLevel1 = $column['jobCategoryLevel1']['value'];
        $jobCategoryLevel2 = $column['jobCategoryId']['value'];
        if (empty($jobCategoryLevel1)) {
            $this->error($column['jobCategoryLevel1'], '不能为空');
        }
        if (empty($jobCategoryLevel2)) {
            $this->error($column['jobCategoryId'], '不能为空');
        }

        foreach (self::CATEGORY_LEVEL1_LIST as $name) {
            if ($name['excelCateName'] == $jobCategoryLevel1) {
                $jobCategoryLevel1 = $name['dataCateName'];
                break;
            }
        }

        $count = CategoryJob::find()
            ->where([
                'name'      => $jobCategoryLevel2,
                'level'     => 2,
                'is_delete' => BaseCategoryJob::IS_DELETE_NO,
            ])
            ->count();
        // 如果有同名的二级分类
        if ($count > 1) {
            // 首先找到一级分类
            $level1Id = CategoryJob::findOneVal([
                'name'      => $jobCategoryLevel1,
                'level'     => 1,
                'is_delete' => BaseCategoryJob::IS_DELETE_NO,
            ], 'id');

            // 通过一级分类id找到二级
            $level2Id = CategoryJob::findOneVal([
                'parent_id' => $level1Id,
                'name'      => $jobCategoryLevel2,
                'level'     => 2,
                'is_delete' => BaseCategoryJob::IS_DELETE_NO,
            ], 'id');
        } else {
            $level2Id = CategoryJob::findOneVal([
                'name'      => $jobCategoryLevel2,
                'level'     => 2,
                'is_delete' => BaseCategoryJob::IS_DELETE_NO,
            ], 'id');
        }

        if (!$level2Id) {
            $this->error($column['jobCategoryId'], '填写错误：' . $jobCategoryLevel2 . '不存在');
        }

        $column['jobCategoryId']['value'] = $level2Id;

        return $column;
    }

    /**
     * @param $value
     */
    private function cleanAmount($line)
    {
        $data = $line['value'];

        if (!$data) {
            $this->error($line, '填写错误');
        }

        $matches = preg_match('/^([1-9]\d{0,3}|\u82e5\u5e72)$/', $data);
        if (!$matches && $data != '若干') {
            $this->error($line, '填写错误');
        }

        $line['value'] = $data;

        return $line;
    }

    /**
     * @param $line
     * @throws Exception
     */
    private function cleanPeriod($line)
    {
        $data = $line['value'];
        if (!$data) {
            if ($this->platformType == self::PLATFORM_COMPANY) {
                $this->error($line, '不允许为空');
            }
            $line['value'] = TimeHelper::ZERO_TIME;

            return $line;
        }

        if ($data == '详见正文' || $data == '详见公告') {
            $line['value'] = TimeHelper::ZERO_TIME;

            return $line;
        }
        if ($data > 0 && $data < strtotime(date('Y-m-d', time()))) {
            $this->error($line, '职位有效期必须大于当前时间');
        }
        if (is_numeric($data)) {
            if ($data > (time() + 60 * 60 * 24 * 365)) {
                $this->error($line, '不能超过当前时间的365天');
            }
            // 强制转数字时间搓
            $data          = Date('Y-m-d', $data);
            $line['value'] = $data;

            return $line;
        }

        $this->error($line, '填写错误');
    }

    /**
     * 工作年限
     * @param $line
     */
    private function cleanExperience($line)
    {
        $data = $line['value'];
        if (!$data) {
            $line['value'] = '';

            return $line;
        }
        if ($data == '不限') {
            $line['value'] = BaseDictionary::STATUS_UNLIMITED;

            return $line;
        }
        // 正则提取里面的code
        // 在里面了,就不需要再次去拿,重复了
        if ($this->searchExperienceList[$data]) {
            $line['value'] = $this->searchExperienceList[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_EXPERIENCE, $data);
            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']                     = $dataId;
            $this->searchExperienceList[$data] = $dataId;
        }

        return $line;
    }

    /**
     * 年龄要求
     * @param $line
     * @throws Exception
     */
    private function cleanAge($line)
    {
        return $line;
    }

    /**
     * 职称
     * @param $line
     * @throws Exception
     */
    private function cleanTitle($line)
    {
        $data = $line['value'];
        if (!$data) {
            $line['value'] = '';

            return $line;
        }
        if ($data == '不限') {
            $line['value'] = BaseDictionary::STATUS_UNLIMITED;

            return $line;
        }
        // 正则提取里面的code
        // 在里面了,就不需要再次去拿,重复了
        if ($this->searchTitleList[$data]) {
            $line['value'] = $this->searchTitleList[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_TITLE, $data);

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']                = $dataId;
            $this->searchTitleList[$data] = $dataId;
        }

        return $line;
    }

    private function cleanAbroad($line)
    {
        $data = $line['value'];
        if ($data == '不限') {
            $line['value'] = BaseDictionary::STATUS_UNLIMITED;

            return $line;
        }
        if (!$data) {
            $line['value'] = '';

            return $line;
        }

        if ($this->searchAbroad[$data]) {
            $line['value'] = $this->searchAbroad[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_ABROAD, $data);

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']             = $dataId;
            $this->searchAbroad[$data] = $dataId;
        }

        return $line;
    }

    private function cleanDepartment($line)
    {
        return $line;
    }

    private function cleanPolitical($line)
    {
        $data = $line['value'];
        if ($data == '不限') {
            $line['value'] = BaseDictionary::STATUS_UNLIMITED;

            return $line;
        }
        if (!$data) {
            $line['value'] = '';

            return $line;
        }
        if ($this->searchPolitical[$data]) {
            $line['value'] = $this->searchPolitical[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_POLITICAL, $data);

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']                = $dataId;
            $this->searchPolitical[$data] = $dataId;
        }

        return $line;
    }

    /**
     * 首先年薪或者月薪只能选其一,不能同时选
     * 并且可以允许年薪或者月薪为空,可以只有上限或者下限
     * @param $column
     * @return bool
     */
    private function cleanWage($column)
    {
        $minWageYearValue  = $column['minWageYear']['value'];
        $maxWageYearValue  = $column['maxWageYear']['value'];
        $minWageMonthValue = $column['minWageMonth']['value'];
        $maxWageMonthValue = $column['maxWageMonth']['value'];
        if (empty($maxWageMonthValue) && empty($minWageMonthValue) && empty($maxWageYearValue) && empty($minWageYearValue)) {
            return $column;
        }

        if ($minWageYearValue && !is_numeric($minWageYearValue)) {
            $this->error($column['minWageYear'], '必须是数字');
        }
        if ($maxWageYearValue && !is_numeric($maxWageYearValue)) {
            $this->error($column['maxWageYear'], '必须是数字');
        }
        if ($minWageMonthValue && !is_numeric($minWageMonthValue)) {
            $this->error($column['minWageMonth'], '必须是数字');
        }
        if ($maxWageMonthValue && !is_numeric($maxWageMonthValue)) {
            $this->error($column['maxWageMonth'], '必须是数字');
        }
        if (($minWageYearValue || $maxWageYearValue) && ($minWageMonthValue || $maxWageMonthValue)) {
            $this->error($column['minWageYear'], '年薪和月薪只能选其一');
        }

        if ($minWageYearValue || $maxWageYearValue) {
            // 年薪
            if ($minWageYearValue && $maxWageYearValue) {
                if ($minWageYearValue > $maxWageYearValue) {
                    $this->error($column['minWageYear'], '最高年薪必须大于等于最小年薪');
                }
            }
        }

        if ($minWageMonthValue || $maxWageMonthValue) {
            // 月薪
            if ($minWageMonthValue && $maxWageMonthValue) {
                if ($minWageMonthValue > $maxWageMonthValue) {
                    $this->error($column['minWageMonth'], '最高月薪必须大于等于最小月薪');
                }
            }
        }

        return $column;
    }

    /**
     * 处理投递相关数据配置
     * @param $column
     */
    private function cleanApply($column)
    {
        $applyType          = $column['applyType']['value'] ?? '';
        $applyAddress       = $column['applyAddress']['value'] ?? '';
        $extraNotifyAddress = $column['extraNotifyAddress']['value'] ?? '';
        //报名方式不为空
        if (!empty($applyType)) {
            $applyTypeArr = explode(',', $applyType);
            $isApplyAttr  = in_array('平台投递', $applyTypeArr);
            if ($isApplyAttr && count($applyTypeArr) != 1) {
                $this->error($column['applyType'], '填写了平台投递则不允许填写其他');
            }
            if ($isApplyAttr && $this->platformType == self::PLATFORM_COMPANY) {
                $this->error($column['applyType'], '填写错误');
            }
            if ($isApplyAttr && !empty($applyAddress)) {
                $this->error($column['applyType'], '填写了平台投递则不允许填写投递地址');
            }
            if (!$isApplyAttr && !empty($extraNotifyAddress)) {
                $this->error($column['applyType'], '报名方式不是平台投递，邮箱通知地址不可填写');
            }
            //导入时候数据存在报名方式与投递方式共存--特殊处理
            if ($isApplyAttr) {
                if (!empty($extraNotifyAddress)) {
                    BaseJob::checkEmailApplyAddress($extraNotifyAddress);
                }
                $column['deliveryWay']          = self::TOTAL_LINE_LIST[$this->pushDeliveryWay];
                $column['deliveryWay']['value'] = BaseJob::DELIVERY_WAY_PLATFORM;
                $column['applyType']['value']   = '';
            } else {
                $dataArr  = explode(';', $applyType);
                $dataList = [];
                foreach ($dataArr as $item) {
                    $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_SIGN_UP, $item);
                    if (!$dataId) {
                        $this->error($column['applyType'], '填写错误:' . $item . '不存在');
                    }
                    array_push($dataList, $dataId);
                }
                $applyTypeArr                 = array_unique($dataList);
                $column['applyType']['value'] = implode(',', $applyTypeArr);
                if ($applyAddress) {
                    if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                        BaseJob::checkEmailApplyAddress($applyAddress);
                        $column['deliveryWay']          = self::TOTAL_LINE_LIST[$this->pushDeliveryWay];
                        $column['deliveryWay']['value'] = BaseJob::DELIVERY_WAY_EMAIL;
                    } else {
                        if (!ValidateHelper::isUrl($applyAddress)) {
                            $this->error($column['applyAddress'], '格式错误');
                        }
                        $column['deliveryWay']          = self::TOTAL_LINE_LIST[$this->pushDeliveryWay];
                        $column['deliveryWay']['value'] = BaseJob::DELIVERY_WAY_LINK;
                    }
                }
            }
        } else {
            if ($applyAddress) {
                $this->error($column['applyType'], '报名方式为空，投递地址不可填写');
            }
            if ($extraNotifyAddress) {
                $this->error($column['applyType'], '报名方式为空，邮箱通知地址不可填写');
            }
        }

        return $column;
    }

    private function cleanMajorAi($line)
    {
        $data = $line['value'];
        if (!$data) {
            $line['value'] = '';

            return $line;
        }

        if ($this->searchMajorAiList[$data]) {
            $line['value'] = $this->searchMajorAiList[$data];
        } else {
            $majorData = MajorAi::recognition($data, BaseMajorAiLog::TYPE_EXCEL);

            if (!$majorData) {
                return $line;
            }

            $majorIds = [];
            foreach ($majorData['majorIds'] as $k => $id) {
                $level1 = Major::findOneVal(['id' => $id], 'level');
                // 过滤一级学科
                if ($level1 == 1) {
                    unset($majorData['majorIds'][$k]);
                } else {
                    $majorIds[] = $id;
                }
            }

            $line['value']                  = $majorIds;
            $this->searchMajorAiList[$data] = $majorIds;
        }

        return $line;
    }

    private function cleanWelfare($line)
    {
        $data = $line['value'];

        if (!$data) {
            $line['value'] = '';

            return $line;
        }

        // 可以多选的
        $data = str_replace(';', '；', $line['value']);
        // 去掉最右边的；
        $data = rtrim($data, '；');

        $dataArr  = explode('；', $data);
        $dataList = [];
        foreach ($dataArr as $item) {
            if (!$item) {
                $this->error($line, '填写错误');
            }
            // 在里面了,就不需要再次去拿,重复了
            if ($this->searchWelfare[$item]) {
                $dataList[] = $this->searchWelfare[$item];
            } else {
                // 找这个code在系统里面是否存在
                $dataId = BaseWelfareLabel::getIdByName($item);
                if (!$dataId) {
                    $this->error($line, '填写错误:' . $item . '不存在');
                }
                $dataList[]                 = $dataId;
                $this->searchWelfare[$item] = $dataId;
            }
        }

        $line['value'] = array_unique($dataList);

        return $line;
    }

    private function cleanDuty($line)
    {
        return $line;
    }

    private function cleanRequirement($line)
    {
        return $line;
    }

    private function cleanRemark($line)
    {
        return $line;
    }

    private function cleanNature($line)
    {
        $data = $line['value'];

        if (!$data) {
            $line['value'] = '';

            return $line;
        }

        if ($this->searchNature[$data]) {
            $line['value'] = $this->searchNature[$data];
        } else {
            // 找这个code在系统里面是否存在
            $dataId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_NATURE, $data);

            if (!$dataId) {
                $this->error($line, '填写错误' . $data . '不存在');
            }
            $line['value']             = $dataId;
            $this->searchNature[$data] = $dataId;
        }

        return $line;
    }

    //第29列 学历限制
    private function cleanDeliveryLimitTypeEducation($line)
    {
        if ($line['value'] == '是') {
            $line['value'] = '1';

            return $line;
        } else {
            $line['value'] = '';

            return $line;
        }
    }

    //第30列 投递类型
    private function cleanDeliveryType($line)
    {
        if ($line['value'] == '站外投递') {
            $line['value'] = '1';

            return $line;
        } elseif ($line['value'] == '站内投递') {
            $line['value'] = '2';

            return $line;
        } else {
            $line['value'] = '';

            return $line;
        }
    }

    //第28列 应聘材料限制
    private function cleanDeliveryLimitTypeFile($line)
    {
        if ($line['value'] == '是') {
            $line['value'] = '2';

            return $line;
        } else {
            $line['value'] = '';

            return $line;
        }
    }

    private function checkRule($data)
    {
        $rules             = $data['rules'];
        $value             = $data['value'];
        $this->columnIndex = $data['columnIndex'];
        $this->lineIndex   = $data['lineIndex'];
        foreach ($rules as $rule) {
            switch ($rule) {
                // 不能为空
                case 'require':
                    if (empty($value)) {
                        $this->error($data, '不能为空');
                    }

                    return true;
                default:
                    return false;
            }
        }
    }

    private function checkColumnName($columnName, $name)
    {
        if ($columnName != $name) {
            throw new Exception('请使用最新模版导入数据');
        }

        return true;
    }

    /**
     * 单条数据检查,这里就简单的检查,最终还是要去添加的service里面做
     */
    private function checkOneData($line)
    {
        $data = $line['value'];
    }

    private function error($data, $error)
    {
        $message = '第' . $data['columnIndex'] . '行第' . $data['lineIndex'] . '列' . $data['name'] . ':' . $error;

        throw new Exception($message);
    }
}