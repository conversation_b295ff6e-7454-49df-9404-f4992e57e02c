<?php
/**
 * create user：shannon
 * create time：2024/11/26 上午9:07
 */
namespace common\service\chat;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseChatRoom;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseMember;

class CommonRule extends BaseService
{
    //按钮类型：1=不显示;2=聊一聊;3=继续聊
    const BUTTON_TYPE_NOT_SHOW           = 1;
    const BUTTON_TYPE_NOT_SHOW_NAME      = '';
    const BUTTON_TYPE_CHAT               = 2;
    const BUTTON_TYPE_CHAT_NAME          = PLATFORM == 'PC' ? '聊一聊' : '直聊';
    const BUTTON_TYPE_CONTINUE_CHAT      = 3;
    const BUTTON_TYPE_CONTINUE_CHAT_NAME = '继续聊';
    const BUTTON_TYPE_LIST               = [
        self::BUTTON_TYPE_NOT_SHOW      => self::BUTTON_TYPE_NOT_SHOW_NAME,
        self::BUTTON_TYPE_CHAT          => self::BUTTON_TYPE_CHAT_NAME,
        self::BUTTON_TYPE_CONTINUE_CHAT => self::BUTTON_TYPE_CONTINUE_CHAT_NAME,

    ];

    /**
     * 检查直聊按钮的权限
     * 规则： 按钮类型：1=不显示;2=聊一聊;3=继续聊
     * 2.1 新增状态①——“不显示按钮”逻辑：
     * 以下情况之一，进入当前页面时，不显示【聊一聊】按钮（此时不可向单位发起聊天）：
     * （1）合作单位、且职位投递方式=网址投递的职位；
     * （2）非合作单位的职位；
     * （3）双会模版公告下的职位；
     * 2.2 可用状态下，新增“【聊一聊】/【继续聊】”按钮文案显示逻辑：
     * 对于合作单位、且职位投递方式=邮箱投递/平台投递的职位——
     * （1）默认显示【聊一聊】按钮；
     * 点击时，判断逻辑更新见
     * （2）若求职者C已登录，且和该职位联系人B1的聊天会话中，正在沟通的职位为当前职位时，显示【继续聊】按钮；点击时，新页面打开【我的直聊】页面，打开与B1的聊天会话窗口：
     */
    public static function checkButtonAuth($jobId, $memberId = 0)
    {
        //获取职位信息
        $jobModel = BaseJob::findOne($jobId);
        // 职位联系人
        $contactMemberId = BaseCompanyMemberInfo::findOne(BaseJobContact::findOne(['job_id' => $jobId])->company_member_info_id)->member_id;
        //获取单位信息
        $companyModel = BaseCompany::findOne($jobModel->company_id);
        //定义按钮类型
        $buttonType = self::BUTTON_TYPE_NOT_SHOW;
        //是否沟通中
        $isChatCommunication = false;
        if ($memberId > 0) {
            //登录了那就看下
            $isChatCommunication = BaseChatRoom::find()
                ->where([
                    'current_job_id'    => $jobId,
                    'resume_member_id'  => $memberId,
                    'company_member_id' => $contactMemberId,
                ])
                ->exists();
        }

        //判断是否登录
        if ($jobModel->announcement_id > 0) {
            $announcementModel = BaseAnnouncement::findOne($jobModel->announcement_id);
            if ($announcementModel->template_id != BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                //非双会模版公告
                if ($companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && ($jobModel->delivery_type == BaseJob::DELIVERY_TYPE_INSIDE || ($jobModel->delivery_type == BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT && $announcementModel->delivery_type == BaseAnnouncement::DELIVERY_TYPE_INSIDE))) {
                    $buttonType = $isChatCommunication ? self::BUTTON_TYPE_CONTINUE_CHAT : self::BUTTON_TYPE_CHAT;
                }
            }
        } else {
            //纯职位
            if ($companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $jobModel->delivery_type == BaseJob::DELIVERY_TYPE_INSIDE) {
                $buttonType = $isChatCommunication ? self::BUTTON_TYPE_CONTINUE_CHAT : self::BUTTON_TYPE_CHAT;
            }
        }

        return [
            'isShow'     => $buttonType == 1 ? 2 : 1,
            'buttonType' => $buttonType,
            'buttonName' => self::BUTTON_TYPE_LIST[$buttonType],
        ];
    }

    /**
     * 聊天活跃规则
     * （1）刚刚活跃：活跃日期为当前日期（即今日）；
     * （2）昨日活跃：活跃日期为当前日期的前一天（即昨日）；
     * （3）3天内活跃：活跃日期为当前日期的前两天（即前日）；
     * （4）本周活跃：活跃日期在当前日期的前2天至前6天之间（即 当前日-6 ≤ 活跃日期 < 当前日-2）；
     * （5）两周内活跃：活跃日期在当前日期的前6天至前13天之间（即 当前日-13 ≤ 活跃日期 < 当前日-6）；
     * （6）本月活跃：活跃日期在当前日期的前13天至前29天之间（即 当前日-29 ≤ 活跃日期 < 当前日-13）；
     * （7）不显示文案：活跃日期在当前日期的前29天之前（即 活跃日期 < 当前日-29），不显示。
     *
     * 2、聊天在线状态（即“小绿点”显示规则，显示位置以UI为准）——
     * 若当前职位联系人的单位账号，最近一次活跃时间在当前日期的7天内（即含当前日的7天之内，满足“当前日-6 ≤ 活跃日期 ≤ 当前日”），则显示“小绿点”在线状态样式；否则不展示；
     * PS：此时，小绿点会和“刚刚活跃/昨日活跃/3天内活跃/本周活跃”的活跃标签文案一起出现。
     */
    public static function checkChatActive($jobId)
    {
        //获取职位信息
        $jobModel = BaseJob::findOne($jobId);
        // 职位联系人ID
        $contactMemberId = BaseCompanyMemberInfo::findOne(BaseJobContact::findOne(['job_id' => $jobModel->id])->company_member_info_id)->member_id;
        //获取职位联系人账号信息
        $contactMemberInfo = BaseMember::findOne($contactMemberId);
        $lastActiveDate    = $contactMemberInfo ? date('Y-m-d', strtotime($contactMemberInfo->last_active_time)) : '';
        [
            $activeType,
            $activeName,
            $isOnline,
        ] = BaseMember::getActiveText($lastActiveDate);

        return [
            'activeType'   => $activeType,
            'activeName'   => $activeName,
            'isOnline'     => $isOnline,
            'isOnlineText' => $isOnline ? '在线' : '',
        ];
    }
}