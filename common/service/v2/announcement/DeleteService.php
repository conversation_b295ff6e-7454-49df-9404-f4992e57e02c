<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseOffSiteJobApply;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use yii\base\Exception;

/**
 * 公告删除
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class DeleteService extends BaseService
{
    public $jobList;

    public function __construct()
    {
        parent::__construct();
    }

    public function validate()
    {
        if (empty($this->params['announcementId'])) {
            throw new \Exception('参数缺失');
        }

        if ($this->announcementInfo->create_type == BaseAnnouncement::CREATE_TYPE_ADMIN) {
            if ($this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $this->articleInfo->first_release_time != TimeHelper::ZERO_TIME) {
                throw new MessageException('合作单位有审核通过历史的公告，不可删除');
            }
        }

        if ($this->announcementInfo->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new MessageException('待审核公告，不可删除');
        }

        $this->jobList = BaseJob::find()
            ->select('id,status,audit_status')
            ->where(['announcement_id' => $this->announcementId])
            ->asArray()
            ->all();

        foreach ($this->jobList as $item) {
            if ($this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                $jobApply = BaseJobApply::findOne(['job_id' => $item['id']]);
            } else {
                $jobApply = BaseOffSiteJobApply::findOne(['job_id' => $item['id']]);
            }
            if ($jobApply) {
                throw new MessageException('该公告的职位存在投递数据，不可删除');
            }
            if ($item['audit_status'] == BaseJob::STATUS_WAIT_AUDIT) {
                throw new MessageException('待审核职位，不可删除');
            }
        }

        return $this;
    }

    /**
     * 删除逻辑
     * @param $params
     */
    public function delete($params)
    {
        $this->params = $params;
        $this->initInfo();
        $this->setAnnouncement($this->params['announcementId'])
            ->setCompany($this->announcementInfo->company_id)
            ->validate()
            ->run()
            ->log(BaseAnnouncementLog::TYPE_DELETE)
            ->afterAnnouncementUpdateJob();
    }

    /**
     * 批量删除逻辑
     * @param $params
     */
    public function deleteBatch($params)
    {
        $successIds  = [];
        $failMessage = [];
        $this->initInfo();
        $ids = StringHelper::changeStrToFilterArr($params['announcementId'] ?? '');
        foreach ($ids as $id) {
            try {
                $this->params['announcementId'] = $id;
                $this->validate()
                    ->setAnnouncement($id)
                    ->run()
                    ->log(BaseAnnouncementLog::TYPE_DELETE_BATCH)
                    ->afterAnnouncementUpdateJob();

                if (!$this->announcementId) {
                    $successIds[] = $this->announcementId;
                }
            } catch (MessageException $exception) {
                $failMessage[] = '公告ID：' . $id . '，删除失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'successIds'  => $successIds,
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    public function run()
    {
        $this->announcementInfo->status = BaseAnnouncement::STATUS_DELETE;
        if (!$this->announcementInfo->save()) {
            throw new Exception($this->announcementInfo->getFirstErrorsMessage());
        }

        $this->articleInfo->is_delete   = BaseArticle::STATUS_ACTIVE;
        $this->articleInfo->status      = BaseArticle::STATUS_DELETE;
        $this->articleInfo->delete_time = CUR_DATETIME;
        if (!$this->articleInfo->save()) {
            throw new Exception($this->articleInfo->getFirstErrorsMessage());
        }
        foreach ($this->jobList as $item) {
            $jobDeleteService = new \common\service\v2\job\DeleteService();
            $jobDeleteService->setPlatform($this->operationPlatform)
                ->delete(['jobId' => $item['id']]);
        }

        // 记录一下log数据
        BaseAnnouncementHandleLog::createInfo([
            'add_time'        => CUR_DATETIME,
            'job_id'          => 0,
            'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_DELETE),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([], JSON_UNESCAPED_UNICODE),
            'handle_after'    => json_encode([
                '操作' => '删除公告',
            ], JSON_UNESCAPED_UNICODE),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->announcementId,
        ]);

        return $this;
    }
}