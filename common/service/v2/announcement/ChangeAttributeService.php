<?php
/**
 * create user：伍彦川
 * create time：2025/6/11 09:51
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use yii\base\Exception;

class ChangeAttributeService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    public function run($params)
    {
        $failMessage    = [];
        $announcementId = 0;

        $this->params                   = $params;
        $this->params['attribute']      = StringHelper::changeStrToFilterArr($this->params['attribute'] ?? []);
        $this->params['announcementId'] = StringHelper::changeStrToFilterArr($this->params['announcementId'] ?? []);
        $this->initInfo();

        foreach ($this->params['announcementId'] as $announcementId) {
            try {
                $this->setAnnouncement($announcementId);

                $oldAttribute = BaseArticleAttribute::getArticleAttributeName($this->articleId);

                if ($this->announcementInfo->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
                    throw new MessageException('公告id=' . $announcementId . '处于待审核状态，不支持编辑');
                }

                $this->baseCheckMemberPackage();

                // 更新文档属性
                BaseArticleAttribute::deleteAttribute($this->articleInfo->id);
                foreach ($this->params['attribute'] as $type) {
                    $articleAttributeModel             = new BaseArticleAttribute;
                    $articleAttributeModel->type       = $type;
                    $articleAttributeModel->article_id = $this->articleInfo->id;
                    $articleAttributeModel->sort_time  = CUR_DATETIME;
                    if (!$articleAttributeModel->save()) {
                        throw new \Exception($articleAttributeModel->getFirstErrorsMessage());
                    }
                }

                $newAttribute = BaseArticleAttribute::getArticleAttributeName($this->articleId);

                // 记录一下log数据
                BaseAnnouncementHandleLog::createInfo([
                    'add_time'        => CUR_DATETIME,
                    'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_CHANGE_ATTR),
                    'handler_type'    => strval($this->params['platformType']),
                    'handler_id'      => $this->params['userId'],
                    'handler_name'    => $this->params['username'],
                    'handle_before'   => json_encode([
                        '属性' => $oldAttribute,
                    ], JSON_UNESCAPED_UNICODE),
                    'handle_after'    => json_encode([
                        '属性' => $newAttribute,
                    ], JSON_UNESCAPED_UNICODE),
                    'ip'              => IpHelper::getIpInt(),
                    'announcement_id' => $announcementId,
                ]);

                $this->afterAnnouncementUpdateJob();
            } catch (MessageException $exception) {
                $failMessage[] = '公告ID：' . $announcementId . '编辑属性失败，原因：' . $exception->getMessage();
            }
        }

        return $failMessage;
    }
}