<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use yii\base\Exception;
use Yii;

/**
 * 公告详情服务类
 * 处理公告相关的数据获取和处理
 */
class DetailService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取公告详情
     * @return array
     * @throws Exception
     */
    public function run()
    {
        $announcementId = Yii::$app->request->get('id');
        if (!$announcementId) {
            throw new Exception('参数错误');
        }
        $this->announcementId = $announcementId;
        $this->getAnnouncementInfo();
        $this->processAnnouncementInfo();

        return [
            'baseInfo' => $this->announcementInfo,
            'jobList'  => $this->getJobList(),
            'fileList' => $this->getFileList(),
        ];
    }

    /**
     * 获取公告基本信息
     * @throws Exception
     */
    private function getAnnouncementInfo()
    {
        $this->announcementInfo = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = a.article_id')
            ->select([
                'a.id',
                'a.company_id as companyId',
                'a.period_date as periodDate',
                'a.major_ids as majorIds',
                'a.apply_type as applyType',
                'a.apply_address as applyAddress',
                'a.extra_notify_address as extraNotifyAddress',
                'a.delivery_way as deliveryWay',
                'a.delivery_type as deliveryType',
                'a.title',
                'a.audit_status as auditStatus',
                'a.article_id as articleId',
                'a.file_ids as fileIds',
                'a.is_attachment_notice as isAttachmentNotice',
                'a.add_time as addTime',
                'a.status',
                'a.address_hide_status as addressHideStatus',
                'a.sub_title as subTitle',
                'a.highlights_describe as highlightsDescribe',
                'ar.home_column_id as homeColumnId',
                'ar.home_sub_column_ids as homeSubColumnIds',
                'ar.release_time as releaseTime',
                'ar.content',
                'ar.first_release_time as firstReleaseTime',
            ])
            ->where(['a.id' => $this->announcementId])
            ->asArray()
            ->one();

        if (!$this->announcementInfo) {
            throw new Exception('公告不存在');
        }
    }

    /**
     * 处理公告信息
     */
    private function processAnnouncementInfo()
    {
        $companyInfo                                   = BaseCompany::findOne($this->announcementInfo['companyId']);
        $this->announcementInfo['companyName']         = $companyInfo->full_name;
        $this->announcementInfo['companyDeliveryType'] = $companyInfo->delivery_type;
        $this->announcementInfo['isCooperation']       = $companyInfo->is_cooperation;

        // 处理栏目信息
        $this->announcementInfo['homeColumnTxt']    = $this->getHomeColumnText($this->announcementInfo['homeColumnId']);
        $this->announcementInfo['homeSubColumnTxt'] = $this->getHomeSubColumnText($this->announcementInfo['homeSubColumnIds']);

        // 处理地址信息
        $this->announcementInfo['applyAddress']       = $this->announcementInfo['applyAddress'] ?: '-';
        $this->announcementInfo['extraNotifyAddress'] = $this->announcementInfo['extraNotifyAddress'] ?: '-';

        // 处理职位相关信息
        $this->announcementInfo['jobNum']    = BaseJob::getAnnouncementJobAmount($this->announcementId,
            $this->announcementInfo['status']);
        $this->announcementInfo['amountNum'] = BaseJob::getAnnouncementJobRecruitAmount($this->announcementId,
            $this->announcementInfo['status']);

        // 处理地区信息
        $this->announcementInfo['areaProvinceTxt'] = BaseAnnouncement::getAllProvinceName($this->announcementId,
            $this->announcementInfo['status']) ?: '-';
        $this->announcementInfo['areaCityTxt']     = BaseAnnouncement::getAllCityName($this->announcementId,
            $this->announcementInfo['status']) ?: '-';

        // 处理教育信息
        $this->announcementInfo['educationTxt'] = $this->getEducationText();

        // 处理专业信息
        $this->announcementInfo['majorTxt'] = BaseAnnouncement::getAllMajorName($this->announcementId, 'text',
            $this->announcementInfo['status']) ?: '-';

        // 处理报名方式
        $this->announcementInfo['applyTypeTxt'] = $this->getApplyTypeText();

        // 处理时间信息
        $this->announcementInfo['releaseTime'] = $this->getReleaseTime();

        // 处理截止日期
        $this->announcementInfo['periodDate'] = $this->getPeriodDate();

        // 处理栏目文本
        $this->announcementInfo['columnTxt'] = BaseArticleColumn::getArticleColumn($this->announcementInfo['articleId']);

        // 处理附件通知状态
        $this->announcementInfo['isAttachmentNoticeTxt'] = BaseAnnouncement::IS_ATTACHMENT_NOTICE_LIST[$this->announcementInfo['isAttachmentNotice']] ?? '-';

        // 处理地址隐藏状态
        $this->announcementInfo['addressHideStatusText'] = BaseAnnouncement::ADDRESS_HIDE_STATUS_TEXT[$this->announcementInfo['addressHideStatus']];
    }

    /**
     * 获取主栏目文本
     * @param int $columnId
     * @return string
     */
    private function getHomeColumnText($columnId)
    {
        return BaseHomeColumn::findOneVal(['id' => $columnId], 'name') ?: '-';
    }

    /**
     * 获取副栏目文本
     * @param string $subColumnIds
     * @return string
     */
    private function getHomeSubColumnText($subColumnIds)
    {
        if (!$subColumnIds) {
            return '-';
        }

        $homeSubColumnIdsArr = explode(',', $subColumnIds);
        $homeSubColumnArr    = BaseHomeColumn::find()
            ->select('name')
            ->where(['id' => $homeSubColumnIdsArr])
            ->asArray()
            ->all();

        $subColumns = array_column($homeSubColumnArr, 'name');

        return !empty($subColumns) ? implode(',', $subColumns) : '-';
    }

    /**
     * 获取教育要求文本
     * @return string
     */
    private function getEducationText()
    {
        $minEducation = BaseAnnouncement::getMinEducationName($this->announcementId, $this->announcementInfo['status']);

        return !empty($minEducation) ? $minEducation : '-';
    }

    /**
     * 获取报名方式文本
     * @return string
     */
    private function getApplyTypeText()
    {
        if ($this->announcementInfo['deliveryType'] == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
            if (!$this->announcementInfo['applyType']) {
                return '-';
            }

            $applyTypeList   = explode(',', $this->announcementInfo['applyType']);
            $applyTypeTxtArr = [];

            foreach ($applyTypeList as $item) {
                $item_name = BaseDictionary::getSignUpName($item);
                if (!empty($item_name)) {
                    $applyTypeTxtArr[] = $item_name;
                }
            }

            return count($applyTypeTxtArr) > 0 ? implode(',', $applyTypeTxtArr) : '-';
        }

        return $this->announcementInfo['deliveryType'] == BaseAnnouncement::DELIVERY_TYPE_INSIDE ? '站内投递' : '-';
    }

    /**
     * 获取发布时间
     * @return string
     */
    private function getReleaseTime()
    {
        return $this->announcementInfo['firstReleaseTime'] == TimeHelper::ZERO_TIME ? $this->announcementInfo['addTime'] : $this->announcementInfo['releaseTime'];
    }

    /**
     * 获取截止日期
     * @return string
     */
    private function getPeriodDate()
    {
        return $this->announcementInfo['periodDate'] == TimeHelper::ZERO_TIME ? '详见正文' : date('Y-m-d',
            strtotime($this->announcementInfo['periodDate']));
    }

    /**
     * 获取职位列表
     * @return array
     */
    private function getJobList()
    {
        if ($this->announcementInfo['status'] == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($this->announcementInfo['status'] == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $jobList = BaseJob::find()
            ->where(['announcement_id' => $this->announcementId])
            ->andWhere($where)
            ->select([
                'id',
                'name',
                'code',
                'major_id as majorId',
                'education_type as educationType',
                'amount',
                'department',
                'is_negotiable as isNegotiable',
                'min_wage as minWage',
                'max_wage as maxWage',
                'wage_type as wageType',
                'province_id as provinceId',
                'city_id as cityId',
                'audit_status as auditStatus',
                'period_date as periodDate',
                'status',
                'delivery_type as deliveryType',
                'delivery_way as deliveryWay',
                'apply_address as applyAddress',
            ])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        foreach ($jobList as &$item) {
            $applyInfo = [];
            if ($item['deliveryType'] != 0) {
                $applyInfo[]       = isset(BaseJob::DELIVERY_WAY_NAME[$item['deliveryWay']]) && !empty(BaseJob::DELIVERY_WAY_NAME[$item['deliveryWay']]) ? BaseJob::DELIVERY_WAY_NAME[$item['deliveryWay']] : '-';
                $applyInfo[]       = $item['applyAddress'] ?: '-';
                $item['applyInfo'] = implode('/', $applyInfo);
            } else {
                $applyInfo[]       = isset(BaseAnnouncement::DELIVERY_WAY_NAME[$this->announcementInfo['deliveryWay']]) && !empty(BaseAnnouncement::DELIVERY_WAY_NAME[$this->announcementInfo['deliveryWay']]) ? BaseJob::DELIVERY_WAY_NAME[$this->announcementInfo['deliveryWay']] : '-';
                $applyInfo[]       = $this->announcementInfo['applyAddress'] ?: '-';
                $item['applyInfo'] = implode('/', $applyInfo);
            }
            //薪资wage_id回显
            if ($item['isNegotiable'] <> 1) {
                $item['wageId'] = (string)BaseJob::getWageId($item['minWage'], $item['maxWage']);
            }
            if ($item['periodDate'] == TimeHelper::ZERO_TIME) {
                $item['periodDate'] = '详见正文';
            } else {
                $item['periodDate'] = date('Y-m-d', strtotime($item['periodDate']));
            }

            // 空转'-'
            $item['code']       = StringHelper::isEmpty($item['code']);
            $item['department'] = StringHelper::isEmpty($item['department']);
            $information        = [];
            if ($item['cityId']) {
                $information[] = BaseArea::getAreaName($item['cityId']) ?: '-';
            }
            if ($item['amount']) {
                $information[] = "招{$item['amount']}人";
            }
            if ($item['educationType']) {
                $information[] = BaseDictionary::getEducationName($item['educationType']) ?: '-';
            }
            if (!$item['minWage'] && !$item['maxWage']) {
                $information[] = '面议';
            } else {
                $information[] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']) ?: '-';
            }
            if ($item['majorId']) {
                $information[] = BaseMajor::getAllMajorNameRedis(explode(',', $item['majorId']));
            }
            $item['information']          = implode(' | ', $information);
            $item['jobContact']           = BaseJob::getJobContact($item['id']);
            $item['jobContactSynergy']    = BaseJob::getJobContactSynergy($item['id']);
            $item['jobContactSynergyNum'] = count($item['jobContactSynergy']);

            unset($item['majorId'], $item['provinceId'], $item['cityId'], $item['auditStatus'], $item['educationType'], $item['isNegotiable']);
        }

        return $jobList;
    }

    /**
     * 获取文件列表
     * @return array
     */
    private function getFileList()
    {
        if (empty($this->announcementInfo['fileIds'])) {
            return [];
        }

        return BaseAnnouncement::getAppendixList($this->announcementInfo['fileIds']) ?: [];
    }
}