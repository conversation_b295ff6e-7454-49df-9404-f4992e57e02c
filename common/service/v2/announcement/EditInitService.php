<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMajor;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use Yii;
use yii\base\Exception;

/**
 * 公告编辑初始化
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class EditInitService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 公告编辑初始化
     * @throws Exception
     */
    public function run()
    {
        $announcementId = Yii::$app->request->get('id');
        if (!$announcementId) {
            throw new Exception('参数错误');
        }
        $announcementInfo = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = a.article_id')
            ->select([
                'a.id as announcementId',
                'a.article_id as articleId',
                'a.title',
                'a.company_id as companyId',
                'a.period_date as periodDate',
                'a.apply_type as applyType',
                'a.apply_address as applyAddress',
                'a.template_id as templateId',
                'a.audit_status as auditStatus',
                'a.file_ids as fileIds',
                'a.delivery_type as deliveryType',
                'a.delivery_way as deliveryWay',
                'a.is_attachment_notice as isAttachmentNotice',
                'a.extra_notify_address as extraNotifyAddress',
                'a.status',
                'a.address_hide_status as addressHideStatus',
                'a.sub_title as subTitle',
                'a.highlights_describe as highlightsDescribe',
                'a.background_img_file_id as backgroundImgFileId',
                'a.background_img_file_id_2 as backgroundImgFileId2',
                'a.background_img_file_id_3 as backgroundImgFileId3',
                'a.background_img_file_type as backgroundImgFileType',
                'a.activity_job_content as activityJobContent',
                'ar.content',
                'ar.home_column_id as homeColumnId',
                'ar.home_sub_column_ids as homeSubColumnIds',
                'ar.cover_thumb as coverThumb',
                'ar.seo_description as seoDescription',
                'ar.seo_keywords as seoKeywords',
                'ar.recommend_ids as recommendIds',
                'ar.tag_ids as tagIds',
            ])
            ->where(['a.id' => $announcementId])
            ->asArray()
            ->one();

        if (!$announcementInfo) {
            throw new Exception('公告不存在');
        }
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->initInfo();
            if ($this->companyInfo->id != $announcementInfo['companyId']) {
                throw new Exception('您的单位不存在该公告数据');
            }
        }
        if ($announcementInfo['auditStatus'] == BaseAnnouncement::STATUS_AUDIT_AWAIT || $announcementInfo['status'] == BaseAnnouncement::STATUS_OFFLINE || $announcementInfo['status'] == BaseAnnouncement::STATUS_DELETE) {
            throw new Exception('待审核或下线、删除状态不支持编辑');
        }
        unset($announcementInfo['auditStatus']);
        // 文档属性
        $comboAttributeInfo = BaseArticleAttribute::getComboAttributeInfo($announcementInfo['articleId']);
        $announcementInfo   = array_merge($announcementInfo, $comboAttributeInfo);

        // 获取调用栏目
        $announcementInfo['columnTxt']       = BaseArticleColumn::getArticleColumn($announcementInfo['articleId']);
        $announcementInfo['deliveryTypeTxt'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$announcementInfo['deliveryType']];
        //投递方式处理
        if (in_array($announcementInfo['deliveryWay'], BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $announcementInfo['deliveryWay'] = (string)BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($announcementInfo['deliveryWay'])) {
            $announcementInfo['deliveryWay'] = '';
        }
        if ($announcementInfo['periodDate'] == TimeHelper::ZERO_TIME) {
            $announcementInfo['periodDate'] = '';
        }
        if (!$announcementInfo['templateId']) {
            $announcementInfo['templateId'] = '';
        }
        if (empty($announcementInfo['deliveryType'])) {
            $announcementInfo['deliveryType'] = '';
        }
        $companyInfo                                = BaseCompany::findOne($announcementInfo['companyId']);
        $announcementInfo['companyName']            = $companyInfo->full_name;
        $announcementInfo['companyDeliveryType']    = $companyInfo->delivery_type;
        $announcementInfo['companyDeliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$companyInfo->delivery_type];
        $announcementInfo['isCooperation']          = (string)$companyInfo->is_cooperation;
        $announcementInfo['companyNature']          = $companyInfo->nature;
        $announcementInfo['companyNatureTxt']       = BaseDictionary::getCompanyNatureName($companyInfo->nature);
        $announcementInfo['companyType']            = $companyInfo->type;
        $announcementInfo['companyTypeTxt']         = BaseDictionary::getCompanyTypeName($companyInfo->type);
        //背景图
        $announcementInfo['backgroundImg'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId']],
            'path'));
        //背景图2
        $announcementInfo['backgroundImg2'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId2']],
            'path'));
        //背景图3
        $announcementInfo['backgroundImg3'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId3']],
            'path'));
        //公告关联活动
        $activityAnnouncementList = BaseHwActivityAnnouncement::find()
            ->where(['announcement_id' => $announcementId])
            ->select(['activity_id'])
            ->column();

        $announcementInfo['activityAnnouncement'] = implode(',', $activityAnnouncementList);
        // 公告下职位列表
        $jobList = BaseJob::find()
            ->select([
                'id as jobId',
                'name',
                'amount',
                'status',
                'department',
                'major_id as majorId',
                'education_type as educationType',
                'province_id as provinceId',
                'city_id as cityId',
                'audit_status as auditStatus',
                'wage_type as wageType',
                'min_wage as minWage',
                'max_wage as maxWage',
            ])
            ->where([
                'announcement_id' => $announcementId,
                'status'          => [
                    BaseJob::STATUS_WAIT,
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('status desc,refresh_time desc,id desc')
            ->asArray()
            ->all();

        foreach ($jobList as &$item) {
            //虚拟字段保持一临时职位字段返回字段一直性
            $item['id']     = 0;//这里的Id指的是临时职位表的ID
            $item['isTemp'] = BaseJobTemp::IS_TEMP_NO;
            //canDel true显示 false 隐藏
            $item['statusTxt']      = BaseJob::JOB_STATUS_NAME[$item['status']];
            $item['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[$item['auditStatus']];
            if ($item['status'] != BaseJob::STATUS_WAIT) {
                $item['canDel'] = false;
            } else {
                $item['canDel'] = true;
            }
            $item['isCooperation']        = $companyInfo->is_cooperation;
            $item['jobContact']           = BaseJob::getJobContact($item['jobId']);
            $item['jobContactSynergy']    = BaseJob::getJobContactSynergy($item['jobId']);
            $item['jobContactSynergyNum'] = count($item['jobContactSynergy']);
            $information                  = [];
            if ($item['cityId']) {
                $information[] = BaseArea::getAreaName($item['cityId']);
            }
            if ($item['amount']) {
                $information[] = "招{$item['amount']}人";
            }
            if ($item['educationType']) {
                $information[] = BaseDictionary::getEducationName($item['educationType']);
            }
            if (!$item['minWage'] && !$item['maxWage']) {
                $information[] = '面议';
            } else {
                $information[] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']) ?: '-';
            }
            $item['majorTitle'] = '';
            if ($item['majorId']) {
                $item['majorTitle'] = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
                $information[]      = $item['majorTitle'];
            }
            $item['information'] = implode(' | ', $information);
            unset($item['amount'], $item['educationType'], $item['provinceId'], $item['cityId'], $item['minWage'], $item['maxWage'], $item['wageType']);
        }

        //公告职位附件
        if (!empty($announcementInfo['fileIds'])) {
            $fileList = BaseAnnouncement::getAppendixList($announcementInfo['fileIds']);
        }

        return [
            'announcementInfo'    => $announcementInfo,
            'announcementJobList' => $jobList,
            'fileList'            => $fileList ?? [],
        ];
    }
}