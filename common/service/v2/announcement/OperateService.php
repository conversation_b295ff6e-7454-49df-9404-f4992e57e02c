<?php
/**
 * create user：伍彦川
 * create time：2025/6/4 11:08
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseJob;

class OperateService extends BaseService
{
    private $nameMap = [
        'show'      => [
            'name'  => '显示',
            'class' => 'emerald',
        ],
        'hide'      => [
            'name'  => '隐藏',
            'class' => 'orange',
        ],
        'log'       => [
            'name'  => '日志',
            'class' => 'purple',
        ],
        'edit'      => [
            'name'  => '编辑',
            'class' => 'green',
        ],
        'delete'    => [
            'name'  => '删除',
            'class' => 'red',
        ],
        'republish' => [
            'name'  => '再发布',
            'class' => 'yellow',
        ],
        'offline'   => [
            'name'  => '下线',
            'class' => 'gray',
        ],
        'refresh'   => [
            'name'  => '刷新',
            'class' => 'bluish',
        ],
        'attribute' => [
            'name'  => '属性',
            'class' => 'white',
        ],
        'business'  => [
            'name'  => '业务',
            'class' => 'teal',
        ],
        'log'       => [
            'name'  => '日志',
            'class' => 'purple',
        ],
    ];

    /** @var string 场景值 */
    private $scene;

    public function __construct($params = [])
    {
        parent::__construct();
        if (isset($params['scene'])) {
            $this->scene = $params['scene'];
        }
    }

    private function getBtnDefautl($label, $key, $class, $disabled)
    {
        return [
            'label'    => $label,
            'key'      => $key,
            'class'    => $class,
            // 是否可以点击
            'disabled' => $disabled,
        ];
    }

    public function getBtnInfo($name)
    {
        return $this->nameMap[$name] ?? $this->nameMap['更多'];
    }

    /**
     * 按钮操作逻辑
     * @param $scene                   场景
     * @param $isShow                  显示状态
     * @param $announcementStatus      公告的状态
     * @param $announcementAuditStatus 公告的审核状态
     * @return array
     */
    public function run(
        $scene,
        $isShow,
        $announcementStatus = 0,
        $announcementAuditStatus = 0
    ) {
        $data = [
            'refresh'   => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 2,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 2,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 2,
                    ],
                ],
            ],
            'edit'      => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 2,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 2,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 2,
                    ],
                    // 待发布
                    BaseAnnouncement::STATUS_STAGING => [
                        // 编辑中
                        BaseAnnouncement::STATUS_AUDIT_STAGING => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE  => 1,
                    ],
                ],
            ],
            'delete'    => [
                'data' => [
                    // 待发布
                    BaseAnnouncement::STATUS_STAGING => [
                        // 编辑中
                        BaseAnnouncement::STATUS_AUDIT_STAGING => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE  => 1,
                    ],
                ],
            ],
            'republish' => [
                'data' => [
                    // 下线：显示状态
                    BaseAnnouncement::STATUS_OFFLINE . ':' . BaseArticle::IS_SHOW_YES => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 1,
                    ],
                    // 下线：隐藏状态
                    BaseAnnouncement::STATUS_OFFLINE . ':' . BaseArticle::IS_SHOW_NO  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 2,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 2,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 2,
                    ],
                ],
            ],
            'offline'   => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                ],
            ],
            'show'      => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 1,
                    ],
                ],
            ],
            'hide'      => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 1,
                    ],
                ],
            ],
            'attribute' => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                ],
            ],
            'log'       => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 1,
                    ],
                    // 待发布
                    BaseAnnouncement::STATUS_STAGING => [
                        // 编辑中
                        BaseAnnouncement::STATUS_AUDIT_STAGING => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE  => 1,
                    ],
                ],
            ],
            'business'  => [
                'data' => [
                    // 在线
                    BaseAnnouncement::STATUS_ONLINE  => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                    ],
                    // 下线
                    BaseAnnouncement::STATUS_OFFLINE => [
                        // 审核通过
                        BaseAnnouncement::STATUS_AUDIT_PASS   => 1,
                        // 审核拒绝
                        BaseAnnouncement::STATUS_AUDIT_REFUSE => 1,
                        // 待审核
                        BaseAnnouncement::STATUS_AUDIT_AWAIT  => 1,
                    ],
                ],
            ],
        ];

        // 特殊处理再发布的纯职位
        if ($scene == 'republish') {
            $result = $data[$scene]['data'][$announcementStatus . ':' . $isShow][$announcementAuditStatus] ?? 3;
        } else {
            $result = $data[$scene]['data'][$announcementStatus][$announcementAuditStatus] ?? 3;
        }

        $btnInfo = $this->getBtnInfo($scene);

        // 返回 1:'可用';2:'禁用'；3:找不到就表示不展示了
        $res = $this->getBtnDefautl($btnInfo['name'], $scene, $btnInfo['class'], $result);

        if ($isShow == BaseArticle::IS_SHOW_YES && $scene == 'show') {
            $res['disabled'] = 3;
        }
        if ($isShow == BaseArticle::IS_SHOW_NO && $scene == 'hide') {
            $res['disabled'] = 3;
        }

        return $res;
    }

}