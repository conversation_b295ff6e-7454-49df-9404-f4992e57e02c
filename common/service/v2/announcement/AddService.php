<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\helpers\IpHelper;
use common\helpers\UUIDHelper;
use common\libs\WxWork;
use common\service\companyPackage\CompanyPackageApplication;
use Yii;
use yii\base\Exception;

/**
 * 公告添加
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AddService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 添加公告
     * @throws \common\components\MessageException
     * @throws \yii\base\Exception
     * @throws \yii\base\NotSupportedException
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        $this->initInfo();
        //检验数据合法性
        $this->dataVerify();
        //验证资源套餐
        $this->companySourcePackageCheck();
        //更新公告信息
        $this->saveTable();
        //保存职位信息
        $this->saveJobTable();
        //更新公告的uuid
        $this->updateUuid();
        //after
        $this->after();
        //日志
        $this->handleLog();
        $this->log(BaseAnnouncementLog::TYPE_ADD);

        return [
            'id' => $this->announcementId,
        ];
    }

    /**
     * 处理所有后置逻辑
     */
    private function after()
    {
        $this->updateAnnouncementPiFlag();
        $this->updateAnnouncementMiniAppType();
        $this->updateJobAnnouncementAmount();
        $this->updateAnnouncementBoShiHouTable();
        $this->updateJobAnnouncementRelation();
        $this->updateStatAnnouncementCount();
        $this->runAutoColumnAfter();
    }

    /**
     * 公告日志
     * @return void
     * @throws \yii\base\Exception
     * @throws \yii\base\NotSupportedException
     */
    private function handleLog()
    {
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE,
            'editor_type'     => BaseAnnouncement::TYPE_EDITOR_OTHER,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '创建时间' => $this->announcementInfo->add_time,
                '修改类型' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE],
            ]),
            'handle_after'    => json_encode([
                '创建时间' => $this->announcementInfo->add_time,
                '修改类型' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE],
            ]),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

    /**
     * 检查资源
     * @throws Exception
     */
    private function companySourcePackageCheck()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
                $this->baseCheckMemberPackage();
                if ($this->companyPackageConfigModel->announcement_amount <= 0) {
                    throw new Exception('公告发布数量已达上限');
                }
                $companyPackageApplication = new CompanyPackageApplication();
                $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
                $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                $companyPackageApplication->announcementCreate($this->companyInfo->id, 1, $remark);
            }
        }
    }
}