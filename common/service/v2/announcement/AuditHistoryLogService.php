<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use Yii;
use yii\base\Exception;

/**
 * 公告审核历史日志
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditHistoryLogService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置数据
     * @return $this
     */
    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    /**
     * 公告审核日志
     * @throws Exception
     */
    public function run()
    {
        //获取表单参数
        if (!$this->params) {
            $this->params = Yii::$app->request->get();
        }
        if (!$this->params['id']) {
            throw new Exception('参数错误');
        }
        $query     = BaseAnnouncementHandleLog::find()
            ->where([
                'announcement_id' => $this->params['id'],
                'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_AUDIT,
            ])
            ->select([
                'add_time as addTime',
                'id',
                'handler_name as handlerName',
                'handle_after as handleAfter',
            ]);
        $count     = $query->count();
        $pageSize  = $this->params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages     = self::setPage($count, $this->params['page'], $pageSize);
        $handleLog = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($handleLog as &$log) {
            $log['auditStatusTitle'] = "";
            $log['opinion']          = "";
            $handleAfter             = json_decode($log['handleAfter'], true);
            $log['handle_after']     = $handleAfter;
            if ($handleAfter['审核状态']) {
                $log['auditStatusTitle'] = $handleAfter['审核状态'];
            }
            if ($handleAfter['审核原因']) {
                $log['opinion'] = $handleAfter['审核原因'];
            }
        }

        return [
            'list' => $handleLog,
            'page' => [
                'total' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$pages['page'],
            ],
        ];
    }
}