<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use yii\base\Exception;

/**
 * 职位学历限制
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class LimitEducationService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    public function limitEducation($params)
    {
        $this->initInfo();
        $this->params['isLimit'] = $params['isLimit'] ?? 1;
        $this->setJob($params['jobId'])
            ->validate()
            ->beforeHandleLog()
            ->run()
            ->handleLog()
            ->log(BaseJobLog::TYPE_LIMIT_EDUCATION);
    }

    public function limitEducationBatch($params)
    {
        $failMessage = [];
        $this->initInfo();
        $this->params['isLimit'] = $params['isLimit'] ?? 1;
        $jobIds                  = StringHelper::changeStrToFilterArr($params['jobId'] ?? '');
        foreach ($jobIds as $jobId) {
            try {
                $this->setJob($jobId)
                    ->validate()
                    ->beforeHandleLog()
                    ->run()
                    ->handleLog()
                    ->log(BaseJobLog::TYPE_LIMIT_EDUCATION_BATCH);
            } catch (MessageException $exception) {
                $failMessage[] = '职位ID：' . $jobId . '，执行' . ($this->params['isLimit'] == 1 ? '添加' : '取消') . '学历限制失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function handleLog()
    {
        $this->handleLogData['handle_after'] = json_encode([
            '投递限制' => $this->getDeliveryLimitType(),
            '更新时间' => $this->jobInfo['update_time'],
        ], JSON_UNESCAPED_UNICODE);
        // 消耗套餐,写日志等等
        BaseJobHandleLog::createInfo($this->handleLogData);

        return $this;
    }

    private function run()
    {
        // 拼接字符串
        $deliveryLimitType = explode(',', $this->jobInfo->delivery_limit_type);
        if ($this->params['isLimit'] == 1) {
            $deliveryLimitType[] = BaseJob::DELIVERY_LIMIT_EDUCATION;
        } else {
            $deliveryLimitType = array_diff($deliveryLimitType, [BaseJob::DELIVERY_LIMIT_EDUCATION]);
        }

        // 去重，保存
        sort($deliveryLimitType);
        $deliveryLimitType                  = array_filter(array_unique($deliveryLimitType));
        $this->jobInfo->delivery_limit_type = implode(',', $deliveryLimitType);
        if (!$this->jobInfo->save()) {
            throw new \Exception($this->jobInfo->getFirstErrorsMessage());
        }

        return $this;
    }

    /**
     * 获取职位限制类型名称
     */
    private function getDeliveryLimitType()
    {
        $type                 = [];
        $deliveryLimitTypeArr = explode(',', $this->jobInfo->delivery_limit_type);
        foreach ($deliveryLimitTypeArr as $val) {
            $type[] = BaseJob::DELIVERY_LIMIT_LIST[$val];
        }

        return implode('；', $type);
    }

    private function beforeHandleLog()
    {
        // 记录一下log数据
        $this->handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobInfo->id,
            'handle_type'     => strval($this->params['isLimit'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_LIMIT_EDUCATION : BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_EDUCATION),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '投递限制' => $this->getDeliveryLimitType(),
                '更新时间' => $this->jobInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => '',
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->jobInfo->announcement_id,
        ];

        return $this;
    }

    private function validate()
    {
        if ($this->params['isLimit'] == 1) {
            $scene = 'addEducation';
        } else {
            $scene = 'removeEducation';
        }
        // 获取按钮是否可以按的逻辑
        $operateRes = (new OperateService())->run($scene, $this->jobInfo->audit_status, $this->jobInfo->status,
            $this->jobInfo->is_article, $this->jobInfo->is_show, 0, 0);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('状态不符合，不满足操作条件');
        }

        $deliveryLimitType = explode(',', $this->jobInfo->delivery_limit_type);
        if (in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType) && $this->params['isLimit'] == 1) {
            throw new MessageException('已经操作，不可二次操作');
        }
        if (!in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType) && $this->params['isLimit'] == 2) {
            throw new MessageException('已经操作，不可二次操作');
        }

        return $this;
    }
}