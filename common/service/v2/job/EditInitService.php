<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseWelfareLabel;
use common\helpers\TimeHelper;
use Yii;
use yii\base\Exception;

/**
 * 职位编辑初始化
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class EditInitService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 编辑初始化
     * @return array|\yii\db\ActiveRecord
     * @throws Exception
     */
    public function run()
    {
        $id = Yii::$app->request->get('jobId');
        if ($id < 0) {
            throw new Exception('参数错误');
        }
        $detail = BaseJob::find()
            ->select([
                'id as jobId',
                'company_id as companyId',
                'name',
                'period_date as periodDate',
                'code',
                'job_category_id as jobCategoryId',
                'education_type as educationType',
                'major_id as majorId',
                'nature_type as natureType',
                'is_negotiable as isNegotiable',
                'wage_type as wageType',
                'min_wage as minWage',
                'max_wage as maxWage',
                'experience_type as experienceType',
                'age_type as ageType',
                'min_age as minAge',
                'max_age as maxAge',
                'title_type as titleType',
                'political_type as politicalType',
                'abroad_type as abroadType',
                'amount',
                'department',
                'province_id as provinceId',
                'city_id as cityId',
                'district_id as districtId',
                'address',
                'welfare_tag as welfareTag',
                'duty',
                'requirement',
                'remark',
                'status',
                // 影响提交了
                'audit_status as auditStatus',
                'is_show as isShow',
                'apply_type as applyType',
                'apply_address as applyAddress',
                'release_time as releaseTime',
                'add_time as addTime',
                'file_ids as fileIds',
                'delivery_limit_type as deliveryLimitType',
                'delivery_type as deliveryType',
                'extra_notify_address as extraNotifyAddress',
                'delivery_way as deliveryWay',
                'establishment_type as establishmentType',
                'announcement_id as announcementId',
                'is_establishment as isEstablishment',
            ])
            ->where(['id' => $id])
            ->asArray()
            ->one();
        if (!$detail) {
            throw new Exception('职位不存在');
        }
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->initInfo();
            if ($this->companyInfo->id != $detail['companyId']) {
                throw new Exception('您的单位不存在该职位数据');
            }
        }
        if ($detail['status'] == BaseJob::STATUS_OFFLINE || $detail['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            throw new Exception('职位待审核或者已下线，不允许编辑');
        }
        unset($detail['auditStatus']);
        //虚拟ID-代指临时职位ID-保证初始化结构一直
        $this->detail = $detail;
        unset($detail);

        $this->detail['id']                = 0;
        $this->detail['isTemp']            = BaseJobTemp::IS_TEMP_NO;
        $this->detail['announcementTitle'] = '';
        $announcementInfo                  = null;
        if ($this->detail['announcementId'] > 0) {
            $announcementInfo = BaseAnnouncement::findOne($this->detail['announcementId']);
            if (!$announcementInfo) {
                throw new Exception('关联的公告不存在');
            }

            if ($announcementInfo->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
                throw new Exception('公告待审核，不允许编辑');
            }
            $this->detail['announcementTitle'] = $this->operationPlatform == self::PLATFORM_WEB_COMPANY ? $announcementInfo->title : '(公告ID：' . $announcementInfo->uuid . ')' . $announcementInfo->title;
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            //特殊处理单位端过来的数据，且携带source ，source=1跟公告 source =2跟随自己
            $this->detail['source'] = 2;
            if ($announcementInfo && empty($this->detail['deliveryType'])) {
                //那就去那拿一下公告的回显
                $this->detail['applyType']          = $announcementInfo->apply_type;
                $this->detail['applyAddress']       = $announcementInfo->apply_address;
                $this->detail['extraNotifyAddress'] = $announcementInfo->extra_notify_address;
                $this->detail['source']             = 1;
            }

            unset($this->detail['deliveryType'], $this->detail['deliveryWay']);
        }
        $this->processCompanyInfo();
        //薪资wage_id回显
        $this->processWageInfo();
        //查询福利标签
        $this->processWelfareTag();
        $this->processDeliveryInfo();
        $this->processAreaInfo();
        $this->processFileInfo();
        $this->processContactInfo();
        $this->processEducationAndMajorInfo();
        //处理一写空值
        $this->processDefaultValue();

        return $this->detail;
    }
}