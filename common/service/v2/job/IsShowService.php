<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\base\models\BaseShowcase;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 职位显示&隐藏
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class IsShowService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 前置检查
     */
    public function runHideCheck()
    {
        $this->jobId = Yii::$app->request->get('jobId');
        if (!$this->jobId) {
            throw new MessageException('参数错误');
        }
        $jobInfo = BaseJob::findOne($this->jobId);
        if (!$jobInfo) {
            throw new MessageException('职位不存在');
        }
        if ($jobInfo->announcement_id > 0) {
            $countJob = BaseJob::find()
                ->where([
                    'announcement_id' => $jobInfo->announcement_id,
                    'status'          => BaseJob::STATUS_ONLINE,
                ])
                ->count();
            if ($countJob == 1) {
                return [
                    'count' => $countJob,
                    'msg'   => '该职位为公告下唯一在线职位！隐藏后，该职位所属公告将被下线。确定操作吗？',
                ];
            }
        }

        return true;
    }

    public function isShow($params)
    {
        $this->initInfo();
        $this->params['isShow'] = $params['isShow'] ?? 1;
        $this->setJob($params['jobId'])
            ->validate()
            ->beforeHandleLog()
            ->run()
            ->handleLog()
            ->log(BaseJobLog::TYPE_LIMIT_IS_SHOW);
        $this->after();
    }

    public function isShowBatch($params)
    {
        $failMessage = [];
        $this->initInfo();
        $this->params['isShow'] = $params['isShow'] ?? 1;
        $jobIds                 = StringHelper::changeStrToFilterArr($params['jobId'] ?? '');
        foreach ($jobIds as $jobId) {
            try {
                $this->setJob($jobId)
                    ->validate()
                    ->beforeHandleLog()
                    ->run()
                    ->handleLog()
                    ->log(BaseJobLog::TYPE_LIMIT_IS_SHOW_BATCH);
            } catch (\Exception $exception) {
                $failMessage[] = '职位ID：' . $jobId . '，执行' . ($this->params['isShow'] == 1 ? '显示' : '隐藏') . '失败，原因：' . $exception->getMessage();
            }
            $this->after();
        }

        return [
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    /**
     * 后置处理
     * @return void
     * @throws Exception
     */
    private function after()
    {
        //是否需要刷新职位详情缓存
        $this->updateStatInfo();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
            BaseShowcase::updateCompanyStatIsPay([$this->jobInfo->company_id]);
        }
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function handleLog()
    {
        $this->handleLogData['handle_after'] = json_encode([
            '投递限制' => BaseJob::IS_SHOW_NAME[$this->jobInfo->is_show],
            '更新时间' => $this->jobInfo['update_time'],
        ], JSON_UNESCAPED_UNICODE);
        // 消耗套餐,写日志等等
        BaseJobHandleLog::createInfo($this->handleLogData);

        return $this;
    }

    private function run()
    {
        if ($this->params['isShow'] == 1) {
            $this->jobInfo->is_show = BaseJob::IS_SHOW_YES;
        } else {
            $this->jobInfo->offline_time = CUR_DATETIME;
            $this->jobInfo->is_show      = BaseJob::IS_SHOW_NO;
        }
        $this->jobInfo->status = BaseJob::STATUS_OFFLINE;
        // 拼接字符串
        if (!$this->jobInfo->save()) {
            throw new Exception($this->jobInfo->getFirstErrorsMessage());
        }

        return $this;
    }

    private function beforeHandleLog()
    {
        // 记录一下log数据
        $this->handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobInfo->id,
            'handle_type'     => strval($this->params['isShow'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_SHOW : BaseJobHandleLog::HANDLE_TYPE_HIDDEN),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '投递限制' => BaseJob::IS_SHOW_NAME[$this->jobInfo->is_show],
                '更新时间' => $this->jobInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => '',
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->jobInfo->announcement_id,
        ];

        return $this;
    }

    private function validate()
    {
        if ($this->params['isShow'] == 1) {
            $scene = 'show';
        } else {
            $scene = 'hide';
        }
        // 获取按钮是否可以按的逻辑
        $operateRes = (new OperateService())->run($scene, $this->jobInfo->audit_status, $this->jobInfo->status,
            $this->jobInfo->is_article, $this->jobInfo->is_show, 0, 0);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('状态不符合，不满足操作条件');
        }

        if ($this->jobInfo->is_show == BaseJob::IS_SHOW_YES && $this->params['isShow'] == 1) {
            throw new MessageException('已经操作，不可二次操作');
        }
        if ($this->jobInfo->is_show == BaseJob::IS_SHOW_NO && $this->params['isShow'] != 1) {
            throw new MessageException('已经操作，不可二次操作');
        }

        return $this;
    }
}