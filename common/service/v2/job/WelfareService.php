<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\base\models\BaseWelfareLabel;
use common\helpers\IpHelper;
use common\service\jobTop\JobTopApplication;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 职位自改福利
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class WelfareService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 批量前置检查
     * @throws Exception
     */
    public function runCheck()
    {
        $jobIds = Yii::$app->request->post('jobIds');
        if (!$jobIds) {
            throw new Exception('参数错误');
        }
        //检查一下jobids 是否同属一个公告
        $jobIdsArr      = explode(',', $jobIds);
        $jobIdOne       = $jobIdsArr[0];
        $jobInfo        = BaseJob::findOne($jobIdOne);
        $announcementId = $jobInfo->announcement_id;
        if (!$announcementId) {
            throw new Exception('含有纯职位，请勾选同一公告下后再修改福利待遇！');
        }
        $count = BaseJob::find()
            ->where([
                'id'              => $jobIdsArr,
                'announcement_id' => $announcementId,
            ])
            ->count();
        if (count($jobIdsArr) != $count) {
            throw new Exception('勾选的职位不是同属一个公告');
        }

        return ['companyMemberId' => BaseCompany::findOne($jobInfo->company_id)->member_id];
    }

    /**
     * 批量福利变更
     * @throws Exception
     */
    public function run()
    {
        $jobIds     = Yii::$app->request->post('jobIds');
        $welfareIds = Yii::$app->request->post('welfareIds', '');
        if (!$jobIds) {
            throw new Exception('参数错误');
        }
        //检查一下jobids 是否同属一个公告
        $jobIdsArr      = explode(',', $jobIds);
        $jobIdOne       = $jobIdsArr[0];
        $announcementId = BaseJob::findOne($jobIdOne)->announcement_id;
        if (!$announcementId) {
            throw new Exception('含有纯职位，请勾选同一公告下后再修改福利待遇！');
        }
        $count = BaseJob::find()
            ->where([
                'id'              => $jobIdsArr,
                'announcement_id' => $announcementId,
            ])
            ->count();
        if (count($jobIdsArr) != $count) {
            throw new Exception('勾选的职位不是同属一个公告');
        }
        foreach ($jobIdsArr as $jobId) {
            $this->jobId          = $jobId;
            $jobInfo              = BaseJob::findOne($jobId);
            $this->oldJobInfo     = $jobInfo;
            $jobInfo->welfare_tag = $welfareIds;
            if (!$jobInfo->save()) {
                throw new Exception('福利保存失败');
            }
            $this->jobInfo = $jobInfo;
            //记录操作日志
            BaseJobHandleLog::createInfo([
                'add_time'        => date('Y-m-d H:i:s'),
                'job_id'          => $jobId,
                'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_WELFARE,
                'handler_type'    => $this->params['platformType'],
                'handler_id'      => $this->params['userId'],
                'handler_name'    => $this->params['username'],
                'handle_before'   => json_encode([
                    '职位福利' => $this->oldJobInfo->welfare_tag ? implode(',',
                        BaseWelfareLabel::getWelfareLabelNameList($this->oldJobInfo->welfare_tag)) : '',
                ]),
                'handle_after'    => json_encode([
                    '职位福利' => $welfareIds ? implode(',',
                        BaseWelfareLabel::getWelfareLabelNameList($welfareIds)) : '',
                ]),
                'ip'              => IpHelper::getIpInt(),
                'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
            ]);
            //写一下日志
            $this->log($this->isBatch ? BaseJobLog::TYPE_REPUBLISH_BATCH : BaseJobLog::TYPE_REPUBLISH);
        }
        //后置处理
        $this->after();

        return true;
    }

    /**
     * 后置处理
     * @return void
     */
    private function after()
    {
        $this->updateJobWelfareRelationTable();
        if ($this->jobInfo->announcement_id) {
            $this->runAutoColumnAfter();
        }
    }
}