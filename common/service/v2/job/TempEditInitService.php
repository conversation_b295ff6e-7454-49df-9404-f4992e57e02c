<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJobTemp;
use Yii;
use yii\base\Exception;

/**
 * 临时职位编辑初始化
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TempEditInitService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 临时职位编辑初始化
     * @return array|\yii\db\ActiveRecord
     * @throws Exception
     */
    public function run()
    {
        $id = Yii::$app->request->get('id');
        if ($id < 0) {
            throw new Exception('参数错误');
        }
        $detail = BaseJobTemp::find()
            ->select([
                'id',
                'job_id as jobId',
                'company_id as companyId',
                'name',
                'period_date as periodDate',
                'code',
                'job_category_id as jobCategoryId',
                'education_type as educationType',
                'major_id as majorId',
                'nature_type as natureType',
                'is_negotiable as isNegotiable',
                'wage_type as wageType',
                'min_wage as minWage',
                'max_wage as maxWage',
                'experience_type as experienceType',
                'age_type as ageType',
                'min_age as minAge',
                'max_age as maxAge',
                'title_type as titleType',
                'political_type as politicalType',
                'abroad_type as abroadType',
                'amount',
                'department',
                'province_id as provinceId',
                'city_id as cityId',
                'district_id as districtId',
                'address',
                'welfare_tag as welfareTag',
                'duty',
                'requirement',
                'remark',
                'apply_type as applyType',
                'apply_address as applyAddress',
                'add_time as addTime',
                'delivery_limit_type as deliveryLimitType',
                'delivery_type as deliveryType',
                'extra_notify_address as extraNotifyAddress',
                'delivery_way as deliveryWay',
                'establishment_type as establishmentType',
                'is_establishment as isEstablishment',
                'contact_id as contactId',
                'contact_synergy_id as contactSynergyId',
            ])
            ->where(['id' => $id])
            ->asArray()
            ->one();
        if (!$detail) {
            throw new Exception('职位不存在');
        }
        $this->detail = $detail;
        unset($detail);
        $this->detail['isTemp'] = BaseJobTemp::IS_TEMP_YES;
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            unset($this->detail['deliveryType'], $this->detail['deliveryWay']);
            $this->initInfo();
            if ($this->companyInfo->id != $this->detail['companyId']) {
                throw new Exception('您的单位不存在该职位数据');
            }
        }

        $this->detail['announcementTitle'] = '';
        $this->processCompanyInfo();
        //薪资wage_id回显
        $this->processWageInfo();
        //查询福利标签
        $this->processWelfareTag();
        $this->processDeliveryInfo();
        $this->processAreaInfo();
        $this->processFileInfo();
        $this->processEducationAndMajorInfo();
        //获取联系人与协同联系人
        $this->detail['jobContact']   = BaseCompanyMemberInfo::getInfoOne($this->detail['contactId']);
        $this->detail['jobContactId'] = $this->detail['contactId'];
        //职位协同账号
        $this->detail['jobContactSynergy'] = $this->detail['contactSynergyId'] ? BaseCompanyMemberInfo::getInfoMany(explode(',',
            $this->detail['contactSynergyId'])) : [];
        foreach ($this->detail['jobContactSynergy'] as &$item) {
            $item['company_member_info_id'] = $item['id'];
            $item['is_contact']             = 0;
            if ($item['company_member_info_id'] == $this->detail['jobContactId']) {
                $item['is_contact'] = 1;
            }
        }
        $this->detail['jobContactSynergyNum'] = count($this->detail['jobContactSynergy']);
        $this->detail['jobContactSynergyIds'] = $this->detail['jobContactSynergyNum'] > 0 ? array_column($this->detail['jobContactSynergy'],
            'id') : [];
        //处理一写空值
        $this->processDefaultValue();

        return $this->detail;
    }
}