<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\service\companyPackage\CompanyPackageApplication;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 职位刷新
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class RefreshService extends BaseService
{
    private $id;
    /** @var 允许通过数量 */
    private $allowNumber = 0;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置ID
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * 职位批量刷新检查
     * @throws Exception
     */
    public function batchRefreshCheck()
    {
        $this->params = Yii::$app->request->post();
        $this->initInfo();

        if (!$this->params['id']) {
            throw new Exception('非法访问');
        }
        $idArr         = explode(',', $this->params['id']);
        $jobWhere      = [
            'company_id' => $this->params['companyId'],
            'id'         => $idArr,
        ];
        $jobSelect     = [
            'id',
            'name',
            'refresh_time',
            'real_refresh_time',
        ];
        $job           = BaseJob::find()
            ->select($jobSelect)
            ->where($jobWhere)
            ->asArray()
            ->all();
        $companyConfig = BaseCompanyPackageConfig::findOne(['company_id' => $this->params['companyId']]);
        $allowNumber   = 0;
        foreach ($job as $list) {
            if ($list['real_refresh_time'] != TimeHelper::ZERO_TIME) {
                // 没有刷新过
                $subDay = TimeHelper::computeDaySub($list['real_refresh_time'], CUR_DATETIME);
                if ($subDay >= $companyConfig->job_refresh_interval_day) {
                    //刷新时间间隔符合说明能刷新
                    $allowNumber++;
                }
            } else {
                $allowNumber++;
            }
        }
        //        if ($allowNumber == 0) {
        //            throw new Exception('当前暂无职位可刷新，请重新选择！');
        //        }

        return [
            'title'       => '职位刷新',
            'title_tips'  => '每个职位' . $companyConfig->job_refresh_interval_day . '天内限刷新一次',
            'content'     => '共有<span class="color-primary">' . $allowNumber . '</span>个职位可刷新，将消耗<span class="color-primary">' . $allowNumber . '</span>次职位刷新资源，当前可用<span class="color-primary">' . $companyConfig->job_refresh_amount . '</span>次',
            'description' => '刷新能让职位排名跃升，曝光量倍增，有效提高招聘效率',
        ];
    }

    /**
     * 职位刷新、批量刷新
     * @throws Exception
     */
    public function run()
    {
        if (!$this->id) {
            $this->id = Yii::$app->request->post('jobId');
        }
        if (!$this->id) {
            throw new Exception('参数错误');
        }
        $this->initInfo();
        //这里利用是单位端才进入所以不管你穿多少个ID都应该是同一个单位
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->baseCheckMemberPackage();
        }
        $ids = explode(',', $this->id);
        $msg = '';
        if (count($ids) > 0 && $this->isBatch) {
            foreach ($ids as $idItem) {
                $itemRes = $this->refreshOne($idItem);
                if ($itemRes !== true) {
                    $msg .= $itemRes;
                }
            }
        } else {
            //单个刷新
            $this->refreshOne($this->id);
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if ($this->allowNumber == 0) {
                //当前暂无职位可刷新！
                throw new Exception('暂无职位可刷新，请重新选择！');
            }
            if ($this->allowNumber > $this->companyPackageConfigModel->job_refresh_amount) {
                throw new Exception('您当前可用职位刷新资源不足！');
            }
            //职位刷新消耗套餐
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFRESH;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            $companyPackageApplication->jobRefresh($this->companyInfo->id, $this->allowNumber, $remark);
        }

        return empty($msg) ? true : $msg;
    }

    /**
     * 刷新单条职位
     * @param $id
     * @throws Exception
     */
    private function refreshOne($id)
    {
        $jobModel = BaseJob::findOne($id);
        if (!$jobModel) {
            throw new Exception('职位不存在');
        }
        if ($jobModel->status != BaseJob::STATUS_ONLINE) {
            if ($this->isBatch && $this->operationPlatform == self::PLATFORM_ADMIN) {
                return '职位ID：' . $jobModel->uuid . ',非在线状态，不允许操作刷新<br />';
            } else {
                throw new Exception($jobModel->name . '职位状态是非在线，不允许操作刷新！');
            }
        }
        // 单位端需统计可通过数
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if ($jobModel->real_refresh_time != TimeHelper::ZERO_TIME) {
                // 没有刷新过
                $subDay = TimeHelper::computeDaySub($jobModel->real_refresh_time, CUR_DATETIME);
                if ($subDay >= $this->companyPackageConfigModel->job_refresh_interval_day) {
                    $this->allowNumber++;
                } else {
                    //这时候不能刷新这个职位
                    //直接返回
                    return true;
                }
            } else {
                $this->allowNumber++;
            }
        }

        //初始化一写数据
        $this->jobId = $id;
        $this->setCompany($jobModel->company_id);

        // 保存原始的刷新时间，避免在save()后被更新
        $oldRefreshTime = $jobModel->refresh_time;

        $jobModel->refresh_time      = CUR_DATETIME;
        $jobModel->refresh_date      = CUR_DATE;
        $jobModel->real_refresh_time = CUR_DATETIME;
        $jobModel->is_first_release  = BaseJob::IS_FIRST_RELEASE_NO;
        if (!$jobModel->save()) {
            throw new Exception('刷新失败:' . $jobModel->getFirstErrorsMessage());
        }

        $this->jobInfo = $jobModel;
        BaseJobHandleLog::createInfo([
            'add_time'        => date('Y-m-d H:i:s'),
            'job_id'          => $id,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode(['刷新时间' => $oldRefreshTime]),
            'handle_after'    => json_encode(['刷新时间' => CUR_DATETIME]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
        ]);
        //写一下日志
        $this->log($this->isBatch ? BaseJobLog::TYPE_REFRESH_BATCH : BaseJobLog::TYPE_REFRESH);

        //执行后置操作
        $this->after();

        return true;
    }

    /**
     * 后置处理
     * @return void
     */
    private function after()
    {
        if ($this->jobInfo->announcement_id) {
            $this->runAutoColumnAfter();
        }
    }

}