<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use Yii;
use yii\base\Exception;

/**
 * 职位审核历史日志
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditHistoryLogService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取审核日志
     * @throws Exception
     */
    public function run()
    {
        $params = Yii::$app->request->get();
        if (!$params['jobId']) {
            throw new Exception('参数错误');
        }
        $query    = BaseJobHandleLog::find()
            ->where([
                'job_id'       => $params['jobId'],
                'handler_type' => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                'handle_type'  => BaseJobHandleLog::HANDLE_TYPE_AUDIT,
            ]);
        $count    = $query->count();
        $pageSize = $params['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        //职位-职位审核处理历史
        $jobHandleLog = $query->select([
            'id',
            'add_time as addTime',
            'handler_name as handlerName',
            'handle_after as handleAfter',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($jobHandleLog as &$log) {
            $log['opinion']     = "";
            $handleAfter        = json_decode($log['handleAfter'], true);
            $log['handleAfter'] = $handleAfter;
            if ($handleAfter['审核状态']) {
                $log['auditStatusTitle'] = $handleAfter['审核状态'];
            }
            if ($handleAfter['审核原因']) {
                $log['opinion'] = $handleAfter['审核原因'];
            }
        }

        return [
            'list' => $jobHandleLog,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }
}