<?php
/**
 * admin职位列表的按钮逻辑
 * create user：伍彦川
 * create time：2025/5/19 09:10
 */
namespace common\service\v2\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseJob;
use common\components\MessageException;

class OperateService extends BaseService
{
    private $nameMap = [
        'show'             => [
            'name'  => '显示',
            'class' => 'emerald',
        ],
        'business'         => [
            'name'  => '业务',
            'class' => 'teal',
        ],
        'log'              => [
            'name'  => '日志',
            'class' => 'purple',
        ],
        'audit'            => [
            'name'  => '审核',
            'class' => 'blue',
        ],
        'edit'             => [
            'name'  => '编辑',
            'class' => 'green',
        ],
        'delete'           => [
            'name'  => '删除',
            'class' => 'red',
        ],
        'republish'        => [
            'name'  => '再发布',
            'class' => 'yellow',
        ],
        'offline'          => [
            'name'  => '下线',
            'class' => 'gray',
        ],
        'hide'             => [
            'name'  => '隐藏',
            'class' => 'orange',
        ],
        'refresh'          => [
            'name'  => '刷新',
            'class' => 'bluish',
        ],
        'more'             => [
            'name'  => '更多',
            'class' => 'white',
        ],
        'establishment'    => [
            'name'  => '编制设置',
            'class' => 'bluish',
        ],
        'addEducation'     => [
            'name'  => '添加学历限制',
            'class' => 'bluish',
        ],
        'removeEducation'  => [
            'name'  => '取消学历限制',
            'class' => 'bluish',
        ],
        'addAttachment'    => [
            'name'  => '添加附件限制',
            'class' => 'bluish',
        ],
        'removeAttachment' => [
            'name'  => '取消附件限制',
            'class' => 'bluish',
        ],
        'contact'          => [
            'name'  => '协同子账号&联系人设置',
            'class' => 'bluish',
        ],
        'invite'           => [
            'name'  => '投递邀约',
            'class' => 'bluish',
        ],

    ];

    /** @var string 场景值 */
    private $scene;

    public function __construct($params = [])
    {
        parent::__construct();
        if (isset($params['scene'])) {
            $this->scene = $params['scene'];
        }
    }

    private function getBtnDefautl($label, $key, $class, $disabled)
    {
        return [
            'label'    => $label,
            'key'      => $key,
            'class'    => $class,
            // 是否可以点击
            'disabled' => $disabled,
        ];
    }

    public function getBtnInfo($name)
    {
        return $this->nameMap[$name] ?? $this->nameMap['更多'];
    }

    /**
     * 按钮操作逻辑
     * @param $scene                   场景
     * @param $auditStatus             职位审核状态
     * @param $status                  职位状态
     * @param $isArticle               职位是否是混合模式
     * @param $isShow                  职位显示状态
     * @param $announcementStatus      职位关联的公告的状态
     * @param $announcementAuditStatus 职位关联的公告的审核状态
     * @param $companyId               单位id，如果不是设置子账号，可以不传
     * @return array
     */
    public function run(
        $scene,
        $auditStatus,
        $status,
        $isArticle,
        $isShow,
        $announcementStatus = 0,
        $announcementAuditStatus = 0,
        $companyId = 0
    ) {
        $announcementStatus      = intval($announcementStatus);
        $announcementAuditStatus = intval($announcementAuditStatus);

        $data    = [
            // 刷新
            'refresh'          => [
                // 是否区分公告
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 已下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                    ],
                ],
            ],
            // 编辑
            'edit'             => [
                // 区分公告
                'article' => true,
                'data'    => [
                    BaseJob::IS_ARTICLE_NO  => [
                        0 => [
                            0 => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 待发布
                                BaseJob::STATUS_WAIT    => [
                                    // 编辑中
                                    BaseJob::AUDIT_STATUS_WAIT         => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                            ],
                        ],
                    ],
                    BaseJob::IS_ARTICLE_YES => [
                        // 公告在线
                        BaseAnnouncement::STATUS_ONLINE  => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS    => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE  => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告编辑中
                            BaseAnnouncement::STATUS_AUDIT_STAGING => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT   => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                        ],
                        // 公告下线
                        BaseAnnouncement::STATUS_OFFLINE => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS    => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE  => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告编辑中
                            BaseAnnouncement::STATUS_AUDIT_STAGING => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT   => [
                                // 在线
                                BaseJob::STATUS_ONLINE  => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            // 再发布
            'republish'        => [
                // 是否区分公告
                'article' => true,
                'data'    => [
                    BaseJob::IS_ARTICLE_NO . ':' . BaseJob::IS_SHOW_YES  => [
                        0 => [
                            0 => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                ],
                            ],
                        ],

                    ],
                    BaseJob::IS_ARTICLE_NO . ':' . BaseJob::IS_SHOW_NO   => [
                        0 => [
                            0 => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                ],
                            ],
                        ],
                    ],
                    BaseJob::IS_ARTICLE_YES . ':' . BaseJob::IS_SHOW_YES => [
                        // 公告在线
                        BaseAnnouncement::STATUS_ONLINE  => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS   => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT  => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                ],
                            ],
                        ],
                        // 公告下线
                        BaseAnnouncement::STATUS_OFFLINE => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS   => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT  => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                        ],
                    ],
                    BaseJob::IS_ARTICLE_YES . ':' . BaseJob::IS_SHOW_NO => [
                        // 公告在线
                        BaseAnnouncement::STATUS_ONLINE  => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS   => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT  => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                                ],
                            ],
                        ],
                        // 公告下线
                        BaseAnnouncement::STATUS_OFFLINE => [
                            // 公告审核通过
                            BaseAnnouncement::STATUS_AUDIT_PASS   => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                            // 公告待审核
                            BaseAnnouncement::STATUS_AUDIT_AWAIT  => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                            // 公告审核拒绝
                            BaseAnnouncement::STATUS_AUDIT_REFUSE => [
                                // 下线
                                BaseJob::STATUS_OFFLINE => [
                                    // 待审核
                                    BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                                    // 审核拒绝
                                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                                    // 审核通过
                                    BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            // 下线
            'offline'          => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 显示
            'show'             => [
                // 是否区分公告
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 隐藏
            'hide'             => [
                // 是否区分公告
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 删除
            'delete'           => [
                'article' => false,
                'data'    => [
                    // 待发布
                    BaseJob::STATUS_WAIT => [
                        // 编辑中
                        BaseJob::AUDIT_STATUS_WAIT         => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 职位编制
            'establishment'    => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 添加学历限制
            'addEducation'     => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 取消学历限制
            'removeEducation'  => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 添加附件限制
            'addAttachment'    => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 取消附件限制
            'removeAttachment' => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 协同子账号&联系人设置
            'contact'          => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
            // 投递邀约
            'invite'           => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 2,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 2,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 2,
                    ],
                ],
            ],
            // 日志
            'log'              => [
                'article' => false,
                'data'    => [
                    // 在线
                    BaseJob::STATUS_ONLINE  => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                    // 下线
                    BaseJob::STATUS_OFFLINE => [
                        // 审核通过
                        BaseJob::AUDIT_STATUS_PASS_AUDIT   => 1,
                        // 待审核
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT   => 1,
                        // 审核拒绝
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT => 1,
                    ],
                ],
            ],
        ];
        $article = $data[$scene]['article'] ?? false;

        // 特殊处理再发布的纯职位
        if ($scene == 'republish') {
            $isArticle = $isArticle . ':' . $isShow;
        }

        if ($article) {
            $result = $data[$scene]['data'][$isArticle][$announcementStatus][$announcementAuditStatus][$status][$auditStatus] ?? 3;
        } else {
            $result = $data[$scene]['data'][$status][$auditStatus] ?? 3;
        }

        $btnInfo = $this->getBtnInfo($scene);

        // 返回 1:'可用';2:'禁用'；3:找不到就表示不展示了
        $btnData = $this->getBtnDefautl($btnInfo['name'], $scene, $btnInfo['class'], $result ?? 3);

        if ($scene == 'contact' && $result == 1) {
            $companyMemberConfig = BaseCompanyMemberConfig::find()
                ->select(['used'])
                ->where(['company_id' => $companyId])
                ->asArray()
                ->one();
            if (!$companyMemberConfig || $companyMemberConfig['used'] < 1) {
                $btnData['disabled'] = 2;
            }
        }

        if ($isShow == BaseJob::IS_SHOW_YES && $scene == 'show') {
            $btnData['disabled'] = 3;
        }
        if ($isShow == BaseJob::IS_SHOW_NO && $scene == 'hide') {
            $btnData['disabled'] = 3;
        }

        return $btnData;
    }

}