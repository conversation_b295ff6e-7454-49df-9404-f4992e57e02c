<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use Yii;
use yii\base\Exception;

/**
 * 职位复制
 *  1、复制临时职位
 *  2、复制正式职位
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class CopyService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 复制临时职位或者职位
     * @throws Exception
     */
    public function run()
    {
        $params = Yii::$app->request->post();
        if (!isset($params['jobId'])) {
            throw new Exception('参数错误');
        }
        $select = [
            'name',
            'code',
            'amount',
            'department',
            'address',
            'duty',
            'requirement',
            'remark',
            'company_id as companyId',
            'period_date as periodDate',
            'job_category_id as jobCategoryId',
            'education_type as educationType',
            'major_id as majorId',
            'nature_type as natureType',
            'is_negotiable as isNegotiable',
            'wage_type as wageType',
            'min_wage as minWage',
            'max_wage as maxWage',
            'experience_type as experienceType',
            'age_type as ageType',
            'min_age as minAge',
            'max_age as maxAge',
            'title_type as titleType',
            'political_type as politicalType',
            'abroad_type as abroadType',
            'district_id as districtId',
            'province_id as provinceId',
            'city_id as cityId',
            'welfare_tag as welfareTag',
            'offline_type as offlineType',
            'announcement_id as announcementId',
            'create_type as createType',
            'gender_type as genderType',
            'apply_type as applyType',
            'apply_address as applyAddress',
            'delivery_limit_type as deliveryLimitType',
            'delivery_type as deliveryType',
            'delivery_way as deliveryWay',
            'extra_notify_address as extraNotifyAddress',
            'establishment_type as establishmentType',
            'is_establishment as isEstablishment',
        ];

        $data                         = BaseJob::find()
            ->select($select)
            ->where(['id' => $params['jobId']])
            ->asArray()
            ->one();
        $contact                      = BaseJob::getJobContact($params['jobId']);
        $data['jobContactId']         = $contact['company_member_info_id'];
        $contact_synergy              = BaseJob::getJobContactSynergy($params['jobId']);
        $jobContactSynergyIdsArr      = $contact_synergy ? array_column($contact_synergy,
            'company_member_info_id') : [];
        $data['jobContactSynergyIds'] = $jobContactSynergyIdsArr ? implode(',', $jobContactSynergyIdsArr) : '';

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            unset($data['deliveryType'], $data['deliveryWay']);
        }

        //但是最后本质是添加职位了
        return (new AddService())->setPlatform($this->operationPlatform)
            ->setParams($data)
            ->run();
    }
}