<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\libs\JobBatchImport;
use common\service\CommonService;
use common\service\job\JobService;
use Yii;
use yii\base\Exception;

/**
 * 批量导入新增职位
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AddBatchImportService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 导入批量新增职位
     * @return true
     * @throws Exception
     */
    public function run()
    {
        $request = Yii::$app->request->post();
        //单位端将单位ID塞进去
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $request['companyId'] = BaseCompanyMemberInfo::findOne(['member_id' => Yii::$app->user->id])->company_id;
        }
        // todo 这里校验一下数据
        if (!$request['filePath'] || $request['companyId'] < 1 || ($this->operationPlatform == self::PLATFORM_ADMIN && !$request['periodDate'])) {
            throw new Exception('必填参数不能为空');
        }
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $request['filePath'];
        $this->setCompany($request['companyId']);
        // 读取数据
        $model = new JobBatchImport();
        $model->identify($filePath,
            $this->operationPlatform == self::PLATFORM_ADMIN ? JobBatchImport::PLATFORM_ADMIN : BaseCompany::getDeliveryTypeCate($this->companyInfo->delivery_type));
        $data = $model->clearData;
        // 处理数据
        // 插入数据
        foreach ($data as $item) {
            $jobData = array_column($item, 'value', 'key');
            // 合并识别学科与自选学科
            if (!empty($jobData['majorId']) && !empty($jobData['majorAi'])) {
                $jobData['majorId'] = array_unique(array_merge($jobData['majorId'], $jobData['majorAi']));
            } elseif (empty($jobData['majorId']) && !empty($jobData['majorAi'])) {
                $jobData['majorId'] = $jobData['majorAi'];
            }
            $jobData['periodDate'] = $jobData['periodDate'] ?: ($this->operationPlatform == self::PLATFORM_ADMIN ? $request['periodDate'] : '');
            $jobData['companyId']  = $request['companyId'];
            $jobData['fileIds']    = $request['fileIds'];
            $jobData['majorId']    = empty($jobData['majorId']) ? [] : implode(',', $jobData['majorId']);;
            $jobData['welfareTag'] = $jobData['welfareTag'] ? implode(',', $jobData['welfareTag']) : '';
            if (!isset($jobData['deliveryWay']) || $jobData['deliveryWay'] <= 0) {
                if (empty($jobData['applyType']) && empty($jobData['applyAddress']) && empty($jobData['extraNotifyAddress'])) {
                    $jobData['applyType']          = $request['applyType'];
                    $jobData['applyAddress']       = $request['applyAddress'];
                    $jobData['extraNotifyAddress'] = $request['extraNotifyAddress'];
                    if ($this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                        $jobData['deliveryWay'] = $request['deliveryWay'];
                    }
                } else {
                    if ($this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $jobData['applyType']) {
                        $jobData['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL_LINK;
                    }
                }
            }

            // 薪资范围
            if ($jobData['minWageMonth'] > 0 || $jobData['maxWageMonth'] > 0) {
                // 月薪
                $jobData['wageType']     = BaseJob::TYPE_WAGE_MONTH;
                $jobData['isNegotiable'] = BaseJob::IS_NEGOTIABLE_YES;
                $jobData['minWage']      = $jobData['minWageMonth'];
                $jobData['maxWage']      = $jobData['maxWageMonth'] ?: $jobData['minWageMonth'];
            } elseif ($jobData['minWageYear'] > 0 || $jobData['maxWageYear'] > 0) {
                // 年薪
                $jobData['wageType']     = BaseJob::TYPE_WAGE_YEARS;
                $jobData['isNegotiable'] = BaseJob::IS_NEGOTIABLE_YES;
                $jobData['minWage']      = $jobData['minWageYear'];
                $jobData['maxWage']      = $jobData['maxWageYear'] ?: $data['minWageYear'];
            } else {
                // 面议
                $jobData['wageType'] = BaseJob::TYPE_WAGE_NEGOTIABLE;
                $jobData['minWage']  = $jobData['minWageMonth'];
                $jobData['maxWage']  = $jobData['maxWageMonth'];
            }
            //处理协同子账号
            ///检验邮箱子账号是否存在
            $contactSynergyEmailArr          = explode(',', $jobData['contactSynergyEmail']);
            $jobData['jobContactSynergyIds'] = [];
            foreach ($contactSynergyEmailArr as $value) {
                $recordId = BaseCompanyMemberInfo::validateEmailMember($request['companyId'], $value);
                if ($recordId) {
                    array_push($jobData['jobContactSynergyIds'], $recordId);
                }
            }
            //处理职位联系人
            $jobData['jobContactId']      = count($jobData['jobContactSynergyIds']) > 0 ? $jobData['jobContactSynergyIds'][0] : BaseCompanyMemberInfo::findOneVal([
                'company_id'          => $request['companyId'],
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ], 'id');
            $jobData['deliveryLimitType'] = '';
            $deliveryLimitTypeArr         = [];
            if (!empty($jobData['deliveryLimitTypeEducation'])) {
                $deliveryLimitTypeArr[] = $jobData['deliveryLimitTypeEducation'];
            }
            if (!empty($jobData['deliveryLimitTypeFile'])) {
                $deliveryLimitTypeArr[] = $jobData['deliveryLimitTypeFile'];
            }
            if (count($deliveryLimitTypeArr) > 0) {
                $jobData['deliveryLimitType'] = implode(',', $deliveryLimitTypeArr);
            }
            // 将提交状态赋值给到后续
            $jobData['auditStatus'] = $request['auditStatus'];
            //删除没用字段
            unset($jobData['minWageMonth'], $jobData['maxWageMonth'], $jobData['minWageYear'], $jobData['maxWageYear'], $jobData['majorAi'], $jobData['deliveryLimitTypeEducation'], $jobData['deliveryLimitTypeFile'], $jobData['contactSynergyEmail']);
            (new AddService())->setPlatform($this->operationPlatform)
                ->setParams($jobData)
                ->setBatch()
                ->run();
        }

        //删除文件
        unlink($filePath);

        return true;
    }
}