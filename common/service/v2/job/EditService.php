<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\helpers\IpHelper;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 职位编辑
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class EditService extends BaseService
{
    private $isHttp = true;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置数据
     * @return $this
     */
    public function setParams($params)
    {
        $this->params = $params;
        $this->isHttp = false;

        return $this;
    }

    /**
     * 编辑职位
     * 纯职位、公告下的职位
     * $this->params两种设置情况：
     *  1、http请求获取参数赋值
     *  2、通过setParams()设置
     * @throws Exception
     */
    public function run()
    {
        //获取表单参数
        if (!$this->params) {
            $this->params = Yii::$app->request->post();
        }
        if (!isset($this->params['jobId']) || empty($this->params['jobId'])) {
            throw new Exception('参数错误');
        }
        //初始化一些数据
        $this->initInfo();
        //前置赋值职位ID
        $this->jobId = $this->params['jobId'];
        //原始职位信息
        $this->oldJobInfo();
        //判断一下一些操作是不允许执行编辑的
        // 1、职位下线或者待审核不允许编辑
        // 2、职位有公告，公告待审核不允许编辑
        if ($this->oldJobInfo->status == BaseJob::STATUS_DELETE || $this->oldJobInfo->status == BaseJob::STATUS_OFFLINE || $this->oldJobInfo->audit_status == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            throw new Exception('职位待审核或者已下线、删除不允许编辑');
        }
        //这里要处理一下----判断一下当前编辑职位是公告下职位并且是否是跟随公告数据内容的、是否本次修改会独立设置内容
        $this->announcementInfo = BaseAnnouncement::findOne($this->oldJobInfo->announcement_id);
        // $this->isHttp公告那边调用直接设置$this->params 不需要判断公告审核的状态
        if ($this->isHttp && $this->announcementInfo && $this->announcementInfo->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('公告待审核不允许编辑所属的职位');
        }
        //单位特殊验证
        $this->companySpecialVerify();
        //检验数据合法性
        $this->dataVerify(false);
        //特殊验证--放dataVerify后面要使用到deliveryType
        $this->specialVerify();
        // 编辑中的不用比对
        // $this->params['auditStatus'] 7点击发布  3点击保存
        if ($this->params['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            if ($this->oldJobInfo->status == BaseJob::STATUS_WAIT) {
                $this->isAudit = true;
            } else {
                $this->filterAudit();
            }
        }
        //检查单位资源
        $this->companySourcePackageCheck();
        //调用更新表逻辑写入职位表-部分内容需要经过审核才能写入
        $this->saveTable(false);
        //写入职位联系人与职位协同
        $this->contact();
        //处理编辑后置逻辑
        $this->after();
        //写入日志
        $this->log(BaseJobLog::TYPE_EDIT);

        return ['jobId' => $this->jobId];
    }

    /**
     * 处理所有后置逻辑
     */
    private function after()
    {
        $this->updateJobCategoryRelationTable();
        $this->updateJobWelfareRelationTable();
        $this->updateJobMajorRelationTable();
        $this->updateJobMiniAppType();
        $this->updateJobBoShiHouTable();
        $this->updateJobPiFlag();
        $this->updateStatInfo();
        $this->updateJobCompanySort();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id) {
            $this->updateAnnouncementEstablishment();
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }

    /**
     * 原始职位信息
     * @throws Exception
     */
    private function oldJobInfo()
    {
        $this->oldJobInfo = BaseJob::findOne($this->jobId);
        if (!$this->oldJobInfo) {
            throw new Exception('职位信息不存在');
        }
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->companyInfo->id != $this->oldJobInfo->company_id) {
            throw new Exception('您的单位不存在该职位数据，无法编辑！');
        }
    }

    /**
     * 特殊检验--一般是编辑出现的检验
     * @throws Exception
     */
    private function specialVerify()
    {
        if ($this->oldJobInfo->status == BaseJob::STATUS_WAIT) {
            //没有通过历史的
            return true;
        }
        // 判断是否属于公告职位
        if ($this->oldJobInfo->announcement_id > 0) {
            $oldJobType       = $this->oldJobInfo->delivery_type;
            $newType          = $this->params['deliveryType'];
            $announcementType = $this->announcementInfo->delivery_type;

            // 公告职位原本有投递类型
            if ($oldJobType > 0) {
                // 编辑后投递类型为0，需与公告一致
                if ($newType == 0 && $announcementType != $oldJobType) {
                    throw new Exception('职位名称:' . $this->jobInfo->name . '报名方式修改导致投递类型发生变更，请注意报名方式的修改规则！');
                }
                // 编辑后投递类型不为0，需与原来一致
                if ($newType != 0 && $oldJobType != $newType) {
                    throw new Exception('职位名称:' . $this->jobInfo->name . '报名方式修改导致投递类型发生变更，请注意报名方式的修改规则！');
                }
            } else {
                // 公告职位原本无投递类型，编辑后有，需与公告一致
                if ($newType > 0 && $announcementType != $newType) {
                    throw new Exception('职位名称:' . $this->jobInfo->name . '编辑使职位报名方式修改导致投递类型发生变更，请注意报名方式的修改规则！');
                }
            }
        } else {
            // 非公告职位，投递类型不能变
            if ($this->oldJobInfo->delivery_type != $this->params['deliveryType']) {
                throw new Exception('职位名称:' . $this->jobInfo->name . '编辑使职位报名方式修改导致投递类型发生变更，请注意报名方式的修改规则！');
            }
        }
    }

    /**
     * 对于职位编辑的一些字段进行对比进入审核流程
     */
    private function filterAudit()
    {
        //编辑前的内容【岗位职责、任职要求、其他说明】
        $oldDuty        = $this->oldJobInfo->duty;
        $oldRequirement = $this->oldJobInfo->requirement;
        $oldRemark      = $this->oldJobInfo->remark;
        //编辑后新的内容
        $newDuty        = $this->params['duty'];
        $newRequirement = $this->params['requirement'];
        $newRemark      = $this->params['remark'];
        //对比内容
        $duty        = $oldDuty != $newDuty ? 1 : 0;
        $requirement = $oldRequirement != $newRequirement ? 1 : 0;
        $remark      = $oldRemark != $newRemark ? 1 : 0;
        $fileIds     = 0;
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $fileIdsArr    = empty($this->params['fileIds']) ? [] : explode(',', $this->params['fileIds']);
            $oldFileIdsArr = empty($this->oldJobInfo->file_ids) ? [] : explode(',', $this->oldJobInfo->file_ids);
            if (count(array_diff($fileIdsArr, $oldFileIdsArr)) > 0) {
                $fileIds = 1;
            } else {
                $this->isWriteFileIds = true;
            }
        }
        //如果有一项不一样就进入审核流程
        if ($duty || $requirement || $remark || ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $fileIds)) {
            $this->isAudit      = true;
            $changeModifyBefore = [];
            $changeModifyAfter  = [];
            //编辑内容
            $editContent = [];
            if ($duty) {
                $editContent['duty']                                                                       = $newDuty;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_DUTY]] = $oldDuty;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_DUTY]]  = $newDuty;
            }
            if ($requirement) {
                $editContent['requirement']                                                                       = $newRequirement;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REQUIREMENT]] = $oldRequirement;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REQUIREMENT]]  = $newRequirement;
            }
            if ($remark) {
                $editContent['remark']                                                                       = $newRemark;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REMARK]] = $oldRemark;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REMARK]]  = $newRemark;
            }
            if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $fileIds) {
                $editContent['file_ids']                                                                       = $this->params['fileIds'];
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]] = $this->oldJobInfo->file_ids;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]]  = $this->params['fileIds'];
            }
            //写入职位编辑表
            $jobEdit = [
                'job_id'          => $this->jobId,
                'add_time'        => CUR_DATETIME,
                'status'          => BaseJobEdit::STATUS_ONLINE,
                'edit_content'    => json_encode($editContent),
                'editor_id'       => $this->params['userId'],
                'editor_type'     => $this->params['platformType'],
                'editor'          => $this->params['username'],
                'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
            ];
            // 检测之前是否有职位编辑内容
            $jobEditInfo = BaseJobEdit::findOne(['job_id' => $this->jobId]);
            if ($jobEditInfo) {
                $condition = ['id' => $jobEditInfo->id];
                BaseJobEdit::updateAll($jobEdit, $condition);
            } else {
                BaseJobEdit::createInfo($jobEdit);
            }
            //写入日志-根据下面注释的
            $handleBefore = json_encode($changeModifyBefore);
            $handleAfter  = json_encode($changeModifyAfter);
            BaseJobHandleLog::createInfo([
                'add_time'        => CUR_DATETIME,
                'job_id'          => $this->jobId,
                'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_EDIT,
                'handler_type'    => $this->params['platformType'],
                'handler_id'      => $this->params['userId'],
                'handler_name'    => $this->params['username'],
                'handle_before'   => $handleBefore,
                'handle_after'    => $handleAfter,
                'ip'              => IpHelper::getIpInt(),
                'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
            ]);
            if ($this->oldJobInfo->is_article == BaseJob::IS_ARTICLE_YES) {
                // 如果之前就有在编辑中的内容,那么是要直接编辑的
                $announcementEditModel                  = BaseAnnouncementEdit::findOne(['announcement_id' => $this->oldJobInfo->announcement_id]) ?: new BaseAnnouncementEdit();
                $announcementEditModel->status          = BaseAnnouncementEdit::STATUS_ONLINE;
                $announcementEditModel->announcement_id = $this->oldJobInfo->announcement_id;
                $announcementEditModel->edit_content    = json_encode([]);//这里不存在编辑公告内容是空的
                $announcementEditModel->editor          = $this->params['username'];
                $announcementEditModel->editor_type     = $this->params['platformType'];
                $announcementEditModel->editor_id       = $this->params['userId'];
                if (!$announcementEditModel->save()) {
                    throw new Exception($announcementEditModel->getFirstErrorsMessage());
                }
                //创建公告操作日志
                $handleLogData = [
                    'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
                    'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
                    'editor_type'     => BaseAnnouncement::TYPE_EDITOR_JOB,
                    'handler_type'    => $this->params['platformType'],
                    'handler_id'      => $this->params['userId'],
                    'handler_name'    => $this->params['username'],
                    'handle_before'   => $handleBefore,
                    'handle_after'    => $handleAfter,
                ];
                BaseAnnouncementHandleLog::createInfo($handleLogData);
            }
        }
    }

    /**
     * 检查资源
     * @throws Exception
     */
    private function companySourcePackageCheck()
    {
        // 单位端纯职位
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && empty($this->params['announcementId'])) {
            if ($this->params['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                if ($this->oldJobInfo->company_id != $this->companyInfo->id) {
                    throw new Exception('职位不属于当前单位');
                }
                $this->baseCheckMemberPackage();
                //编辑时候没有通过历史的审核，需要判断是否超出了职位数量限制
                if ($this->oldJobInfo->status == BaseJob::STATUS_WAIT) {
                    if ($this->companyPackageConfigModel->job_amount <= 0) {
                        throw new Exception('职位发布资源不足');
                    }
                    // 发布职位
                    $companyPackageApplication = new CompanyPackageApplication();
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE];
                    $companyPackageApplication->jobRelease($this->companyInfo->id, 1, $remark);
                }
            }
        }
    }
}