<?php
/**
 * 投递的公共逻辑
 * create user：伍彦川
 * create time：2025/5/14 18:58
 */
namespace common\service\v2\job;

use admin\models\JobApply;
use admin\models\ResumeDownloadLog;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAdditionalInfo;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeDownloadLog;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use Faker\Provider\Base;
use Mpdf\Tag\P;

class BaseApplyService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取投递初始内容
     * @param $params
     * @return array
     * @throws MessageException
     */
    public function getApplyInit($params)
    {
        if (empty($params['jobId']) && empty($params['announcementId'])) {
            throw new MessageException('参数错误');
        }

        // 站内投递, 站外投递, 下载简历, 邀请面试
        $showOutside = $showOuter = $showInvite = 0;

        // 根据职位
        if (!empty($params['jobId'])) {
            $jobDetail = BaseJob::findOne($params['jobId']);

            if ($jobDetail->delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                $showOutside = 1;
            } else {
                if ($jobDetail->delivery_type == BaseJob::DELIVERY_TYPE_INSIDE) {
                    $showOuter = $showInvite = 1;
                } else {
                    if ($jobDetail->announcement_id) {
                        $announcementDetail = BaseAnnouncement::findOne($jobDetail->announcement_id);
                        if ($announcementDetail->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                            $showOutside = 1;
                        } else {
                            if ($announcementDetail->delivery_type == BaseAnnouncement::DELIVERY_TYPE_INSIDE) {
                                $showOuter = $showInvite = 1;
                            }
                        }
                    }
                }
            }
        }

        // 根据公告
        if (!empty($params['announcementId'])) {
            $announcementDetail = BaseAnnouncement::findOne($params['announcementId']);
            if ($announcementDetail->delivery_type == BaseAnnouncement::DELIVERY_TYPE_INSIDE) {
                $showOuter = $showInvite = 1;
            } else {
                if ($announcementDetail->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                    $showOutside = 1;
                }
            }

            // 公告下的职位可能与公告不同，所以需要查一下所有职位
            $announcementJobDeliveryTypeCount = BaseJob::find()
                ->select([
                    'delivery_type',
                    'count(*) as delivery_type_count',
                ])
                ->where(['announcement_id' => $params['announcementId']])
                ->groupBy('delivery_type')
                ->asArray()
                ->all();
            if ($announcementJobDeliveryTypeCount) {
                $announcementJobDeliveryTypeCount = array_column($announcementJobDeliveryTypeCount,
                    'delivery_type_count', 'delivery_type');
            }
            if (isset($announcementJobDeliveryTypeCount[BaseAnnouncement::DELIVERY_TYPE_INSIDE]) && $announcementJobDeliveryTypeCount[BaseAnnouncement::DELIVERY_TYPE_INSIDE] > 0) {
                $showOuter = $showInvite = 1;
            }
            if (isset($announcementJobDeliveryTypeCount[BaseAnnouncement::DELIVERY_TYPE_OUTSIDE]) && $announcementJobDeliveryTypeCount[BaseAnnouncement::DELIVERY_TYPE_OUTSIDE] > 0) {
                $showOutside = 1;
            }
        }

        return [
            'showTab' => [
                'outside'        => $showOutside,
                'outer'          => $showOuter,
                'invite'         => $showInvite,
                'downloadResume' => 0,
            ],
        ];
    }

    /**
     * 面试邀约sql
     */
    private function buildInterview($params)
    {
        $andWhere = ['and'];
        // 职位来的
        if (isset($params['jobId']) && $params['jobId'] != '') {
            $andWhere[] = [
                'in',
                'ja.job_id',
                [$params['jobId']],
            ];
        }

        // 公告来的
        if (isset($params['announcementId']) && $params['announcementId'] != '') {
            $jobIds     = BaseAnnouncement::getAnnouncementJobIds($params['announcementId']);
            $andWhere[] = [
                'in',
                'ja.job_id',
                $jobIds,
            ];
        }

        // 人才姓名/编号检索
        if (isset($params['resumeName']) && $params['resumeName'] != '') {
            $andWhere[] = [
                'or',
                [
                    'like',
                    'r.name',
                    $params['resumeName'],
                ],
                [
                    'like',
                    'r.uuid',
                    $params['resumeName'],
                ],
            ];
        }

        // 邀约时间
        if (!empty($params['interviewTimeStart']) && !empty($params['interviewTimeEnd'])) {
            $andWhere[] = [
                'between',
                'ci.interview_time',
                [
                    TimeHelper::dayToBeginTime($params['interviewTimeStart']),
                    TimeHelper::dayToEndTime($params['interviewTimeEnd']),
                ],
            ];
        }

        // 新增时间
        if (!empty($params['addTimeStart']) && !empty($params['addTimeEnd'])) {
            $andWhere[] = [
                'between',
                'ci.add_time',
                [
                    TimeHelper::dayToBeginTime($params['addTimeStart']),
                    TimeHelper::dayToEndTime($params['addTimeEnd']),
                ],
            ];
        }
        if (isset($params['isInvitation']) && $params['isInvitation'] != '') {
            $andWhere[] = [
                '=',
                'ja.is_invitation',
                $params['isInvitation'],
            ];
        }
        $select = [
            // 序号
            'ci.id',
            // 职位名称
            'ja.job_name',
            'j.uuid as job_uuid',
            // 人才编号
            'r.uuid as resume_uuid',
            // 人才姓名
            'r.name as resume_name',
            // 面试时间
            'ci.interview_time',
            // 投递时间
            'ja.add_time',
            // 面试创建时间
            'ci.add_time as interview_add_time',
            // 联系人
            'ci.contact',
            // 联系电话
            'ci.telephone',
            // 联系地址
            'ci.address',
        ];

        return BaseCompanyInterview::find()
            ->alias('ci')
            ->leftJoin(['ja' => JobApply::tableName()], 'ci.job_apply_id = ja.id')
            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = ja.resume_id')
            ->innerJoin(['j' => BaseJob::tableName()], 'j.id = ja.job_id')
            ->select($select)
            ->andWhere($andWhere)
            ->orderBy('ci.add_time desc');
    }

    /**
     * 根据职位id获取投递人数
     */
    public function runInterviewStat($params = [])
    {
        $query                = $this->buildInterview($params);
        $allInterviewCount    = $query->count();
        $allInterviewJobCount = $query->groupBy('ja.job_id')
            ->count();

        return [
            'allInterviewCount'    => $allInterviewCount,
            'allInterviewJobCount' => $allInterviewJobCount,
        ];
    }

    /**
     * 业务-面试邀约
     * @param $params
     * @return array
     */
    public function runInterview($params)
    {
        $params['isInvitation'] = BaseJobApply::IS_INVITATION_YES;
        $query                  = $this->buildInterview($params);
        $count                  = $query->count();
        $pageSize               = $params['limit'] ?: \Yii::$app->params['defaultPageSize'];
        $pages                  = self::setPage($count, $params['page'] ?? 1, $pageSize);

        $companyInterviewList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '邀约sql');

        $list = [];
        foreach ($companyInterviewList as $interviewInfo) {
            $list[] = [
                // 面试信息ID
                'id'               => $interviewInfo['id'],
                // 职位编号
                'jobUuid'          => $interviewInfo['job_uuid'],
                // 职位名称
                'jobName'          => $interviewInfo['job_name'],
                // 简历UUID
                'resumeUuid'       => $interviewInfo['resume_uuid'],
                // 简历名称
                'resumeName'       => $interviewInfo['resume_name'],
                // 面试时间
                'interviewTime'    => $interviewInfo['interview_time'],
                // 投递时间
                'addTime'          => $interviewInfo['add_time'],
                // 面试创建时间
                'interviewAddTime' => $interviewInfo['interview_add_time'],
            ];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    /**
     * 获取面试详情
     */
    public function getInterviewDetail($id)
    {
        $select = [
            // 序号
            'ci.id',
            // 职位名称
            'ja.job_name',
            // 面试时间
            'ci.interview_time',
            // 联系人
            'ci.contact',
            // 联系电话
            'ci.telephone',
            // 联系地址
            'ci.address',
        ];

        $detail = BaseCompanyInterview::find()
            ->alias('ci')
            ->select($select)
            ->leftJoin(['ja' => JobApply::tableName()], 'ci.job_apply_id = ja.id')
            ->where(['ci.id' => $id])
            ->asArray()
            ->one();

        if (empty($detail)) {
            throw new MessageException('面试详情不存在');
        }

        $interviewAmount = BaseCompanyInterview::find()
            ->alias('ci')
            ->leftJoin(['ja' => JobApply::tableName()], 'ci.job_apply_id = ja.id')
            ->where([
                'and',
                [
                    '=',
                    'ja.id',
                    $detail['job_apply_id'] ?? 0,
                ],
                [
                    '=',
                    'ja.is_invitation',
                    BaseJobApply::IS_INVITATION_YES,
                ],
                [
                    '<',
                    'ci.add_time',
                    $detail['add_time'],
                ],
            ])
            ->count();

        return [
            'id'              => $detail['id'],
            'jobName'         => $detail['job_name'],
            'interviewTime'   => $detail['interview_time'],
            'contact'         => $detail['contact'],
            'telephone'       => $detail['telephone'],
            'address'         => $detail['address'],
            'interviewAmount' => BaseAnnouncement::number2chinese($interviewAmount + 1),
        ];
    }

    /**
     * 获取简历下载基础sql
     */
    private function buildResumeDownloadLogQuery($params)
    {
        $select = [
            'rdl.add_time',
            'rdl.id',
            'rdl.job_id',
            'rdl.resume_name',
            'rdl.resume_id',
            'rdl.resume_attachment_id',
            'r.uuid as resume_uuid',
        ];

        $where   = ['and'];
        $where[] = ['rdl.handler_type' => BaseResumeDownloadLog::HANDLE_TYPE_ADMIN];
        if (!isset($params['jobId']) && $params['jobId'] != '') {
            $where[] = ['rdl.job_id' => $params['jobId']];
        }
        if (!isset($params['announcementId']) && $params['announcementId'] != '') {
            $jobIds  = BaseAnnouncement::getHasAuditAnnouncementJobIds($params['announcementId']);
            $where[] = ['rdl.job_id' => $jobIds];
        }
        if (!isset($params['resumeName']) && $params['resumeName'] != '') {
            $where[] = [
                'like',
                'rdl.resume_name',
                $params['resumeName'],
            ];
        }

        if (!empty($params['downloadTimeStart']) && !empty($params['downloadTimeEnd'])) {
            $where[] = [
                'between',
                'rdl.add_time',
                [
                    TimeHelper::dayToBeginTime($params['downloadTimeStart']),
                    TimeHelper::dayToEndTime($params['downloadTimeEnd']),
                ],
            ];
        }

        return ResumeDownloadLog::find()
            ->alias('rdl')
            ->innerJoin(['r' => BaseResume::tableName()], 'rdl.resume_id = r.id')
            ->select($select)
            ->where($where)
            ->orderBy('add_time desc');
    }

    /**
     * 业务-下载的简历
     */
    public function getResumeDownloadLogIndex($params)
    {
        $query = $this->buildResumeDownloadLogQuery($params);

        $count    = $query->count();
        $pageSize = $params['limit'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        //职位-下载简历列表
        $jobResumeDownloadList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->groupBy('id')
            ->asArray()
            ->all();
        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '职位-下载简历列表');

        $list = [];
        foreach ($jobResumeDownloadList as $jobResumeDownload) {
            $resumeAttachmentTitle = '';
            if ($jobResumeDownload['resume_id'] > 0) {
                $resumeTypeTitle       = '在线简历';
                $resumeAttachmentTitle = "简历" . $jobResumeDownload['resume_id'];
            } else {
                $resumeTypeTitle = '附件简历';

                $resumeAdditionalInfo = BaseResumeAttachment::find()
                    ->where(['id' => intval($jobResumeDownload['resume_attachment_id'])])
                    ->select(['file_name'])
                    ->asArray()
                    ->one();
                if ($resumeAdditionalInfo) {
                    $resumeAttachmentTitle = "(" . $resumeAdditionalInfo['file_name'] . ") " . intval($jobResumeDownload['resume_attachment_id']);
                }
            }

            $list[] = [
                'id'                    => $jobResumeDownload['id'],
                'resumeUuid'            => $jobResumeDownload['resume_uuid'],
                'resumeName'            => $jobResumeDownload['resume_name'],
                'resumeTypeTitle'       => $resumeTypeTitle,
                'resumeAttachmentTitle' => $resumeAttachmentTitle,
                'downloadTime'          => $jobResumeDownload['add_time'],
            ];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'] ?? 1,
            ],
        ];
    }

    /**
     * 业务-下载的简历统计
     */
    public function getResumeDownloadLogStat($params)
    {
        $query = $this->buildResumeDownloadLogQuery($params);

        $count       = $query->count();
        $resumeCount = $query->groupBy('resume_id')
            ->count();

        return [
            'allResumeDownloadLogCount' => $count,
            'allResumeCount'            => $resumeCount,
        ];
    }
}