<?php
/**
 * create user：shannon
 * create time：2025/5/12 下午5:10
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\helpers\ArrayHelper;
use common\helpers\UUIDHelper;
use Yii;
use yii\base\Exception;

/**
 * 职位模板列表
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TemplateListService extends BaseService
{
    const FORMAT_TYPE_K_V     = 1;
    const FORMAT_TYPE_ID_NAME = 2;
    private $formatType = self::FORMAT_TYPE_K_V;

    public function __construct()
    {
        parent::__construct();
    }

    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    public function setFormat($type)
    {
        $this->formatType = $type;

        return $this;
    }

    /**
     * 职位模板列表获取
     * @throws \yii\base\Exception
     */
    public function run()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->initInfo();
        }
        if (!$this->params['companyId']) {
            throw new Exception('没有选择单位');
        }
        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ])
            ->andWhere([
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_WAIT,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'    => BaseJob::IS_SHOW_YES,
                'company_id' => $this->params['companyId'],
            ]);

        if ($this->params['name']) {
            if (is_numeric($this->params['name'])) {
                if (mb_strlen($this->params['name']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($this->params['name']);
                    $query->andFilterCompare('id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('name', $this->params['name'], 'like');
                }
            } else {
                $query->andFilterCompare('name', $this->params['name'], 'like');
            }
        }
        $list = $query->limit(20)
            ->asArray()
            ->all();
        switch ($this->formatType) {
            case self::FORMAT_TYPE_ID_NAME:
                $data = $list;
                break;
            case self::FORMAT_TYPE_K_V:
            default:
                $data = ArrayHelper::arr2KV($list, 'id', 'name');
                break;
        }

        return $data;
    }
}