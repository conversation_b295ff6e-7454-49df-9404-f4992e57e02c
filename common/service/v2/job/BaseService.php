<?php
/**
 * create user：shannon
 * create time：2025/4/21 上午9:09
 */
namespace common\service\v2\job;

use admin\models\RuleJob;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobBoshihou;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobLog;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTemp;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseShowcase;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\ColumnAuto\JobAutoClassify;
use common\libs\WxWork;
use common\service\CommonService;
use common\service\v2\announcement\AfterService;
use queue\AfterAnnouncementUpdateJob;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 职位--职位--职位
 * 职位基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class BaseService extends CommonService
{
    protected $detail;
    protected $myData = true;//true 自己 false 公告
    protected const DEFAULT_EMPTY_VALUE = '-';
    protected const ZERO_VALUE          = '0';
    /** @var bool 是否批量 */
    protected $isBatch = false;
    /** form表单参数 */
    protected $params;
    /** 登录用户信息 */
    protected $loginInfo;
    /** 当前操作的职位ID */
    protected $jobId;
    /** 当前操作的职位临时ID */
    protected $jobTempId;
    /** 当前操作的职位的公告ID */
    protected $announcementId = 0;
    /**
     * 当前操作的职位公告信息
     * @var BaseAnnouncement
     */
    protected $announcementInfo;
    /**
     * 当前操作的职位信息
     * @var BaseJob
     */
    protected $jobInfo;
    /**
     * 当前操作的职位临时信息
     * @var BaseJobTemp
     */
    protected $jobTempInfo;
    /** 是否进过审核 */
    protected $isAudit        = false;
    protected $isWriteFileIds = false;//是否写入文件ID
    /**
     * 原始职位信息
     * @var BaseJob
     */
    protected $oldJobInfo;
    /**
     * 单位信息
     * @var BaseCompany
     */
    protected $companyInfo;

    /**
     * 单位套餐
     * @var BaseCompanyPackageConfig
     */
    protected $companyPackageConfigModel;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 验证套餐状态(基础类)
     */
    protected function baseCheckMemberPackage()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if (empty($this->companyPackageConfigModel) || $this->companyPackageConfigModel->company_id != $this->companyInfo->id) {
                $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne(['company_id' => $this->companyInfo->id]);
            }
            if (!$this->companyPackageConfigModel) {
                throw new \Exception('套餐不存在');
            }

            if ($this->companyPackageConfigModel->effect_time > CUR_DATETIME) {
                throw new \Exception('套餐还没生效');
            }

            if ($this->companyPackageConfigModel->expire_time < CUR_DATETIME) {
                throw new \Exception('套餐已过期');
            }
        }
    }

    /**
     * 设置是否是批量
     * @return $this
     */
    public function setBatch()
    {
        $this->isBatch = true;

        return $this;
    }

    /**
     * 操作日志
     * @param $type
     */
    protected function log($type)
    {
        BaseJobLog::addLog([
            'createType'     => $this->params['platformType'],
            'userId'         => $this->params['userId'],
            'createName'     => $this->operationPlatform == self::PLATFORM_WEB_COMPANY ? $this->companyInfo->full_name . '-' . $this->params['username'] : $this->params['username'],
            'type'           => $type,
            'jobId'          => $this->jobId ?: 0,
            'announcementId' => $this->announcementId ?: ($this->jobInfo->announcement_id ?: 0),
        ]);

        return $this;
    }

    /**
     * 初始化登录的账号信息、单位信息
     * @throws Exception
     */
    protected function initInfo()
    {
        //根据平台初始化登录账号信息
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB_COMPANY:
                $id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginInfo = BaseMember::findOne($id);
                //获取单位信息
                $companyMemberInfo         = BaseCompanyMemberInfo::findOne(['member_id' => $id]);
                $this->companyInfo         = BaseCompany::findOne($companyMemberInfo->company_id);
                $this->params['companyId'] = $this->companyInfo->id;
                //职位创建类型
                $this->params['createType']   = BaseJob::CREATE_TYPE_SELF;
                $this->params['platformType'] = BaseJobEdit::EDITOR_TYPE_COMPANY;
                //登录人ID
                $this->params['userId'] = $this->loginInfo->id;
                //登录人名称
                $this->params['username'] = $this->loginInfo->username;
                $this->params['userType'] = BaseJob::APPLY_ID_TYPE_COMPANY;
                break;
            case self::PLATFORM_ADMIN:
                if (isset($this->params['companyId']) && $this->params['companyId'] > 0 && empty($this->companyInfo)) {
                    $this->setCompany($this->params['companyId']);
                }
                $id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginInfo = BaseAdmin::findOne($id);
                //获取单位信息
                //$this->companyInfo = BaseCompany::findOne($this->params['companyId']);
                //职位创建类型
                $this->params['createType']   = BaseJob::CREATE_TYPE_AGENT;
                $this->params['platformType'] = BaseJobEdit::EDITOR_TYPE_PLATFORM;
                //登录人ID
                $this->params['userId'] = $this->loginInfo->id;
                //登录人名称
                $this->params['username'] = $this->loginInfo->name;
                $this->params['userType'] = BaseJob::APPLY_ID_TYPE_ADMIN;
                break;
            case self::PLATFORM_TIMER:
                if (isset($this->params['companyId']) && $this->params['companyId'] > 0 && empty($this->companyInfo)) {
                    $this->setCompany($this->params['companyId']);
                }
                //创建类型
                $this->params['createType']   = 0;
                $this->params['platformType'] = 0;
                //登录人ID
                $this->params['userId'] = 0;
                //登录人名称
                $this->params['username'] = '系统';
                $this->params['userType'] = 0;
                break;
            default:
                break;
        }
        //        if (!$this->companyInfo) {
        //            throw new Exception('职位单位信息获取失败');
        //        }
    }

    /**
     * 单位特殊验证
     */
    protected function companySpecialVerify()
    {
        // source=1跟公告 source =2跟随自己
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && isset($this->params['source']) && $this->params['source'] == 1 && $this->params['announcementId'] > 0) {
            //谨慎处理,才能进入特殊处理否侧让它独立
            //获取公告数据
            $announcementInfo         = BaseAnnouncement::findOne($this->params['announcementId']);
            $dataApplyTypeStr         = '';
            $announcementApplyTypeStr = '';
            //升-排序报名方式
            if ($this->params['applyType']) {
                $dataApplyType = explode(',', $this->params['applyType']);
                sort($dataApplyType);
                $dataApplyTypeStr = implode(',', $dataApplyType);
            }
            if ($announcementInfo->apply_type) {
                $announcementApplyType = explode(',', $announcementInfo->apply_type);
                sort($announcementApplyType);
                $announcementApplyTypeStr = implode(',', $announcementApplyType);
            }
            //是否用自己配置
            //$myData = true;//用自己的
            if ($dataApplyTypeStr == $announcementApplyTypeStr && $this->params['applyAddress'] == $announcementInfo->apply_address && $this->params['extraNotifyAddress'] == $announcementInfo->extra_notify_address) {
                //$myData = false;//说明跟公告相同那就是跟公告
                $this->myData = false;
            }
            if (!$this->myData) {//跟随公告
                //将参数清理为空跟随公告数据
                $this->params['applyType']          = '';
                $this->params['applyAddress']       = '';
                $this->params['extraNotifyAddress'] = '';
                $this->params['deliveryWay']        = 0;
                $this->params['deliveryType']       = 0;
            }
        }
    }

    /**
     * 设置职位信息
     * @param $jobId
     * @return $this
     * @throws MessageException
     */
    protected function setJob($jobId)
    {
        $this->jobId   = $jobId;
        $this->jobInfo = BaseJob::findOne($jobId);

        if (empty($jobId) || !$this->jobInfo) {
            throw new \Exception('职位id不能为空');
        }

        if ($this->jobInfo->company_id) {
            $this->setCompany($this->jobInfo->company_id);
        }

        return $this;
    }

    /**
     * 设置单位信息
     * @param $companyId
     * @throws MessageException
     */
    protected function setCompany($companyId)
    {
        if (empty($companyId)) {
            throw new MessageException('请传递单位ID不能为空');
        }
        if (empty($this->companyInfo) || !$this->companyInfo->id != $companyId) {
            $this->companyInfo = BaseCompany::findOne($companyId);
        }

        return $this;
    }

    /**
     * 校验数据合法性
     * @throws Exception
     */
    protected function dataVerify($isAdd = true, $isTemp = false)
    {
        $companyInfo = $this->companyInfo;

        // 基础必填校验
        $required = [
            'companyId'     => '请选择所属单位',
            'name'          => '职位名称不能为空',
            'jobCategoryId' => '职位类型不能为空',
            'educationType' => '请选择学历要求',
            'amount'        => '招聘人数不能为空',
            'provinceId'    => '工作地点省份不能为空',
            'cityId'        => '工作地点城市不能为空',
            'duty'          => '岗位职责不能为空',
            'requirement'   => '任职要求不能为空',
        ];
        foreach ($required as $key => $msg) {
            if (empty($this->params[$key])) {
                throw new Exception($msg);
            }
        }

        // 长度校验
        if (mb_strlen($this->params['duty']) > 2300) {
            throw new Exception('岗位职责字符长度不允许超过2300');
        }
        if (mb_strlen($this->params['requirement']) > 2300) {
            throw new Exception('任职要求字符长度不允许超过2300');
        }
        if (!empty($this->params['remark']) && mb_strlen($this->params['remark']) > 2300) {
            throw new Exception('其他说明字符长度不允许超过2300');
        }

        // 截止日期校验
        if (strtotime($this->params['periodDate']) > 1 && strtotime($this->params['periodDate']) < strtotime(date('Y-m-d'))) {
            throw new Exception('职位截止日期不能小于当前时间');
        }

        // 附件数量校验
        if (count(explode(',', $this->params['fileIds'])) > 10) {
            throw new Exception('职位附件不能超过10个');
        }

        // 报名方式与通知邮箱不可同时填写
        if (!empty($this->params['applyType']) && !empty($this->params['extraNotifyAddress'])) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }

        // if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && !$this->isBatch) {
        //专业不允许为空
        //            if (empty($this->params['majorIds'])) {
        //                throw new Exception('专业不能为空');
        //            }
        // }

        // 截止日期必填
        // if (!$isTemp && !$this->isBatch && (!isset($this->params['announcementId']) || $this->params['announcementId'] == 0) && (empty($this->params['periodDate']) || $this->params['periodDate'] == TimeHelper::ZERO_TIME)) {
        if (!$isTemp && (!isset($this->params['announcementId']) || $this->params['announcementId'] == 0) && (empty($this->params['periodDate']) || $this->params['periodDate'] == TimeHelper::ZERO_TIME)) {
            throw new Exception('职位截止日期必须选择');
        }

        //运营后台  报名方式与通知邮箱填写了其中一个则提示勾选投递方式
        if ($this->operationPlatform == self::PLATFORM_ADMIN && $companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES && empty($this->params['deliveryWay']) && ($this->params['applyType'] || $this->params['applyAddress'] || $this->params['extraNotifyAddress'])) {
            throw new Exception('请选择投递方式');
        }
        // 合作单位
        if ($companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            $this->params['extraNotifyAddress'] = $this->params['extraNotifyAddress'] ?? '';
            if ($this->params['extraNotifyAddress']) {
                $this->params['extraNotifyAddress'] = BaseJob::checkEmailApplyAddress($this->params['extraNotifyAddress']);
            }

            // 协同账号校验
            $this->params['jobContactSynergyIds'] = $this->params['jobContactSynergyIds'] ?? [];
            if (is_string($this->params['jobContactSynergyIds'])) {
                $this->params['jobContactSynergyIds'] = explode(',', $this->params['jobContactSynergyIds']);
            }
            if (count($this->params['jobContactSynergyIds']) > 3) {
                throw new Exception('协同账号最多设置3个');
            }
            $jobContactSynergyIds = [];
            foreach ($this->params['jobContactSynergyIds'] as $item) {
                if (BaseCompanyMemberInfo::validateMemberRecordId($companyInfo->id, $item, 2)) {
                    $jobContactSynergyIds[] = $item;
                }
            }
            $this->params['jobContactSynergyIds'] = $jobContactSynergyIds;

            // 职位联系人校验
            if (empty($this->params['jobContactId'])) {
                throw new Exception('职位联系人必须设置');
            }
            if (!BaseCompanyMemberInfo::validateMemberRecordId($companyInfo->id, $this->params['jobContactId'], 3)) {
                throw new Exception('职位联系人设置错误');
            }
            $mainMember = BaseCompanyMemberInfo::findOne([
                'company_id'          => $companyInfo->id,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
            if (!in_array($this->params['jobContactId'],
                array_unique(array_merge($this->params['jobContactSynergyIds'], [$mainMember->id])))) {
                throw new Exception('职位联系人必须是协同账号或者是单位主账号');
            }

            // 报名方式和投递地址校验
            $onlyOneFilled = (!empty($this->params['applyType']) && empty($this->params['applyAddress'])) || (empty($this->params['applyType']) && !empty($this->params['applyAddress']));
            if ($isTemp || (!empty($this->params['announcementId']) && $this->params['announcementId'] > 0)) {
                if ($onlyOneFilled) {
                    throw new Exception('合作单位公告下职位报名方式和投递地址必须同时填写或者不填写！');
                }
            } else {
                if ($this->operationPlatform == self::PLATFORM_ADMIN && $this->params['deliveryWay'] != BaseJob::DELIVERY_WAY_PLATFORM && (empty($this->params['applyType']) || empty($this->params['applyAddress']))) {
                    throw new Exception('合作纯职位报名方式、投递地址必须全部填写！');
                }
                if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $onlyOneFilled) {
                    throw new Exception('合作纯职位报名方式、投递地址必须同时填写或者不填写！');
                }
            }
            // 投递方式和类型处理
            if ($this->operationPlatform == self::PLATFORM_ADMIN) {
                if (!empty($this->params['deliveryWay'])) {
                    if ($this->params['deliveryWay'] != BaseJob::DELIVERY_WAY_PLATFORM) {
                        $applyTypeArr = explode(',', $this->params['applyType']);
                        $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                        if ($isEmail) {
                            $this->params['applyAddress'] = BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                            $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_EMAIL;
                        } else {
                            if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                                throw new Exception('投递地址网址格式错误');
                            }
                            $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                        }
                    } else {
                        $this->params['applyType']    = '';
                        $this->params['applyAddress'] = '';
                        $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_PLATFORM;
                    }
                } else {
                    $this->params['applyType']    = '';
                    $this->params['applyAddress'] = '';
                    $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_UP_ANNOUNCEMENT;
                }
            } else {
                if ($isAdd || $this->myData) {
                    if (!empty($this->params['applyType']) && !empty($this->params['applyAddress'])) {
                        $applyTypeArr = explode(',', $this->params['applyType']);
                        $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                        if ($isEmail) {
                            $this->params['applyAddress'] = BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                            $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_EMAIL;
                        } else {
                            if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                                throw new Exception('投递地址网址格式错误');
                            }
                            $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                        }
                    } else {
                        $this->params['applyType']    = '';
                        $this->params['applyAddress'] = '';
                        $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_PLATFORM;
                    }
                }

                // 带有source==2 跟自己 没有source
                //                if ((isset($this->params['source']) && $this->params['source'] == 2) || (!isset($this->params['source']) && (!isset($this->params['deliveryType']) || !isset($this->params['deliveryWay'])))) {
                //                    if (!empty($this->params['applyType']) && !empty($this->params['applyAddress'])) {
                //                        $applyTypeArr = explode(',', $this->params['applyType']);
                //                        $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //                        if ($isEmail) {
                //                            $this->params['applyAddress'] = BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                //                            $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_EMAIL;
                //                        } else {
                //                            if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                //                                throw new Exception('投递地址网址格式错误');
                //                            }
                //                            $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                //                        }
                //                    } else {
                //                        $this->params['applyType']    = '';
                //                        $this->params['applyAddress'] = '';
                //                        $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_PLATFORM;
                //                    }
                //                }

                //                if (!isset($this->params['source']) || $this->params['source'] == 2) {
                //                    if (!empty($this->params['applyType']) && !empty($this->params['applyAddress'])) {
                //                        $applyTypeArr = explode(',', $this->params['applyType']);
                //                        $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //                        if ($isEmail) {
                //                            $this->params['applyAddress'] = BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                //                            $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_EMAIL;
                //                        } else {
                //                            if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                //                                throw new Exception('投递地址网址格式错误');
                //                            }
                //                            $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                //                        }
                //                    } else {
                //                        $this->params['applyType']    = '';
                //                        $this->params['applyAddress'] = '';
                //                        $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_PLATFORM;
                //                    }
                //                }
            }

            // 处理投递类型
            if ($this->params['deliveryWay'] == BaseJob::DELIVERY_WAY_UP_ANNOUNCEMENT) {
                $this->params['deliveryType'] = BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT;
            } elseif ($this->params['deliveryWay'] == BaseJob::DELIVERY_WAY_LINK) {
                $this->params['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $this->params['deliveryType'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
        } else {
            //非合作进来就删除deliveryWay 让其重新计算
            unset($this->params['deliveryWay']);
            if (!empty($this->params['extraNotifyAddress'])) {
                throw new Exception('非合作单位不支持填写邮件通知地址');
            }
            if ($isTemp || (!empty($this->params['announcementId']) && $this->params['announcementId'] > 0)) {
                if ((!empty($this->params['applyType']) && empty($this->params['applyAddress'])) || (empty($this->params['applyType']) && !empty($this->params['applyAddress']))) {
                    throw new Exception('非合作单位公告下职位报名方式和投递地址必须同时填写或者不填写！');
                }
            } else {
                if (empty($this->params['applyType']) || empty($this->params['applyAddress'])) {
                    throw new Exception('非合作纯职位报名方式、投递地址必须全部填写！');
                }
            }
            if (!empty($this->params['applyType'])) {
                $applyTypeArr = explode(',', $this->params['applyType']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if ($isEmail) {
                    $this->params['applyAddress'] = BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                    $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                    $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $this->params['applyType']    = '';
                $this->params['applyAddress'] = '';
                $this->params['deliveryWay']  = BaseJob::DELIVERY_WAY_UP_ANNOUNCEMENT;
            }
            $this->params['deliveryType'] = ($this->params['deliveryWay'] == BaseJob::DELIVERY_WAY_UP_ANNOUNCEMENT) ? BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT : BaseJob::DELIVERY_TYPE_OUTSIDE;
        }

        // 薪资范围(非年薪)
        if ($this->params['wageType'] != BaseJob::WAGE_TYPE_YEAR && $this->params['isNegotiable'] == BaseJob::IS_NEGOTIABLE_NO && !empty($this->params['wageId'])) {
            $wageInfo                = BaseDictionary::getMinAndMaxWage($this->params['wageId']);
            $this->params['minWage'] = (int)$wageInfo['min'];
            $this->params['maxWage'] = (int)$wageInfo['max'];
        }
    }

    /**
     * 保存职位正式的表
     * @return bool
     */
    protected function saveTable($isAdd = true)
    {
        if ($isAdd) {
            $model              = new BaseJob();
            $model->create_type = $this->params['createType'];
            $model->create_id   = $this->params['userId'];
            $model->creator     = $this->params['username'];
            // 新增保存都是这种状态
            $model->status           = BaseJob::STATUS_WAIT;
            $model->audit_status     = $this->params['auditStatus'] ?: BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            $model->is_show          = BaseJob::IS_SHOW_YES;
            $model->is_article       = $this->params['announcementId'] > 0 ? BaseJob::IS_ARTICLE_YES : BaseJob::IS_ARTICLE_NO;
            $model->file_ids         = $this->params['fileIds'] ?: '';
            $model->duty             = $this->params['duty'] ?: "";
            $model->remark           = $this->params['remark'] ?: "";
            $model->requirement      = $this->params['requirement'] ?: "";
            $model->apply_audit_time = CUR_DATETIME;
            //新增时$model->audit_status为待审核则写入申请人
            if ($model->audit_status == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                $model->apply_id      = $this->params['userId'];
                $model->apply_name    = $this->params['username'];
                $model->apply_id_type = $this->params['userType'];
                //是单位端发布审核时候就标记需要扣资源 运营则不扣
                $model->is_consume_release = $this->operationPlatform == self::PLATFORM_WEB_COMPANY ? BaseJob::IS_CONSUME_RELEASE_YES : BaseJob::IS_CONSUME_RELEASE_NO;
            } else {
                $model->is_consume_release = BaseJob::IS_CONSUME_RELEASE_NO;
            }
        } else {
            $model = BaseJob::findOne($this->params['jobId']);
            //判断是否进入审核流程
            if ($this->isAudit) {
                //进入审核流程
                $model->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
                $model->apply_audit_time = CUR_DATETIME;
                //进入审核流程的编辑修改申请人
                $model->apply_id      = $this->params['userId'];
                $model->apply_name    = $this->params['username'];
                $model->apply_id_type = $this->params['userType'];
                // 公告+职位模式编辑
                if ($model->is_article == BaseJob::IS_ARTICLE_YES) {
                    // 修改公告审核状态
                    $announcementModel                = BaseAnnouncement::findOne($model->announcement_id);
                    $announcementModel->audit_status  = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                    $announcementModel->apply_id      = $this->params['userId'];
                    $announcementModel->apply_name    = $this->params['username'];
                    $announcementModel->apply_id_type = $this->params['userType'];
                    if (!$announcementModel->save()) {
                        throw new Exception($announcementModel->getFirstErrorsMessage());
                    }
                    //修改申请时间
                    $articleModel                   = BaseArticle::findOne($announcementModel->article_id);
                    $articleModel->apply_audit_time = CUR_DATETIME;
                    if (!$articleModel->save()) {
                        throw new Exception($articleModel->getFirstErrorsMessage());
                    }
                }
                if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $model->status == BaseJob::STATUS_WAIT && $model->is_consume_release == BaseJob::IS_CONSUME_RELEASE_NO) {
                    $model->is_consume_release = BaseJob::IS_CONSUME_RELEASE_YES;
                }
                //单位端 必须有有新增文件才审核
                if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->isWriteFileIds) {
                    $model->file_ids = $this->params['fileIds'] ?: '';
                }
                //运营这个字段不审核 直接写入
                if ($this->operationPlatform == self::PLATFORM_ADMIN) {
                    $model->file_ids = $this->params['fileIds'] ?: '';
                }
                //没有审核通过历史的职位 提交审核直接修改字段
                if ($model->status == BaseJob::STATUS_WAIT) {
                    $model->duty        = $this->params['duty'] ?: "";
                    $model->remark      = $this->params['remark'] ?: "";
                    $model->requirement = $this->params['requirement'] ?: "";
                }
            } else {
                $model->duty        = $this->params['duty'] ?: "";
                $model->remark      = $this->params['remark'] ?: "";
                $model->requirement = $this->params['requirement'] ?: "";
                $model->file_ids    = $this->params['fileIds'] ?: '';
            }
        }
        // 公共字段赋值
        $this->assignJobCommonFields($model);
        //判断操作平台为运营平台
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            $model->establishment_type = $this->params['establishmentType'] ?: '';
            $model->is_establishment   = !empty($this->params['establishmentType']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            //单位端-添加
            if ($isAdd) {
                $model->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
            }
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //设置一下写入信息----后置赋值
        $this->jobId   = $model->id;
        $this->jobInfo = $model;

        //写一条职位附属表逻辑
        BaseJobExtra::insertData([
            'job_id'          => $model->id,
            'announcement_id' => $this->params['announcementId'] ?? 0,
            'company_id'      => $this->companyInfo->id,
        ]);

        return true;
    }

    /**
     * 公共字段赋值
     */
    protected function assignJobCommonFields($model)
    {
        $model->announcement_id      = $this->params['announcementId'] ?: 0;
        $model->member_id            = $this->companyInfo->member_id;
        $model->company_id           = $this->companyInfo->id;
        $model->is_job_cooperation   = $this->companyInfo->is_cooperation;
        $model->name                 = $this->params['name'];
        $model->code                 = $this->params['code'] ?: "";
        $model->job_category_id      = $this->params['jobCategoryId'] ?: "";
        $model->education_type       = $this->params['educationType'] ?: "";
        $model->major_id             = $this->params['majorId'] ?: "";
        $model->nature_type          = $this->params['natureType'] ?: "";
        $model->wage_type            = $this->params['wageType'] ?: "";
        $model->is_negotiable        = $this->params['isNegotiable'] ?: "";
        $model->min_wage             = $this->params['minWage'] ?: "";
        $model->max_wage             = $this->params['maxWage'] ?: "";
        $model->experience_type      = $this->params['experienceType'] ?: "";
        $model->age_type             = $this->params['ageType'] ?: '';
        $model->title_type           = $this->params['titleType'] ?: "";
        $model->political_type       = $this->params['politicalType'] ?: "";
        $model->abroad_type          = $this->params['abroadType'] ?: "";
        $model->amount               = $this->params['amount'] ?: "";
        $model->department           = $this->params['department'] ?: "";
        $model->province_id          = $this->params['provinceId'] ?: "";
        $model->city_id              = $this->params['cityId'] ?: "";
        $model->district_id          = $this->params['districtId'] ?: "";
        $model->address              = $this->params['address'] ?: "";
        $model->welfare_tag          = $this->params['welfareTag'] ?: '';
        $model->period_date          = $this->params['periodDate'] ?: TimeHelper::ZERO_TIME;
        $model->apply_type           = $this->params['applyType'] ?: "";
        $model->apply_address        = $this->params['applyAddress'] ?: '';
        $model->delivery_type        = $this->params['deliveryType'];
        $model->delivery_way         = $this->params['deliveryWay'];
        $model->extra_notify_address = $this->params['extraNotifyAddress'] ?: '';
        $model->delivery_limit_type  = $this->params['deliveryLimitType'] ?: '';
        $model->search_name          = $this->params['name'] . $this->companyInfo->full_name;
    }

    /**
     * 对职位的联系人与协同人进行处理
     * @param $params
     */
    protected function contact()
    {
        if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $common_insert                            = [
                'job_id'          => $this->jobId,
                'company_id'      => $this->companyInfo->id,
                'announcement_id' => $this->params['announcementId'] ?? 0,
            ];
            $contact_insert                           = $common_insert;
            $contact_insert['company_member_info_id'] = $this->params['jobContactId'];
            BaseJobContact::add($contact_insert);
            $contact_synergy_insert                           = $common_insert;
            $contact_synergy_insert['company_member_info_id'] = $this->params['jobContactSynergyIds'];
            BaseJobContactSynergy::addBatch($contact_synergy_insert);
        }
    }

    /**
     * 处理所有后置逻辑
     * @throws Exception
     */
    protected function runAutoColumnAfter()
    {
        //        if (Yii::$app->params['environment'] == 'prod') {
        Producer::afterAnnouncementUpdateJob($this->jobId, AfterAnnouncementUpdateJob::TYPE_JOB);
        //        } else {
        //            $auto = new JobAutoClassify($this->jobId);
        //            $auto->run();
        //        }
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }


    /**
     * 审核
     */

    /**
     * 更新职位的福利中间表
     * @return bool
     */
    protected function updateJobWelfareRelationTable()
    {
        if (!$this->jobInfo) {
            return true;
        }
        //获取职位的福利
        $jobWelfareIdsStr = $this->jobInfo->welfare_tag;
        //炸成数组
        $jobWelfareIdsArr = explode(',', $jobWelfareIdsStr);
        //去重
        $jobWelfareIdsArr = array_unique($jobWelfareIdsArr);
        //删除职位的福利中间表数据
        BaseJobWelfareRelation::deleteAll(['job_id' => $this->jobId]);
        //写入表中
        foreach ($jobWelfareIdsArr as $jobWelfareId) {
            $jobWelfareInfo                   = BaseJobWelfareRelation::findOne([
                'job_id'     => $this->jobId,
                'welfare_id' => $jobWelfareId,
            ]);
            $jobWelfareModel                  = $jobWelfareInfo ?: new BaseJobWelfareRelation();
            $jobWelfareModel->announcement_id = $this->jobInfo->announcement_id;
            $jobWelfareModel->job_id          = $this->jobId;
            $jobWelfareModel->welfare_id      = $jobWelfareId;
            $jobWelfareModel->save();
        }

        return true;
    }

    /**
     * 更新职位的专业中间表
     * @return bool
     */
    protected function updateJobMajorRelationTable()
    {
        //获取职位的专业
        $jobMajorIdsStr = $this->jobInfo->major_id;
        //炸成数组
        $jobMajorIdsArr = explode(',', $jobMajorIdsStr);
        //去重
        $jobMajorIdsArr = array_unique($jobMajorIdsArr);
        //去掉隐藏的
        if (count(array_intersect($jobMajorIdsArr, BaseMajor::HIDE_MAJOR_ID)) > 0) {
            $jobMajorIdsArr          = array_diff($jobMajorIdsArr, BaseMajor::HIDE_MAJOR_ID);
            $this->jobInfo->major_id = count($jobMajorIdsArr) > 0 ? implode(',', $jobMajorIdsArr) : '';
            $this->jobInfo->save();
        }

        //删除职位的专业中间表数据
        BaseJobMajorRelation::deleteAll(['job_id' => $this->jobId]);

        //写入表中
        foreach ($jobMajorIdsArr as $jobMajorId) {
            $jobMajorInfo                   = BaseJobMajorRelation::findOne([
                'job_id'   => $this->jobId,
                'major_id' => $jobMajorId,
            ]);
            $level                          = BaseMajor::findOneVal(['id' => $jobMajorId], 'level');
            $jobMajorModel                  = $jobMajorInfo ?: new BaseJobMajorRelation();
            $jobMajorModel->announcement_id = $this->jobInfo->announcement_id;
            $jobMajorModel->job_id          = $this->jobId;
            $jobMajorModel->major_id        = $jobMajorId;
            $jobMajorModel->level           = $level;
            $jobMajorModel->save();
            if ($level == 2) {
                //获取上级major
                $parent_id   = BaseMajor::findOneVal(['id' => $jobMajorId], 'parent_id');
                $parent_info = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobInfo->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            } elseif ($level == 3) {
                //获取上级major
                $parent_id     = BaseMajor::findOneVal(['id' => $jobMajorId], 'parent_id');
                $parent_info_1 = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_id,
                    'level'    => 2,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobInfo->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 2;
                    $jobMajorModel->save();
                }
                $parent_parent_id = BaseMajor::findOneVal(['id' => $parent_id], 'parent_id');
                $parent_info_1    = BaseJobMajorRelation::findOne([
                    'job_id'   => $this->jobId,
                    'major_id' => $parent_parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $this->jobInfo->announcement_id;
                    $jobMajorModel->job_id          = $this->jobId;
                    $jobMajorModel->major_id        = $parent_parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            }
        }

        return true;
    }

    /**
     * 更新职位的类型中间表
     * @return bool
     */
    protected function updateJobCategoryRelationTable()
    {
        //获取职位的类型
        $jobCategoryId = $this->jobInfo->job_category_id;
        //获取职位的类型信息
        $categoryJobInfo = BaseCategoryJob::findOne(['id' => $jobCategoryId]);
        //删除职位的类型中间表数据
        BaseJobCategoryRelation::deleteAll(['job_id' => $this->jobId]);
        if ($categoryJobInfo->level == 2) {
            $categoryJobParentInfo = BaseCategoryJob::findOne(['id' => $categoryJobInfo->parent_id]);
            //是否存在信息
            $jobCategoryParentInfo             = BaseJobCategoryRelation::findOne([
                'job_id'      => $this->jobId,
                'category_id' => $categoryJobParentInfo->id,
            ]);
            $jobCategoryModel                  = $jobCategoryParentInfo ?: new BaseJobCategoryRelation();
            $jobCategoryModel->announcement_id = $this->jobInfo->announcement_id;
            $jobCategoryModel->job_id          = $this->jobId;
            $jobCategoryModel->category_id     = $categoryJobParentInfo->id;
            $jobCategoryModel->level           = $categoryJobParentInfo->level;
            $jobCategoryModel->save();
        }
        //写入自己的信息
        $jobCategoryInfo                   = BaseJobCategoryRelation::findOne([
            'job_id'      => $this->jobId,
            'category_id' => $jobCategoryId,
        ]);
        $jobCategoryModel                  = $jobCategoryInfo ?: new BaseJobCategoryRelation();
        $jobCategoryModel->announcement_id = $this->jobInfo->announcement_id;
        $jobCategoryModel->job_id          = $this->jobId;
        $jobCategoryModel->category_id     = $jobCategoryId;
        $jobCategoryModel->level           = $categoryJobInfo->level;
        $jobCategoryModel->save();

        return true;
    }

    /**
     * 更新职位是否是小程序职位
     */
    protected function updateJobMiniAppType()
    {
        if ($this->jobInfo->is_manual_tag == BaseJob::IS_MANUAL_TAG_NONE) {
            $model     = new RuleJob();
            $res       = $model->exec($this->jobId);
            $isMiniApp = $res ? BaseJob::IS_MINIAPP_YES : BaseJob::IS_MINIAPP_NO;
        } elseif ($this->jobInfo->is_manual_tag == BaseJob::IS_MANUAL_TAG_YES) {
            $isMiniApp = BaseJob::IS_MINIAPP_YES;
        } else {
            $isMiniApp = BaseJob::IS_MINIAPP_NO;
        }
        if ($this->jobInfo->is_miniapp != $isMiniApp) {
            $this->jobInfo->is_miniapp = $isMiniApp;
            $this->jobInfo->save();
        }
    }

    /**
     * 更新公告的编制字段
     */
    protected function updateAnnouncementEstablishment()
    {
        $announcementId = $this->jobInfo->announcement_id;
        if ($announcementId) {
            //查询更新公告编制字段
            $establishmentType                     = BaseAnnouncement::getEstablishmentType($announcementId);
            $announcementModel                     = BaseAnnouncement::findOne($announcementId);
            $announcementModel->establishment_type = $establishmentType;
            $announcementModel->save();
        }
    }

    /**
     * 更新职位附属表的PI标识
     */
    protected function updateJobPiFlag()
    {
        //先看下是否有附属记录
        $jobExtraInfo = BaseJobExtra::findOne(['job_id' => $this->jobInfo->id]);
        if (!$jobExtraInfo) {
            //没有就补一下
            BaseJobExtra::insertData([
                'announcement_id' => $this->jobInfo->announcement_id,
                'company_id'      => $this->jobInfo->company_id,
                'job_id'          => $this->jobInfo->id,
            ]);
            $jobExtraInfo = BaseJobExtra::findOne(['job_id' => $this->jobInfo->id]);
        }
        //看下自身是否有pi属性
        if (($this->jobInfo->announcement_id > 0 && BaseArticleAttribute::find()
                    ->where([
                        'article_id' => $this->announcementInfo->article_id,
                        'type'       => BaseArticleAttribute::ATTRIBUTE_PI,
                    ])
                    ->exists()) || BaseCompanyFeatureTagRelation::find()
                ->where([
                    'company_id'     => $this->jobInfo->company_id,
                    'feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                ])
                ->exists()) {
            $jobExtraInfo->is_pi = BaseJobExtra::IS_PI_YES;
        } else {
            $jobExtraInfo->is_pi = BaseJobExtra::IS_PI_NO;
        }
        if ($jobExtraInfo->is_pay == BaseJobExtra::IS_PAY_NO || $jobExtraInfo->is_boshihou_pay == BaseJobExtra::IS_PAY_NO) {
            BaseShowcase::updateCompanyStatIsPay([$this->jobInfo->company_id]);
        }
        $jobExtraInfo->save();
    }

    /**
     * 更新博士后职位表
     */
    protected function updateJobBoShiHouTable()
    {
        if (BaseJobBoshihou::find()
                ->where(['job_id' => $this->jobId])
                ->exists() && !in_array($this->jobInfo->job_category_id, self::BOSHIHOU_CATEGORY_ID)) {
            //删除
            BaseJobBoshihou::deleteAll(['job_id' => $this->jobId]);
        }
        if (!BaseJobBoshihou::find()
                ->where(['job_id' => $this->jobId])
                ->exists() && in_array($this->jobInfo->job_category_id, self::BOSHIHOU_CATEGORY_ID)) {
            // 添加
            $model         = new BaseJobBoshihou();
            $model->job_id = $this->jobId;
            $model->save();
        }

        return true;
    }

    /**
     * 更新单位扩展表的职位相关
     * @return void
     */
    protected function updateStatInfo()
    {
        $statModel = BaseCompanyStatData::find()
            ->where(['company_id' => $this->jobInfo->company_id])
            ->one();

        if (!$statModel) {
            $statModel             = new BaseCompanyStatData();
            $statModel->company_id = $this->jobInfo->company_id;
        }

        // 当前职位满足博士后栏目
        if (in_array($this->jobInfo->job_category_id, self::BOSHIHOU_CATEGORY_ID) && in_array($this->jobInfo->status, [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ]) && $this->jobInfo->is_show == BaseJob::IS_SHOW_YES) {
            $statModel->is_boshihou_column = BaseCompanyStatData::IS_BOSHIHOU_COLUMN;
        } else {
            // 查询这个单位满足上线的职位
            $boshihouExists = BaseJob::find()
                ->select(['id'])
                ->where([
                    'company_id'      => $this->jobInfo->company_id,
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'status'          => BaseJob::STATUS_ONLINE,
                    'job_category_id' => self::BOSHIHOU_CATEGORY_ID,
                ])
                ->exists();
            if ($boshihouExists) {
                $statModel->is_boshihou_column = BaseCompanyStatData::IS_BOSHIHOU_COLUMN;
            } else {
                $statModel->is_boshihou_column = BaseCompanyStatData::IS_NOT_BOSHIHOU_COLUMN;
            }
        }

        $onLineCount = BaseJob::find()
            ->where([
                'company_id' => $this->jobInfo->company_id,
                'is_show'    => BaseJob::IS_SHOW_YES,
                'status'     => BaseJob::STATUS_ONLINE,
            ])
            ->count();

        $allCount = BaseJob::find()
            ->where([
                'company_id' => $this->jobInfo->company_id,
                'is_show'    => BaseJob::IS_SHOW_YES,
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->count();

        $statModel->all_job_count    = $allCount;
        $statModel->online_job_count = $onLineCount;
        $statModel->save();
    }

    /**
     * 更新排序
     * @return void
     */
    public function updateJobCompanySort()
    {
        // 拿单位的sort更新到职位的company_sort里面
        $model = BaseJob::findOne($this->jobId);
        if (!$model->company_sort != $this->companyInfo->sort) {
            $model->company_sort = $this->companyInfo->sort;
            $model->save();
        }
    }

    /**
     * 处理基本信息
     */
    protected function processBasicInfo()
    {
        // 处理公司信息
        $this->processCompanyInfo();
        // 处理投递相关信息
        $this->processDeliveryInfo(true);
        // 处理职位分类信息
        $this->processJobCategoryInfo();
        // 处理地区信息
        $this->processAreaInfo();
        // 处理福利标签
        $this->processWelfareTag();
        // 处理编制信息
        $this->processEstablishmentInfo();
        // 处理薪资信息
        $this->processWageInfo();
        //处理文件列表
        $this->processFileInfo();
        // 处理联系人与协同联系人
        $this->processContactInfo();
        // 处理学历和专业信息
        $this->processEducationAndMajorInfo();
        // 处理其他基本信息
        $this->processOtherBasicInfo();
        //处理一下空值
        $this->processDefaultValue();
    }

    /**
     * 处理公司信息
     */
    protected function processCompanyInfo()
    {
        $companyInfo = BaseCompany::findOne($this->detail['companyId']);
        if ($companyInfo) {
            $this->detail['companyName']            = $companyInfo->full_name;
            $this->detail['companyNature']          = $companyInfo->nature;
            $this->detail['companyNatureTitle']     = BaseDictionary::getCompanyNatureName($companyInfo->nature);
            $this->detail['companyTypeTitle']       = BaseDictionary::getCompanyTypeName($companyInfo->type);
            $this->detail['companyType']            = $companyInfo->type;
            $this->detail['isCooperation']          = $companyInfo->is_cooperation;
            $this->detail['companyDeliveryType']    = $companyInfo->delivery_type;
            $this->detail['companyDeliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$companyInfo->delivery_type] ?? self::DEFAULT_EMPTY_VALUE;
        }
    }

    /**
     * 处理投递相关信息
     */
    protected function processDeliveryInfo($isText = false)
    {
        if ($isText) {
            // 处理投递类型--处理投递方式
            $this->detail['applyTypeTxt'] = '';
            if ($this->detail['deliveryType'] == BaseJob::DELIVERY_WAY_UP_ANNOUNCEMENT) {
                if ($this->detail['announcementId'] > 0) {
                    $announcementInfo                = BaseAnnouncement::findOne($this->detail['announcementId']);
                    $this->detail['deliveryTypeTxt'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$announcementInfo->delivery_type] ?? self::DEFAULT_EMPTY_VALUE;
                    $this->setDeliveryWayText($announcementInfo->delivery_way, true);
                    $this->processJobApplyText($announcementInfo->apply_type);
                    $this->detail['applyType']    = $announcementInfo->apply_type;
                    $this->detail['applyAddress'] = $announcementInfo->apply_address;
                } else {
                    $this->detail['deliveryType']    = '';
                    $this->detail['deliveryTypeTxt'] = self::DEFAULT_EMPTY_VALUE;
                    $this->detail['deliveryWay']     = '';
                    $this->detail['deliveryWayTxt']  = self::DEFAULT_EMPTY_VALUE;
                    $this->detail['applyTypeTxt']    = self::DEFAULT_EMPTY_VALUE;
                    $this->detail['applyAddress']    = '';
                }
            } else {
                $this->detail['deliveryTypeTxt'] = BaseJob::DELIVERY_TYPE_NAME[$this->detail['deliveryType']] ?? self::DEFAULT_EMPTY_VALUE;
                $this->setDeliveryWayText($this->detail['deliveryWay'], false);
                $this->processJobApplyText($this->detail['applyType']);
            }
        } else {
            $this->detail['deliveryTypeTxt'] = BaseJob::DELIVERY_TYPE_NAME[$this->detail['deliveryType']] ?? self::DEFAULT_EMPTY_VALUE;
            $this->setDeliveryWayText($this->detail['deliveryWay'], false);
            $this->processJobApplyText($this->detail['applyType']);
        }

        // 处理投递限制
        if ($this->detail['deliveryLimitType']) {
            $deliveryLimitTypeArr                 = explode(',', $this->detail['deliveryLimitType']);
            $deliveryLimitTypeNameArr             = array_map(function ($val) {
                return BaseJob::DELIVERY_LIMIT_LIST[$val] ?? '';
            }, $deliveryLimitTypeArr);
            $this->detail['deliveryLimitTypeTxt'] = implode(',', array_filter($deliveryLimitTypeNameArr));
        }
    }

    /**
     * 处理职位分类信息
     */
    protected function processJobApplyText($applyType)
    {
        if ($applyType) {
            $applyTypeArr = explode(',', $applyType);
            if (count($applyTypeArr) > 1) {
                $applyTypeTxtArr = [];
                foreach ($applyTypeArr as $v) {
                    $applyTypeTxtArr[] = BaseDictionary::getSignUpName($v);
                }
                $this->detail['applyTypeTxt'] = implode(',', $applyTypeTxtArr);
            } else {
                $this->detail['applyTypeTxt'] = BaseDictionary::getSignUpName($applyType);
            }
        }
    }

    /**
     * 处理职位分类信息
     */
    protected function processJobCategoryInfo()
    {
        if (!empty($this->detail['jobCategoryId'])) {
            $jobCategoryInfo = BaseCategoryJob::findOne($this->detail['jobCategoryId']);
            if ($jobCategoryInfo) {
                $this->detail['jobCategoryTitle'] = BaseCategoryJob::getName($jobCategoryInfo->parent_id) . ' - ' . $jobCategoryInfo->name;
            }
        }
    }

    /**
     * 处理地区信息
     */
    protected function processAreaInfo()
    {
        $areaInfo = [];
        if (!empty($this->detail['provinceId'])) {
            $areaInfo[]                    = BaseArea::getAreaName($this->detail['provinceId']);
            $this->detail['provinceTitle'] = $this->detail['provinceId'] > 0 ? BaseArea::getAreaName($this->detail['provinceId']) : '';
        }
        if (!empty($this->detail['cityId'])) {
            $areaInfo[]                = BaseArea::getAreaName($this->detail['cityId']);
            $this->detail['cityTitle'] = $this->detail['cityId'] > 0 ? BaseArea::getAreaName($this->detail['cityId']) : '';
        }
        if (!empty($this->detail['districtId'])) {
            $areaInfo[]                    = BaseArea::getAreaName($this->detail['districtId']);
            $this->detail['districtTitle'] = $this->detail['districtId'] > 0 ? BaseArea::getAreaName($this->detail['districtId']) : '';
        }
        $this->detail['areaName'] = implode('', $areaInfo);
        if (!empty($this->detail['address'])) {
            $areaInfo[] = $this->detail['address'];
        }
        $this->detail['areaText'] = implode('', $areaInfo);
    }

    /**
     * 处理福利标签
     */
    protected function processWelfareTag()
    {
        if (!empty($this->detail['welfareTag'])) {
            $welfareTags    = explode(',', $this->detail['welfareTag']);
            $welfareTagList = BaseWelfareLabel::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where([
                    'in',
                    'id',
                    $welfareTags,
                ])
                ->andWhere(['is_delete' => BaseWelfareLabel::SYSTEM_NO])
                ->asArray()
                ->all();

            $this->detail['welfareTagTitle'] = implode(',', array_column($welfareTagList, 'name'));
            $this->detail['welfareTage']     = ArrayHelper::arr2KV($welfareTagList, 'id', 'name');
        } else {
            $this->detail['welfareTagTitle'] = '';
            $this->detail['welfareTage']     = [];
        }
    }

    /**
     * 处理编制信息
     */
    protected function processEstablishmentInfo()
    {
        if ($this->detail['isEstablishment'] == BaseJob::IS_ESTABLISHMENT_YES) {
            $this->detail['establishmentTxt'] = BaseJob::getEstablishmentName($this->detail['establishmentType']);
        }
    }

    /**
     * 处理薪资信息
     */
    protected function processWageInfo()
    {
        if ($this->detail['isNegotiable'] != 1) {
            $this->detail['wageId'] = (string)BaseJob::getWageId($this->detail['minWage'], $this->detail['maxWage']);
        }
        $this->detail['wage'] = BaseJob::formatWage($this->detail['minWage'], $this->detail['maxWage'],
            $this->detail['wageType']) ?: '';
    }

    /**
     * 处理附件
     */
    protected function processFileInfo()
    {
        if ($this->detail['fileIds']) {
            $this->detail['fileList'] = BaseAnnouncement::getAppendixList($this->detail['fileIds']);
        } else {
            if (!empty($this->detail['announcementId'])) {
                $fileIds                  = BaseAnnouncement::findOneVal(['id' => $this->detail['announcementId']],
                    'file_ids');
                $this->detail['fileList'] = BaseAnnouncement::getAppendixList($fileIds);
            } else {
                $this->detail['fileList'] = [];
            }
        }
    }

    /**
     * 处理学历和专业信息
     */
    protected function processEducationAndMajorInfo()
    {
        // 处理学历要求
        if ($this->detail['educationType']) {
            $this->detail['educationTypeTitle'] = BaseDictionary::getEducationName($this->detail['educationType']);
        } else {
            $this->detail['educationTypeTitle'] = '';
        }

        // 处理专业要求
        if ($this->detail['majorId']) {
            $this->detail['majorTitle'] = BaseMajor::getAllMajorName(explode(',', $this->detail['majorId']));
        } else {
            $this->detail['majorTitle'] = '';
        }
    }

    /**
     * 处理联系人与协同联系人
     */
    protected function processContactInfo()
    {
        //单位子账号配置
        $companySubAccountConfig                 = BaseCompanyMemberConfig::findOne(['company_id' => $this->detail['companyId']]);
        $this->detail['companySubAccountConfig'] = $companySubAccountConfig ? $companySubAccountConfig->toArray() : [];
        $contact                                 = BaseJob::getJobContact($this->detail['jobId']);
        $this->detail['jobContact']              = $contact;
        $this->detail['jobContactId']            = $contact['company_member_info_id'];

        $contact_synergy                      = BaseJob::getJobContactSynergy($this->detail['jobId']);
        $this->detail['jobContactSynergy']    = $contact_synergy;
        $this->detail['jobContactSynergyNum'] = count($contact_synergy);
        $this->detail['jobContactSynergyIds'] = $this->detail['jobContactSynergyNum'] > 0 ? array_column($contact_synergy,
            'company_member_info_id') : [];
    }

    /**
     * 处理其他基本信息
     */
    protected function processOtherBasicInfo()
    {
        // 处理性质类型
        if ($this->detail['natureType']) {
            $this->detail['natureTypeTitle'] = BaseDictionary::getNatureName($this->detail['natureType']);
        } else {
            $this->detail['natureTypeTitle'] = self::DEFAULT_EMPTY_VALUE;
        }

        // 处理薪资类型
        if ($this->detail['wageType']) {
            $this->detail['wageTypeTitle'] = BaseJob::JOB_WAGE_TYPE_NAME[$this->detail['wageType']];
        }

        // 处理经验要求
        if ($this->detail['experienceType']) {
            $this->detail['experienceTypeTitle'] = $this->detail['experienceType'] < 1 ? "经验不限" : BaseDictionary::getExperienceName($this->detail['experienceType']);
        } else {
            $this->detail['experienceTypeTitle'] = "经验不限";
        }

        // 处理职称要求
        if ($this->detail['titleType']) {
            $this->detail['titleTypeTitle'] = $this->detail['titleType'] < 1 ? "职称不限" : BaseDictionary::getTitleName($this->detail['titleType']);
        } else {
            $this->detail['titleTypeTitle'] = self::DEFAULT_EMPTY_VALUE;
        }

        // 处理政治面貌
        if ($this->detail['politicalType']) {
            $this->detail['politicalTypeTitle'] = $this->detail['politicalType'] < 1 ? "不限" : BaseDictionary::getPoliticalStatusName($this->detail['politicalType']);
        } else {
            $this->detail['politicalTypeTitle'] = self::DEFAULT_EMPTY_VALUE;
        }

        // 处理海外经历
        $this->detail['abroadTypeTitle'] = $this->detail['abroadType'] ? BaseDictionary::getAbroadName($this->detail['abroadType']) : self::DEFAULT_EMPTY_VALUE;
    }

    /**
     * 处理默认值
     */
    protected function processDefaultValue()
    {
        // 处理数值相关字段
        $zeroFields = [
            'experienceType',
            'politicalType',
            'abroadType',
            'natureType',
            'wageType',
            'ageType',
            'titleType',
            'minWage',
            'maxWage',
            'minAge',
            'maxAge',
            'districtId',
            'applyAddress',
            'extraNotifyAddress',
            'majorId',
            'natureType',
            'ageType',
            'titleType',
            'politicalType',
            'abroadType',
            'department',
            'remark',
            'code',
            'educationType',
            'experienceType',
        ];

        foreach ($zeroFields as $field) {
            if (isset($this->detail[$field]) && $this->detail[$field] === self::ZERO_VALUE) {
                $this->detail[$field] = '';
            }
        }

        //        // 处理空值字段
        //        $emptyFields = [
        //            'majorId',
        //            'natureType',
        //            'ageType',
        //            'titleType',
        //            'politicalType',
        //            'abroadType',
        //            'department',
        //            'remark',
        //            'code',
        //            'educationType',
        //            'experienceType',
        //        ];
        //
        //        foreach ($emptyFields as $field) {
        //            if (empty($this->detail[$field])) {
        //                $this->detail[$field] = StringHelper::isEmpty($this->detail[$field]);
        //            }
        //        }

        // 处理时间相关字段
        if ($this->detail['periodDate'] == TimeHelper::ZERO_TIME) {
            $this->detail['periodDate']    = '';
            $this->detail['periodDateTxt'] = '';
        }
        if ($this->detail['releaseTime'] == TimeHelper::ZERO_TIME) {
            $this->detail['releaseTime'] = $this->detail['addTime'];
        }
    }

    /**
     * 设置投递方式文本
     */
    protected function setDeliveryWayText($deliveryWay, $isAnnouncement)
    {
        $baseClass = $isAnnouncement ? BaseAnnouncement::class : BaseJob::class;
        if ($deliveryWay == $baseClass::DELIVERY_WAY_PLATFORM) {
            $this->detail['deliveryWayTxt'] = $baseClass::DELIVERY_WAY_SELECT_NAME[$baseClass::DELIVERY_WAY_PLATFORM];
        } elseif (in_array($deliveryWay, $baseClass::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $this->detail['deliveryWayTxt'] = $baseClass::DELIVERY_WAY_SELECT_NAME[$baseClass::DELIVERY_WAY_EMAIL_LINK];
            $this->detail['deliveryWay']    = strval($baseClass::DELIVERY_WAY_EMAIL_LINK);
        } else {
            $this->detail['deliveryWay'] = '';
        }
    }

    /**
     * 临时职位表
     */
    protected function saveTempTable($isAdd = true)
    {
        if ($isAdd) {
            $model              = new BaseJobTemp();
            $model->create_type = BaseJobTemp::CREATE_TYPE_ADD;
            // member_id、company_id、announcement_id三个字段虽然写入了 但是尽量不要使用
            $model->member_id       = $this->companyInfo->member_id;
            $model->company_id      = $this->companyInfo->id;
            $model->announcement_id = $this->params['announcementId'] ?: 0;
            $model->job_id          = $this->params['jobId'] ?: 0;
            $model->is_temp         = BaseJobTemp::IS_TEMP_YES;
            $model->audit_status    = BaseJob::AUDIT_STATUS_WAIT;
        } else {
            $model = BaseJobTemp::findOne(['id' => $this->params['id']]);
        }
        //
        //        if ($oldJob) {
        //            if ($oldJob->delivery_type == 0 && $data['announcement_id'] > 0 && $data['delivery_type'] != 0) {
        //                $announcementInfo = BaseAnnouncement::findOne($data['announcement_id']);
        //                if ($announcementInfo->delivery_type != $data['delivery_type']) {
        //                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
        //                }
        //            } elseif ($oldJob->delivery_type > 0 && $data['announcement_id'] > 0 && $data['delivery_type'] == 0 && $this->operatorType == self::OPERATOR_TYPE_COMPANY) {
        //                //单位端去验证这一层吧 不是单位端就不验证了
        //                $announcementInfo = BaseAnnouncement::findOne($data['announcement_id']);
        //                if ($oldJob->delivery_type != $announcementInfo->delivery_type) {
        //                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
        //                }
        //            } elseif ($oldJob->delivery_type > 0 && $data['delivery_type'] > 0) {
        //                if ($oldJob->delivery_type != $data['delivery_type']) {
        //                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
        //                }
        //            }
        //        }

        $model->name                 = $this->params['name'];
        $model->code                 = $this->params['code'] ?: '';
        $model->job_category_id      = $this->params['jobCategoryId'] ?: 0;
        $model->education_type       = $this->params['educationType'] ?: 0;
        $model->major_id             = $this->params['majorId'] ?: '';
        $model->nature_type          = $this->params['natureType'] ?: 0;
        $model->experience_type      = $this->params['experienceType'] ?: 0;
        $model->wage_type            = $this->params['wageType'] ?: 0;
        $model->is_negotiable        = $this->params['isNegotiable'] ?: BaseJob::IS_NEGOTIABLE_NO;
        $model->apply_type           = $this->params['applyType'] ?: '';
        $model->apply_address        = $this->params['applyAddress'] ?: '';
        $model->min_wage             = $this->params['minWage'] ?: 0;
        $model->max_wage             = $this->params['maxWage'] ?: 0;
        $model->age_type             = $this->params['ageType'] ?: '0';
        $model->title_type           = $this->params['titleType'] ?: 0;
        $model->political_type       = $this->params['politicalType'] ?: 0;
        $model->abroad_type          = $this->params['abroadType'] ?: 0;
        $model->amount               = $this->params['amount'];
        $model->department           = $this->params['department'] ?: '';
        $model->province_id          = $this->params['provinceId'] ?: 0;
        $model->city_id              = $this->params['cityId'] ?: 0;
        $model->address              = $this->params['address'] ?: '';
        $model->welfare_tag          = $this->params['welfareTag'] ?: '';
        $model->period_date          = $this->params['periodDate'] ?: TimeHelper::ZERO_TIME;
        $model->duty                 = $this->params['duty'] ?: '';
        $model->requirement          = $this->params['requirement'] ?: '';
        $model->remark               = $this->params['remark'] ?: '';
        $model->delivery_limit_type  = $this->params['deliveryLimitType'] ?: '';
        $model->delivery_type        = $this->params['deliveryType'] ?: 0;
        $model->delivery_way         = $this->params['deliveryWay'] ?: 0;
        $model->extra_notify_address = $this->params['extraNotifyAddress'] ?: '';
        $model->contact_id           = $this->params['jobContactId'] ?: 0;
        $model->contact_synergy_id   = is_array($this->params['jobContactSynergyIds']) ? implode(',',
            $this->params['jobContactSynergyIds']) : trim($this->params['jobContactSynergyIds']);
        //判断操作平台为运营平台
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            $model->establishment_type = $this->params['establishmentType'] ?: '';
            $model->is_establishment   = !empty($this->params['establishmentType']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            //单位端-添加
            if ($isAdd) {
                $model->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
            }
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        $this->jobTempId   = $model->id;
        $this->jobTempInfo = $model;
    }

    /**
     * 处理一下添加临时的数据时候需要返回Item给前端展示
     */
    protected function tempItem()
    {
        //防止Key不是小驼峰做做一个转换
        $item           = FormatConverter::convertLine($this->jobTempInfo);
        $data['id']     = $item['id'];
        $data['name']   = $item['name'];
        $data['jobId']  = $item['jobId'];
        $data['isTemp'] = $item['isTemp'];
        //canDel true显示 false 隐藏
        if ($item['jobId'] > 0) {
            $jobInfo                = BaseJob::findOne($item['jobId']);
            $data['status']         = $jobInfo->status;
            $data['statusTxt']      = BaseJob::JOB_STATUS_NAME[$jobInfo->status];
            $data['auditStatus']    = $jobInfo->audit_status;
            $data['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[$jobInfo->audit_status];
            if ($jobInfo->status != BaseJob::STATUS_WAIT) {
                $data['canDel'] = false;
            } else {
                $data['canDel'] = true;
            }
        } else {
            $data['status']         = BaseJob::STATUS_WAIT;
            $data['statusTxt']      = BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_WAIT];
            $data['auditStatus']    = BaseJob::AUDIT_STATUS_WAIT;
            $data['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[BaseJob::AUDIT_STATUS_WAIT];
            $data['canDel']         = true;
        }

        $data['department']           = $item['department'];
        $data['isCooperation']        = $this->companyInfo->is_cooperation;
        $contactSynergyIds            = $item['contactSynergyId'] ? explode(',', $item['contactSynergyId']) : [];
        $data['jobContactSynergy']    = count($contactSynergyIds) > 0 ? BaseCompanyMemberInfo::getInfoMany($contactSynergyIds) : [];
        $data['jobContactSynergyNum'] = count($data['jobContactSynergy']);
        $data['jobContactSynergyIds'] = $contactSynergyIds;
        foreach ($item['jobContactSynergy'] as &$contactItem) {
            $contactItem['is_contact'] = 0;
            if ($contactItem['id'] == $item['contactId']) {
                $contactItem['is_contact'] = 1;
            }
        }
        $data['jobContact']   = BaseCompanyMemberInfo::getInfoOne($item['contactId']);
        $data['jobContactId'] = $item['contactId'];
        $information          = [];
        if ($item['cityId']) {
            $information[] = BaseArea::getAreaName($item['cityId']);
        }
        if ($item['amount']) {
            $information[] = "招{$item['amount']}人";
        }
        if ($item['educationType']) {
            $information[] = BaseDictionary::getEducationName($item['educationType']);
        }
        if (!$item['minWage'] && !$item['maxWage']) {
            $information[] = '面议';
        } else {
            $information[] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']) ?: '-';
        }
        $data['majorId']    = $item['majorId'];
        $data['majorTitle'] = '';
        if ($item['majorId']) {
            $data['majorTitle'] = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
            $information[]      = $data['majorTitle'];
        }
        $data['information'] = implode(' | ', $information);

        return $data;
    }

    public function afterUpdateJob()
    {
        $this->updateStatInfo();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
            BaseShowcase::updateCompanyStatIsPay([$this->jobInfo->company_id]);
        }

        return $this;
    }

    /**
     * 更新当前新增的职位ID的UUid
     * @return void
     * @throws Exception
     */
    protected function updateUuid()
    {
        $model       = BaseJob::findOne($this->jobId);
        $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $model->id);
        $model->save();
    }
}