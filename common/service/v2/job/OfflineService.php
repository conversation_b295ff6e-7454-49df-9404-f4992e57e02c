<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\service\company\PushEditMessageService;
use common\service\jobTop\JobTopApplication;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 职位状态
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class OfflineService extends BaseService
{
    public $isAnnouncement = false;

    public $jobId;
    public $reason;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置是否公告过来的下线
     * 这里是防止死循，公告过来执行职位，职位又去执行公告，所以这里处理一下
     */
    public function setIsAnnouncement()
    {
        $this->isAnnouncement = true;

        return $this;
    }

    /**
     * 设置参数
     * @param $params
     */
    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    /**
     * 前置检查
     */
    public function runCheck()
    {
        $this->jobId = Yii::$app->request->get('jobId');
        if (!$this->jobId) {
            throw new MessageException('参数错误');
        }
        $jobInfo = BaseJob::findOne($this->jobId);
        if (!$jobInfo) {
            throw new MessageException('职位不存在');
        }
        if ($jobInfo->announcement_id > 0) {
            $countJob = BaseJob::find()
                ->where([
                    'announcement_id' => $jobInfo->announcement_id,
                    'status'          => BaseJob::STATUS_ONLINE,
                ])
                ->count();
            if ($countJob == 1) {
                return [
                    'count' => $countJob,
                    'msg'   => '该职位为公告下唯一在线职位！下线后，该职位及所属公告将一并下线。确定操作吗？',
                ];
            }
        }

        return true;
    }

    /**
     * 职位状态变更
     * @throws Exception
     */
    public function run()
    {
        if (!$this->params) {
            $this->params = Yii::$app->request->post();
        }
        if (!$this->params['jobId']) {
            throw new MessageException('参数错误');
        }
        if ($this->params['isCooperation'] != 2 && (!$this->params['reason'] && $this->operationPlatform != self::PLATFORM_WEB_COMPANY)) {
            throw new MessageException('下线原因不能为空');
        }

        $this->initInfo();
        $ids = explode(',', $this->params['jobId']);
        $msg = '';

        if (count($ids) > 0 && $this->isBatch) {
            //批量
            foreach ($ids as $idItem) {
                $this->jobId = $idItem;
                $itemRes     = $this->offlineOne();
                if ($itemRes !== true) {
                    $msg .= $itemRes;
                }
            }
        } else {
            $this->jobId = $this->params['jobId'];
            //单个
            $this->offlineOne();
            $ids[] = $this->params['jobId'];
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            foreach ($ids as $idItem) {
                (new PushEditMessageService())->offline(['jobId' => $idItem]);
            }
        }

        return empty($msg) ? true : $msg;
    }

    /**
     * 单个职位下线
     * @throws Exception
     */
    private function offlineOne()
    {
        $jobModel = BaseJob::findOne($this->jobId);
        if (!$jobModel) {
            throw new MessageException('职位不存在');
        }
        //单个操作
        if ($jobModel->status != BaseJob::STATUS_ONLINE) {
            if ($this->isBatch && $this->operationPlatform != self::PLATFORM_WEB_COMPANY) {
                return '职位ID：' . $jobModel->uuid . ',非在线状态，不允许操作下线<br />';
            } else {
                throw new MessageException('当前职位是非在线状态，不允许操作下线！');
            }
        }
        //初始化一写数据
        $oldStatus = $jobModel->status;
        $this->setCompany($jobModel->company_id);

        if ($jobModel->announcement_id > 0) {
            $countJob = BaseJob::find()
                ->where([
                    'announcement_id' => $jobModel->announcement_id,
                    'status'          => BaseJob::STATUS_ONLINE,
                ])
                ->count();
            if ($countJob == 1 && !$this->isAnnouncement) {
                //这时候公告直接一并下线--这时候直接走去公告下线流程就好了
                (new \common\service\v2\announcement\OfflineService())->setPlatform($this->operationPlatform)
                    ->setIsJob()
                    ->offline([
                        'announcementId' => $jobModel->announcement_id,
                        'reason'         => $this->params['reason'],
                    ]);
            }
        }
        // 运营后台操作下线=违规
        switch ($this->operationPlatform) {
            // admin端
            case self::PLATFORM_ADMIN:
                $jobModel->offline_type = BaseJob::OFFLINE_TYPE_VIOLATION;
                break;
            // 企业端
            case  self::PLATFORM_WEB_COMPANY:
                $jobModel->offline_type = BaseJob::OFFLINE_TYPE_HAND;
                break;
            default:
                $jobModel->offline_type = BaseJob::OFFLINE_TYPE_AUTO;
                break;
        }
        $jobModel->status       = BaseJob::STATUS_OFFLINE;
        $jobModel->offline_time = CUR_DATETIME;
        // 不应该修改到期时间
        // $model->period_date    = $this->data['periodDate'];
        $jobModel->offline_reason = $this->params['reason'] ?: '';
        if (!$jobModel->save()) {
            throw new MessageException('下线失败');
        }
        $this->jobInfo = $jobModel;
        //添加下线日志
        BaseJobHandleLog::createInfo([
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobId,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_OFFLINE,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '职位状态' => BaseJob::JOB_STATUS_NAME[$oldStatus],
            ]),
            'handle_after'    => json_encode([
                '职位状态' => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_OFFLINE],
                '下线时间' => CUR_DATETIME,
                '下线原因' => $this->params['reason'],
            ]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
        ]);
        //写一下日志
        $this->log($this->isBatch ? BaseJobLog::TYPE_OFFLINE_BATCH : BaseJobLog::TYPE_OFFLINE);
        //更新置顶状态
        (JobTopApplication::getInstance())->updateStatusByJobStatus($this->jobId);
        //执行后置操作
        $this->after();

        return true;
    }

    /**
     * 执行后置操作
     * @return void
     * @throws Exception
     */
    private function after()
    {
        $this->updateStatInfo();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id && !$this->isAnnouncement) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }
}