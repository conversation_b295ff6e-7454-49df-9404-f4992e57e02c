<?php

namespace common\service\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMajor;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use yii\base\Exception;

class BatchJobService extends BaseService
{
    /**
     * 执行添加
     * @throws Exception
     */
    public function run()
    {
        if ($this->saveType == self::SAVE_TYPE_AUDIT) {
            $this->beforeRun();
        }
        $this->batchAddRun();

        if ($this->saveType == self::SAVE_TYPE_AUDIT) {
            $this->afterRun();
        }
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): BatchJobService
    {
        $checkData = [
            'name',
            'job_category_id',
            'education_type',
            'amount',
            'duty',
            'requirement',
        ];

        foreach ($checkData as $list) {
            if (strlen($data[$list]) < 1) {
                throw new Exception('参数' . $list . '不能为空');
            }
        }

        $this->actionType = self::ACTION_TYPE_ADD;
        if ($data['status'] == BaseJob::STATUS_WAIT_AUDIT) {
            $this->saveType           = self::SAVE_TYPE_AUDIT;
            $data['apply_audit_time'] = CUR_DATETIME;
        } else {
            $this->saveType = self::SAVE_TYPE_STAGING;
        }

        // 开始组装数据
        $data['is_temp'] = BaseJobTemp::IS_TEMP_YES;

        // 薪资范围
        if ($data['min_wage_month'] > 0 && $data['max_wage_month'] > 0) {
            // 月薪
            $data['wage_type']     = BaseJob::TYPE_WAGE_MONTH;
            $data['min_wage']      = $data['min_wage_month'];
            $data['max_wage']      = $data['max_wage_month'] ?: $data['min_wage_month'];
            $data['is_negotiable'] = BaseJob::IS_NEGOTIABLE_YES;
        } elseif ($data['min_wage_year'] > 0 || $data['max_wage_year'] > 0) {
            // 年薪
            $data['wage_type']     = BaseJob::TYPE_WAGE_YEARS;
            $data['min_wage']      = $data['min_wage_year'];
            $data['max_wage']      = $data['max_wage_year'] ?: $data['min_wage_year'];
            $data['is_negotiable'] = BaseJob::IS_NEGOTIABLE_YES;
        } else {
            // 面议
            $data['wage_type'] = BaseJob::TYPE_WAGE_NEGOTIABLE;
            $data['min_wage']  = $data['min_wage_month'];
            $data['max_wage']  = $data['max_wage_month'];
        }
        //报名方式与通知地址不可同时填写
        if ($data['apply_type'] && $data['extra_notify_address']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        $data['extra_notify_address'] = $data['extra_notify_address'] ?: '';
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO && isset($data['delivery_way']) && $data['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
            throw new Exception('报名方式填写错误');
        }
        if (isset($data['delivery_way']) && $data['delivery_way'] > 0) {
            if ($data['delivery_way'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($data['apply_type']) || empty($data['apply_address'])) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['apply_address']);
                } else {
                    if (!ValidateHelper::isUrl($data['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {//deliveryWay=1
                $data['apply_type']    = '';
                $data['apply_address'] = '';
            }
        } else {
            if ($data['apply_type']) {
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if (empty($data['apply_address'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['apply_address']);
                } else {
                    if (!ValidateHelper::isUrl($data['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //判断投递方式
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $data['apply_type']    = '';
                $data['apply_address'] = '';
                //判断投递方式
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_PLATFORM;
                } else {
                    $data['delivery_way'] = 0;
                }
            }
        }

        //检查通知邮箱的格式
        if ($data['extra_notify_address']) {
            $data['extra_notify_address'] = BaseJob::checkEmailApplyAddress($data['extra_notify_address']);
        }
        //处理纯职位投递类型；选择投递类型那就站外投递，没有就站内投递
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;//跟公告
            } elseif ($data['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
        } else {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        }

        //处理职位联系人
        $data['job_contact_id'] = BaseCompanyMemberInfo::findOneVal([
            'company_id'          => $data['company_id'],
            'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
        ], 'id');

        $this->jobData = $data;

        return $this;
    }

    /**
     * 添加
     * @throws Exception
     */
    private function batchAddRun()
    {
        $data  = $this->jobData;
        $model = new BaseJob();//shannon删除【BaseJob::findOne(['id' => $data['id']]) ?:】

        $data['major_id']        = implode(',', $data['major_id']);
        $data['welfare_tag']     = implode(',', $data['welfare_tag']);
        $delivery_limit_type_arr = [];
        if (isset($data['delivery_limit_type_file']) && !empty($data['delivery_limit_type_file'])) {
            array_push($delivery_limit_type_arr, $data['delivery_limit_type_file']);
        }
        if (isset($data['delivery_limit_type_education']) && !empty($data['delivery_limit_type_education'])) {
            array_push($delivery_limit_type_arr, $data['delivery_limit_type_education']);
        }
        $delivery_limit_type = '';
        if (count($delivery_limit_type_arr) > 0) {
            $delivery_limit_type = implode(',', $delivery_limit_type_arr);
        }
        $model->announcement_id = $data['announcement_id'] ?: 0;
        $model->name            = $data['name'];
        $model->code            = $data['code'] ?: '';
        $model->job_category_id = $data['job_category_id'];
        $model->education_type  = $data['education_type'];
        $model->major_id        = $data['major_id'] ?: '';
        $model->nature_type     = $data['nature_type'];
        $model->wage_type       = $data['wage_type'];
        $model->is_negotiable   = $data['is_negotiable'];
        $model->apply_type      = $data['apply_type'] ?: '';
        $model->apply_address   = $data['apply_address'] ?: '';
        $model->min_wage        = $data['min_wage'];
        $model->max_wage        = $data['max_wage'];
        $model->experience_type = $data['experience_type'];
        $model->age_type        = $data['age_type'];
        $model->title_type      = $data['title_type'];
        $model->political_type  = $data['political_type'];
        $model->abroad_type     = $data['abroad_type'];
        $model->amount          = $data['amount'];
        $model->department      = $data['department'];
        $model->province_id     = $data['province_id'];
        $model->city_id         = $data['city_id'];
        $model->welfare_tag     = $data['welfare_tag'] ?: '';
        $model->period_date     = $data['period_date'] ?: TimeHelper::ZERO_TIME;
        $model->duty            = $data['duty'];
        $model->requirement     = $data['requirement'];
        $model->remark          = $data['remark'];
        //        $model->create_type          = !empty($data['id']) ? self::CREATE_TYPE_EDIT : self::CREATE_TYPE_ADD;
        $model->is_consume_release   = BaseJob::IS_CONSUME_RELEASE_NO;
        $model->status               = $data['status'] ?: BaseJob::STATUS_WAIT;
        $model->audit_status         = $data['status'] ?: BaseJob::AUDIT_STATUS_WAIT;
        $model->member_id            = $data['member_id'];
        $model->company_id           = $data['company_id'];
        $model->is_article           = BaseJob::IS_ARTICLE_NO;
        $model->is_show              = BaseJob::IS_SHOW_YES;
        $model->create_id            = $this->operatorId ?: 0;
        $model->creator              = $this->operatorUserName ?: '';
        $model->create_type          = $this->create_type ?: 1;
        $model->apply_audit_time     = $data['apply_audit_time'] ?: '0000-00-00 00:00:00';
        $model->delivery_limit_type  = $delivery_limit_type;
        $model->delivery_way         = $data['delivery_way'] ?: 0;
        $model->delivery_type        = $data['delivery_type'] ?: 0;
        $model->extra_notify_address = $data['extra_notify_address'] ?: '';
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //添加职位联系人
        $contact_insert = [
            'job_id'                 => $model->id,
            'company_id'             => $data['company_id'],
            'announcement_id'        => $data['announcement_id'] ?: 0,
            'company_member_info_id' => $data['job_contact_id'],
        ];
        BaseJobContact::add($contact_insert);
        //写一条职位附属表逻辑
        BaseJobExtra::insertData([
            'job_id'          => $model->id,
            'announcement_id' => $data['announcement_id'] ?: 0,
            'company_id'      => $data['company_id'],
        ]);
    }

}
