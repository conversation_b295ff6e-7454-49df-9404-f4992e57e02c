<?php

namespace common\service\commonResume;

// 下载服务
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseJob;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeStatData;
use common\components\MessageException;
use common\helpers\StringHelper;
use common\helpers\UUIDHelper;
use common\service\companyPackage\CompanyPackageApplication;
use Yii;
use yii\base\Exception;

// 下载的逻辑其实是下载文件+消费点数,这里有个需要注意的,主要下载过这个简历一次,那么这个收费后面就不会再收取了
class DownloadService extends BaseService
{

    protected $companyId;
    protected $resumeId;
    protected $resumeMemberId;
    protected $resumeLevel;

    // “消费记录“-“简历下载”服务类型，备注栏补充简历信息：
    //
    // 简历类型：XXX；人才姓名:XXX；人才ID:XXX

    /**
     * 在人才库的id
     */
    protected $companyResumeLibraryId;

    public function beforeDownload($companyId, $resumeId)
    {
        $this->companyId = $companyId;
        $this->resumeId  = $resumeId;
        $this->setData();

        //获取备注信息
        $remark = BaseResumeLibrary::getResumeLevelRemark();

        //下载前的判断，主要是下载点数方面
        $model = BaseResume::findOne($resumeId);
        if (!$model) {
            throw new MessageException('简历不存在');
        }
        // 检查在不在人才库
        if (!BaseResume::find()
            ->where([
                'id'                => $resumeId,
                'is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
            ])
            ->exists()) {
            throw new MessageException('简历不在人才库');
        }
        // 检查自己是否有套餐
        if ($this->isFreePackage) {
            throw new MessageException('普通会员不能进行下载');
        }
        //排除已经下载的数据
        $companyResumeLibraryInfo = BaseCompanyResumeLibrary::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $this->companyId,
        ]);
        if (!empty($companyResumeLibraryInfo)) {
            throw new MessageException('该简历已经下载到简历库中');
        }

        $downLoadAmount = $this->companyPackageConfigModel->resume_download_amount;

        $this->resumeLevel = $this->getResumeLevel($resumeId);

        if (empty($downLoadAmount)) {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_EMPTY_POINT,
                'needPoint'    => (int)$this->resumeLevel['point'],
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        }

        if ($downLoadAmount < $this->resumeLevel['point']) {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_NOT_ENOUGH_POINT,
                'needPoint'    => (int)$this->resumeLevel['point'],
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        } else {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_TIPS,
                'needPoint'    => (int)$this->resumeLevel['point'],
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        }
    }

    /**
     * 批量下载简历前置判断
     * @param $companyId
     * @param $resumeIds
     * @return array
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function beforeBatchDownload($companyId, $resumeIds)
    {
        if (empty($resumeIds)) {
            throw new MessageException('简历id不能为空');
        }

        $this->companyId = $companyId;
        $this->setCompany($this->companyId);
        $this->setPackage();
        //由于是批量处理，简历判断需要单独处理
        $needTotalPoint = 0;
        foreach ($resumeIds as $K => $id) {
            $model = BaseResume::findOne($id);
            if (!$model) {
                throw new MessageException('简历不存在');
            }
            // 检查在不在人才库
            if (!BaseResume::find()
                ->where([
                    'id'                => $id,
                    'is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
                ])
                ->exists()) {
                throw new MessageException('简历不在人才库');
            }
            // 检查自己是否有套餐
            if ($this->isFreePackage) {
                throw new MessageException('普通会员不能进行下载');
            }

            //排除已经下载的数据
            $companyResumeLibraryInfo = BaseCompanyResumeLibrary::findOne([
                'resume_id'  => $id,
                'company_id' => $this->companyId,
            ]);
            if (!empty($companyResumeLibraryInfo)) {
                continue;
            }

            //如果没有问题，获取简历点数
            $levelInfo      = $this->getResumeLevel($id);
            $needTotalPoint += $levelInfo['point'];
        }

        //获取备注信息
        $remark = BaseResumeLibrary::getResumeLevelRemark();

        //下载前的判断，主要是下载点数方面
        $downLoadAmount = $this->companyPackageConfigModel->resume_download_amount;

        if (empty($downLoadAmount)) {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_EMPTY_POINT,
                'needPoint'    => (int)$needTotalPoint,
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        }

        if ($downLoadAmount < $needTotalPoint) {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_NOT_ENOUGH_POINT,
                'needPoint'    => (int)$needTotalPoint,
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        } else {
            return [
                'infoType'     => BaseResumeLibrary::DOWNLOAD_INFO_TYPE_TIPS,
                'needPoint'    => (int)$needTotalPoint,
                'surplusPoint' => $downLoadAmount,
                'remark'       => $remark,
            ];
        }
    }

    public function run($companyId, $resumeId)
    {
        $this->companyId  = $companyId;
        $this->resumeId   = $resumeId;
        $this->actionType = self::ACTION_TYPE_DOWNLOAD;

        $this->setData();
        $this->check();
        $this->create();
        $this->log();

        return $this->companyResumeLibraryId;
    }

    private function setData()
    {
        $this->setCompany($this->companyId);
        $this->setPackage();
        $this->setResume($this->resumeId);
    }

    private function check()
    {
        // 检查一下这个简历在不在简历库
        if (!BaseResume::find()
            ->where([
                'id'                => $this->resumeId,
                'is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
            ])
            ->exists()) {
            throw new MessageException('简历不存在人才库');
        }

        // 检查自己是否有套餐啊
        if ($this->isFreePackage) {
            throw new MessageException('普通会员不能进行下载');
        }

        // 检查自己的简历库里面是否已经有存在了这个简历
        $handelLoginId = BaseCompanyResumeLibrary::findOneVal([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
        ], 'id');

        if ($handelLoginId) {
            $this->companyResumeLibraryId = $handelLoginId;

            return;
        }

        $downLoadAmount = $this->companyPackageConfigModel->resume_download_amount;

        $this->resumeLevel = $this->getResumeLevel($this->resumeId);

        if ($downLoadAmount < $this->resumeLevel['point']) {
            throw new MessageException('下载点数不足');
        }

        // if ($this->isSendSms) {
        //     if ($this->companyPackageConfigModel->sms_amount <= 1) {
        //     }
        // }

        // 检查一下对于这个简历是否已经邀约过了
        // 这里需要看看是否发送短信, 如果发送短信, 则需要看看短信余额是否充足

    }

    public function create()
    {
        if ($this->companyResumeLibraryId) {
            return;
        }

        // 如果没有,就下载到人才库,并且写一个下载记录?
        $model                    = new BaseCompanyResumeLibrary();
        $model->company_id        = $this->companyId;
        $model->resume_id         = $this->resumeId;
        $model->source_type       = BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD;
        $model->download_time     = CUR_DATETIME;
        $model->company_member_id = Yii::$app->user->id;
        if (!$model->save()) {
            throw new MessageException('下载失败');
        }

        // 统计数据也更新一下
        BaseResumeStatData::updateAllCounters([
            'resume_library_download_amount' => 1,
        ], [
            'resume_id' => $this->resumeId,
        ]);

        $this->companyResumeLibraryId = $model->id;

        $this->consumption();
    }

    /**
     *
     * 这部分要总体迁移到服务层
     */
    public function consumption()
    {
        $num = $this->resumeLevel['point'];

        // 简历类型：XXX；人才姓名:XXX；人才ID:XXX
        $remark = '简历类型：' . $this->resumeLevel['name'] . '；人才姓名：' . $this->resumeModel->name . '；人才ID：' . UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON,
                $this->resumeModel->id);

        $companyPackageChangeLogId = (CompanyPackageApplication::getInstance())->resumeDownload($this->companyId, $num,
            $remark);

        $this->companyPackageChangeLogId = $companyPackageChangeLogId;
    }

}
