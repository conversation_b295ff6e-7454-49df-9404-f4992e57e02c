<?php
namespace common\service\match;

use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseResumeLibraryCollect;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeTopConfig;
use common\base\models\BaseShieldCompany;
use common\helpers\DebugHelper;
use common\helpers\MaskHelper;
use common\libs\Cache;
use common\service\companyAuth\ButtonGroupAuthService;
use common\service\resume\ResumeCacheService;
use Yii;
use yii\base\Exception;

/**
 * 处理职位匹配人才
 */
class JobToPersonService extends CommonMatchService
{
    private $job_id;
    private $list_type           = self::LIST_TYPE_RECOMMEND;
    private $distinct_resume_ids = [];
    private $rule_config         = [];
    private $cache_expire        = 0;

    //匹配规则的条件---end
    private $params;
    private $page      = self::PAGE;
    private $page_size = self::PAGE_SIZE;
    private $init_bool = true;

    //列表配置
    const LIST_RESUME_EDUCATION_NUMBER = 3;
    const LIST_RESUME_WORK_NUMBER      = 1;

    //职位对比数据初始化
    private $job_info;
    private $job_category_id;
    private $job_area_id;
    private $job_education_id;
    private $job_major_id;

    /**
     * 初始化
     * @param $params
     * @throws Exception
     */
    public function init($params = [])
    {
        $this->job_id = (isset($params['jobId']) && $params['jobId'] > 0) ? $params['jobId'] : 0;
        if ($this->job_id <= 0) {
            throw new Exception('参数错误');
        }
        //初始化一下职位数据
        $this->job_info = BaseJob::findOne([
            'id'     => $this->job_id,
            'status' => BaseJob::STATUS_ACTIVE,
        ]);
        if (!$this->job_info) {
            throw new Exception('职位不存在或已下线');
        }
        //缓存过期时间
        //        $cache_expire       = strtotime(date('Y-m-d 20:00:00')) - time();
        //        $this->cache_expire = $cache_expire < self::CACHE_COMPANY_EXPIRE_TIME ? self::CACHE_COMPANY_EXPIRE_TIME : $cache_expire;
        $this->cache_expire = self::CACHE_COMPANY_EXPIRE_TIME;

        //列表参数初始化
        $this->params    = $params;
        $this->list_type = ((!isset($params['type']) || empty($params['type'])) || $params['type'] == self::LIST_TYPE_RECOMMEND) ? self::LIST_TYPE_RECOMMEND : self::LIST_TYPE_NEW;
        $this->page      = (!isset($params['page']) || $params['page'] <= 0) ? $this->page : $params['page'];
        $this->page_size = (!isset($params['pageSize']) || $params['pageSize'] <= 0) ? $this->page_size : ($params['pageSize'] > self::PAGE_SIZE_MAX ? $this->page_size : $params['pageSize']);
        //初始化规则数据
        $this->job_category_id  = $this->job_info->job_category_id;
        $this->job_area_id      = $this->job_info->city_id;
        $this->job_education_id = $this->job_info->education_type;
        $this->job_major_id     = $this->job_info->major_id ? explode(',', $this->job_info->major_id) : [];
        //职位数据预处理
        $this->processingArea();
        $this->init_bool = false;

        return $this;
    }

    /**
     * 执行主程序
     * @throws Exception
     */
    public function run()
    {
        if ($this->init_bool) {
            throw new Exception('请先初始化');
        }
        $data = Cache::get($this->getCacheKey());
        if (!$data) {
            //获取数据
            // 暂时换成db2
            //BaseActiveRecord::openDb2();
            $data = $this->getData();
            //BaseActiveRecord::closeDb2();
            //缓存当前页数据
            if (count($data['list']) > 0) {
                Cache::setex($this->getCacheKey(), $this->cache_expire, json_encode($data));
            }
        } else {
            $data             = json_decode($data, true);
            $data['is_cache'] = 1;
        }
        $data['list'] = $this->notCacheListData($data['list']);

        return $data;
    }

    /**
     * 获取数据
     */
    private function getData()
    {
        switch ($this->list_type) {
            case self::LIST_TYPE_RECOMMEND:
                $this->rule_config = array_column(Yii::$app->params[self::JOB_TO_PERSON_RECOMMEND_RULE], null, 'id');
                break;
            case self::LIST_TYPE_NEW:
                $this->rule_config = array_column(Yii::$app->params[self::JOB_TO_PERSON_NEW_RULE], null, 'id');
                break;
        }
        $data = $this->onePageData();
        foreach ($data['list'] as &$item) {
            //简历信息
            $avatar         = BaseMember::findOneVal(['id' => $item['member_id']], 'avatar');
            $item['avatar'] = BaseMember::getAvatarMask($avatar, $item['gender']);
            //教育经历的最后三段
            //            $last_education_name = '';
            //            $last_major_name     = '';
            //            $education_list      = BaseResumeEducation::getLastRecord($item['resume_id'],
            //                self::LIST_RESUME_EDUCATION_NUMBER);
            //            foreach ($education_list as &$education_item) {
            //                if ($education_item['id'] == $item['last_education_id']) {
            //                    $last_education_name = $education_item['education_name'];
            //                    $last_major_name     = $education_item['major'] ?: $education_item['major_custom'];
            //                }
            //                unset($education_item['id'], $education_item['major_id'], $education_item['education_id'], $education_item['is_recruitment']);
            //            }
            //            $item['education_list'] = $education_list;
            $educationList = ResumeCacheService::getEducation($item['resume_id']);
            $educationTop  = ResumeCacheService::getTopEducation($item['resume_id']);
            //教育经历的取最后三段
            $item['education_list'] = array_slice($educationList, 0, self::LIST_RESUME_EDUCATION_NUMBER);
            $last_education_name    = $educationTop['educationName'];
            $last_major_name        = $educationTop['majorId'] ? $educationTop['majorName'] : $educationTop['majorCustom'];
            //工作经验的最后一段
            $item['work_list']      = array_slice(ResumeCacheService::getWork($item['resume_id']), 0,
                self::LIST_RESUME_WORK_NUMBER);
            $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);

            //简历基本信息
            $resume_info_arr = [
                ($item['age']) . '岁',
                $last_education_name,
                $last_major_name,
            ];
            if ($identityExperienceText) {
                $resume_info_arr[] = $identityExperienceText;
            }
            $item['user_info'] = implode('·', $resume_info_arr);
            //求职意向-职位类型
            $item['job_category_name'] = BaseCategoryJob::getName($item['job_category_id']);
            //获取研究方向
            $researchInfo             = ResumeCacheService::getResearchDirection($item['resume_id']);
            $item['research_content'] = empty($researchInfo) ? '' : $researchInfo['content'];
            //简历名称
            $item['name'] = $item['name'] ? MaskHelper::getName($item['name']) : '';
            //简历类型便签
            $item['resume_type_tag']  = BaseResume::getResumeLevel($item['resume_id']);
            $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($item['resume_id']);

            //时间格式化

            // unset($item['age'], $item['work_experience'], $item['job_category_id'], $item['member_id'], $item['last_education_id']);
            unset($item['work_experience'], $item['member_id'], $item['last_education_id']);
        }

        return $data;
    }

    /**
     * 处理不需要缓存的数据列
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function notCacheListData($data)
    {
        //处理不能缓存的数据列
        foreach ($data as &$item) {
            $item['update_time'] = (empty($item['last_update_time']) || $item['last_update_time'] == '0000-00-00 00:00:00') ? '-' : date('Y/m/d',
                strtotime($item['last_update_time']));
            //活跃标签
            $item['active_tag'] = BaseMember::getUserActiveTime($item['member_id']);
            //获取收藏状态
            $item['is_collect'] = BaseResumeLibraryCollect::checkMemberCollectStatus($item['resume_id'],
                $this->params['memberId']);
            //获取邀约状态
            $item['is_invite'] = BaseResumeLibraryInviteLog::checkInviteStatus($item['resume_id'],
                $this->job_info->company_id);
            //获取下载状态
            $info = BaseCompanyResumeLibrary::find()
                ->select(['id'])
                ->where([
                    'resume_id'  => $item['resume_id'],
                    'company_id' => $this->job_info->company_id,
                ])
                ->asArray()
                ->one();

            $item['company_resume_library_id'] = $info['id'] ?? '0';
            $item['is_download']               = !empty($info) ? BaseCompanyResumeLibrary::HAS_RECORD_YES : BaseCompanyResumeLibrary::HAS_RECORD_NO;
            $item['is_company_resume_library'] = !empty($info) ? BaseCompanyResumeLibrary::HAS_RECORD_YES : BaseCompanyResumeLibrary::HAS_RECORD_NO;
            //            $item['is_download']    = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'],
            //                $this->job_info->company_id);
            //            $companyResumeLibraryId = BaseCompanyResumeLibrary::findOneVal([
            //                'resume_id'  => $item['resume_id'],
            //                'company_id' => $this->job_info->company_id,
            //            ], 'id');
            //            if ($companyResumeLibraryId > 0) {
            //                $item['is_company_resume_library'] = BaseCompanyResumeLibrary::HAS_RECORD_YES;
            //                $item['company_resume_library_id'] = $companyResumeLibraryId;
            //            } else {
            //                $item['is_company_resume_library'] = BaseCompanyResumeLibrary::HAS_RECORD_NO;
            //                $item['company_resume_library_id'] = "0";
            //            }
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'],
                $this->job_info->company_id);
            //获取置顶状态
            $item['is_resume_top']      = BaseResumeTopConfig::isResumeTopEffect($item['resume_id']);
            $item['is_resume_top_text'] = $item['is_resume_top'] ? '高匹配' : '';
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
            if (!(isset($this->params['isButton']) && $this->params['isButton'])) {
                $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME)
                    ->run($item['resume_id']);
            }
        }

        return $data;
    }

    /**
     * 获取DB对象
     * @return \yii\db\ActiveQuery
     */
    private function getQuery()
    {
        $query = BaseResume::find()
            ->alias('r')
            //->leftJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'r.id=crl.resume_id')
            //            ->innerJoin(['m' => BaseMember::tableName()], 'r.member_id=m.id')
            ->innerJoin(['re' => BaseResumeEducation::tableName()], 'r.last_education_id=re.id and re.status=1')
            ->innerJoin(['ri' => BaseResumeIntention::tableName()], 'r.id=ri.resume_id and ri.status=1')
            ->innerJoin(['riar' => BaseResumeIntentionAreaRelation::tableName()], 'riar.intention_id=ri.id')
            //            ->innerJoin(['rs' => BaseResumeSetting::tableName()], 'r.id=rs.resume_id')
            ->andWhere([
                'r.status'            => BaseResume::STATUS_ACTIVE,
                'r.is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
            ])
            ->groupBy('r.id,re.resume_id');
        //处理一些where条件
        //1、简历完善度：65%（与人才库入库要求一致）
        //2、近90日活跃
        //        $day_time_90 = date('Y-m-d H:i:s', strtotime('-90 day'));
        //        $query->andWhere([
        //            '>=',
        //            'm.last_active_time',
        //            $day_time_90,
        //        ]);
        $day_time_180 = date('Y-m-d H:i:s', strtotime('-180 day'));
        $query->andWhere([
            '>=',
            'r.last_update_time',
            $day_time_180,
        ]);
        //3、未设置简历隐藏
        //        $query->andWhere(['rs.is_hide_resume' => BaseResumeSetting::IS_HIDE_RESUME_NO]);
        //        $hide_resume_ids = BaseResumeSetting::find()
        //            ->select('resume_id')
        //            ->where(['is_hide_resume' => BaseResumeSetting::IS_HIDE_RESUME_NO])
        //            ->column();
        //4、未屏蔽该单位
        ////反向理解就那些人屏蔽了这个单位
        /////先查出那些屏蔽的简历ID
        $shield_resume_ids = BaseShieldCompany::find()
            ->select('resume_id')
            ->where([
                'company_id' => $this->job_info->company_id,
                'status'     => BaseShieldCompany::STATUS_ACTIVE,
            ])
            ->asArray()
            ->column();
        //$shield_resume_ids = array_column($shield_data, 'resume_id');
        //5、近7日未投递过该职位
        //同理4条件 反向思考
        //获取最近7天投递过该职位的简历ID
        $day_time_7       = date('Y-m-d H:i:s', strtotime('-7 day'));
        $apply_resume_ids = BaseJobApplyRecord::find()
            ->select(['resume_id'])
            ->andWhere([
                '>=',
                'add_time',
                $day_time_7,
            ])
            ->andWhere(['job_id' => $this->job_id])
            ->asArray()
            ->column();
        //$apply_resume_ids = array_column($apply_data, 'resume_id');
        //将剔除的简历ID合并，去重
        $not_resume_ids = array_unique(array_merge($shield_resume_ids, $apply_resume_ids));
        if (count($not_resume_ids) > 0) {
            $query->andWhere([
                'not in',
                'r.id',
                $not_resume_ids,
            ]);
        }
        switch ($this->list_type) {
            case self::LIST_TYPE_RECOMMEND:
                //                $query->orderBy('m.last_active_time desc,r.refresh_time desc,r.id desc');
                $query->orderBy('r.last_update_time desc,r.refresh_time desc,r.id desc');
                break;
            case self::LIST_TYPE_NEW:
                $query->orderBy('r.add_time desc,r.id desc');
                break;
        }
        $this->getQuerySearchWhere($query);

        return $query;
    }

    /**
     * 进行条件筛选条件
     * @param $query
     * @return mixed
     */
    private function getQuerySearchWhere($query)
    {
        //'checkedInSevenDays' => Yii::$app->request->get('checkedInSevenDays'),
        //'invitedInSevenDays' => Yii::$app->request->get('invitedInSevenDays'),
        //'accepted' => Yii::$app->request->get('accepted'),
        $resume_ids = [];
        //60天内已查看 0没有勾选 1已勾选
        if (isset($this->params['checkedInSevenDays']) && $this->params['checkedInSevenDays'] == 1) {
            $check_in_service_time = date('Y-m-d H:i:s', strtotime('-60 day'));
            //连接查看表
            //获取查看的简历
            $view_resume_ids = BaseCompanyViewResume::find()
                ->select('resume_id')
                ->andWhere([
                    'company_id' => $this->job_info->company_id,
                ])
                ->andWhere([
                    '>',
                    'last_time',
                    $check_in_service_time,
                ])
                ->column();
            //注意这个位置是第一个所以没有过多判断
            $resume_ids = array_merge($resume_ids, $view_resume_ids);
        }
        //30天内已邀请 0没有勾选 1已勾选
        if (!empty($this->params['invitedInSevenDays']) && $this->params['invitedInSevenDays'] == 1) {
            $invite_in_service_time = date('Y-m-d H:i:s', strtotime('-30 day'));
            //连接查看表
            $a = BaseResumeLibraryInviteLog::find();
            $invite_resume_ids = $a
                ->select('resume_id')
                ->andWhere([
                    'company_id' => $this->job_info->company_id,
                    'job_id'     => $this->job_info->id,
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $invite_in_service_time,
                ])
                ->column();

            $resume_ids        = array_merge($resume_ids, $invite_resume_ids);
        }
        //已应聘 0没有勾选 1已勾选
        if (!empty($this->params['accepted']) && $this->params['accepted'] == 1) {
            //获取以应聘的简历ID
            $apply_resume_ids = BaseJobApplyRecord::find()
                ->select('resume_id')
                ->andWhere([
                    'company_id' => $this->job_info->company_id,
                    'job_id'     => $this->job_info->id,
                ])
                ->column();
            $resume_ids       = array_merge($resume_ids, $apply_resume_ids);
        }
        $resume_ids = array_unique($resume_ids);
        if (count($resume_ids) > 0) {
            $query->andWhere([
                'not in',
                'r.id',
                $resume_ids,
            ]);
        }

        // 隐藏完善度＜65%博士  前端虽然加上了博士的这个文案，但是实际上只是要过滤掉完善度小于65%的简历
        if (isset($this->params['hideViewLess65Percentage']) && $this->params['hideViewLess65Percentage'] == 1) {
            $query->andWhere([
                '>=',
                'r.complete',
                65,
            ]);
        }

        return $query;
    }

    /**
     * 设置获取的字段
     * @param $query
     * @return mixed
     */
    private function getSelect($query)
    {
        return $query->select([
            'r.id as resume_id',
            'r.member_id',
            'r.name',
            'r.age',
            'r.gender',
            'r.complete',
            'r.last_update_time',
            'r.work_experience',
            'r.last_education_id',
            //'crl.id as library_id',
            'ri.job_category_id',
            //'m.avatar',
        ]);
    }

    /**
     * 去重
     * @param $query
     * @return mixed
     */
    private function getDistinctWhere($query)
    {
        return $query->andWhere([
            'not in',
            'r.id',
            $this->distinct_resume_ids,
        ]);
    }

    /***
     * 组装规则条件
     * @param $rule_id
     * @return \yii\db\ActiveQuery
     */
    private function getQueryRuleWhere($query, $rule_id)
    {
        $rule_info = $this->rule_config[$rule_id];

        if ($rule_id === 0) {
            $where_job_category_arr = $this->queryJobCategoryRecommend($rule_info['match']['job_category_id']);
            //$where_area_arr         = $this->queryAreaRecommend($rule_info['match']['area_id']);
            $where_education_arr = $this->queryEducationRecommend($rule_info['match']['education_id']);
            $where_major_arr     = $this->queryMajorRecommend($rule_info['match']['major_id']);
            $query->andWhere($where_job_category_arr);
            //$query->andWhere($where_area_arr);
            $query->andWhere($where_education_arr);
            $query->andWhere($where_major_arr);
            //特殊处理增加一个条件
            $top_resume_ids = BaseResumeTopConfig::getEffectResumeIds();
            //如果是空，就让它查不到一条数据
            $query->andWhere(['r.id' => $top_resume_ids]);
        } else {
            $where_job_category_arr = $this->queryJobCategoryRecommend($rule_info['match']['job_category_id']);
            $where_area_arr         = $this->queryAreaRecommend($rule_info['match']['area_id']);
            $where_education_arr    = $this->queryEducationRecommend($rule_info['match']['education_id']);
            $where_major_arr        = $this->queryMajorRecommend($rule_info['match']['major_id']);
            $query->andWhere($where_job_category_arr);
            $query->andWhere($where_area_arr);
            $query->andWhere($where_education_arr);
            $query->andWhere($where_major_arr);
        }

        return $query;
    }

    /**
     * 获取一页数据
     * @return array
     */
    private function onePageData()
    {
        $total                    = 0;
        $list                     = [];
        $page                     = $this->page;
        $page_size                = $this->page_size;
        $list_distinct_resume_ids = [];
        //分页数据处理
        $page_count           = intval(ceil(($page + 1) / self::PAGE_STEP) * self::PAGE_STEP);
        $total_step           = $page_count * $page_size;
        $cur_page_data_offset = ($page - 1) * $page_size;
        $cur_page_data_start  = $cur_page_data_offset;
        $cur_page_data_end    = $cur_page_data_offset + $page_size;
        $one_page_number      = $page_size;//这一页需要数量
        $query                = $this->getQuery();
        for ($rule_id = 0; $rule_id < count($this->rule_config); $rule_id++) {
            $query_item  = $this->getQueryRuleWhere(clone $query, $rule_id);
            $query_count = clone $query_item;
            if (count($this->distinct_resume_ids) > 0) {
                $this->getDistinctWhere($query_count);
            }
            $item_ids   = $query_count->select('r.id')
                ->column();
            $rule_total = count($item_ids);
            $total_new  = $total + $rule_total;
            //注意这里加1
            if ($cur_page_data_start + 1 <= $total_new && $one_page_number > 0 && $rule_total > 0) {
                $query_dis_list = clone $query_item;
                $query_list     = clone $query_item;
                //这个大于这个说明可以取数据了
                //这里分两种情况
                /// 1、第一次进入区间不在最后端点上 存在前置 所以要注意计算offset
                /// 2、第二次或者第一次刚好在前置端点上时候 我们不需要计算offset直接去拿  只需要判断当前页数量是否足够就OK了
                $offset = ($cur_page_data_start - $total > 0) ? ($cur_page_data_start - $total) : 0;
                $limit  = ($rule_total - $offset) > $one_page_number ? $one_page_number : ($rule_total - $offset);
                //拿数据 放入返回的列表中
                $this->getSelect($query_list);
                if (count($this->distinct_resume_ids) > 0) {
                    $this->getDistinctWhere($query_list);
                }
                $list_item       = $query_list->offset($offset)
                    ->limit($limit)
                    ->asArray()
                    ->all();
                $list            = array_merge($list, $list_item);
                $one_page_number -= $limit;
                unset($query_item, $query_list, $query_dis_list);
            }
            $total = $total_new;
            //这里处理分页总数
            if ($total_step <= $total) {
                $total = $total_step;
                break;
            }
            if ($rule_total > 0) {
                $this->distinct_resume_ids = array_merge($this->distinct_resume_ids, $item_ids);
            }

            unset($query_count);
        }
        //不正常情形
        if (count($list) <= 0) {
            $total = 0;
        }
        //返回
        $result = [
            'list'  => $list,
            'total' => $total,
        ];

        return $result;
    }

    /**
     * 对职位类型进行条件组装
     * @param $type 0不用匹配 1匹配
     * @return mixed
     */
    private function queryJobCategoryRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::JOB_CATEGORY_RULE_TYPE_NO:
                $arr = [
                    '<>',
                    'ri.job_category_id',
                    $this->job_category_id,
                ];
                break;
            case self::JOB_CATEGORY_RULE_TYPE_YES:
                $arr = ['ri.job_category_id' => $this->job_category_id];
                break;
        }

        return $arr;
    }

    /**
     * 对学历进行条件组装
     * @param $type 0不用匹配 1匹配 2职位学历要求低于人才最高学历
     * @return mixed
     */
    private function queryEducationRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::EDUCATION_RULE_TYPE_NO:
                $arr = [
                    '<>',
                    're.education_id',
                    $this->job_education_id,
                ];
                break;
            case self::EDUCATION_RULE_TYPE_YES:
                $arr = ['re.education_id' => $this->job_education_id];
                break;
            case self::EDUCATION_RULE_TYPE_GT:
                $education_type = $this->getEducationType();
                $arr            = ['re.education_id' => $education_type];
                break;
        }

        return $arr;
    }

    /**
     * 对专业进行条件组装
     * @param $type 0不用匹配 1匹配专业 2专业不限 3专业未分类 4空 5专业未分类或空值
     * @return mixed
     */
    private function queryMajorRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::MAJOR_RULE_TYPE_NO:
                if ($this->job_major_id) {
                    $arr = [
                        'not in',
                        're.major_id_level_2',
                        $this->job_major_id,
                    ];
                }
                break;
            case self::MAJOR_RULE_TYPE_YES:
                if ($this->job_major_id) {
                    $arr = ['re.major_id_level_2' => $this->job_major_id];
                }
                break;
        }

        return $arr;
    }

    /**
     * 对地区进行条件组装
     * @param $type 0不用匹配 1匹配 2匹配城市 3匹配省份 4匹配重点城市 5匹配非重点城市 6意向城市所属省份匹配 7意向地区或所属省份匹配  8意向地区所属省份不匹配
     * @return mixed
     */
    private function queryAreaRecommend($type)
    {
        $city               = $this->job_area_id['city'];//非数组
        $province_city      = $this->job_area_id['city_province'];//非数组
        $city_province_city = $this->job_area_id['city_province_city'];//数组
        $area_ids           = [];
        $arr                = [];
        if (!empty($city)) {
            array_push($area_ids, $city);
        }
        if (!empty($province_city)) {
            array_push($area_ids, $province_city);
        }
        switch ($type) {
            case self::AREA_RULE_TYPE_NO:
                $arr = [
                    'not in',
                    'riar.area_id',
                    array_unique(array_merge($city_province_city, $area_ids)),
                ];
                break;
            case self::AREA_RULE_TYPE_YES:
                $arr = [
                    'in',
                    'riar.area_id',
                    $area_ids,
                ];
                break;
            case self::AREA_RULE_TYPE_CITY:
                if ($city > 0) {
                    $arr = ['riar.area_id' => $city];
                }
                break;
            case self::AREA_RULE_TYPE_PROVINCE:
                if ($province_city > 0) {
                    $arr = ['riar.area_id' => $province_city];
                }
                break;
            case self::AREA_RULE_TYPE_PROVINCE_CITY:
                if (count($city_province_city) > 0) {
                    $arr = ['riar.area_id' => $city_province_city];
                }
                break;
            case self::AREA_RULE_TYPE_NO_KEY:
                $arr = [
                    'not in',
                    'riar.area_id',
                    array_unique(array_merge($city_province_city, [$city])),
                ];
                break;
        }

        return $arr;
    }

    /**
     * 处理学历类型
     * @return array|int[]
     */
    private function getEducationType()
    {
        $education_type = [];
        switch ($this->job_education_id) {
            case 1:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ];
                break;
            case 2:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ];
                break;
            case 3:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ];
                break;
            case 4:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ];
                break;
            case 5:

                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                ];
                break;
        }

        return $education_type;
    }

    /**
     * 地区ID处理
     ** 原因：人才地区选择了多个有城市、省份
     ** 处理结果：将职位地区id处理成集合城市(key:city)与省份(key:province)
     */
    private function processingArea()
    {
        //直接地区ID
        $area_id = $this->job_area_id;
        //获取所有地区信息
        $area_data = BaseArea::findOne($area_id);
        //区分
        $city               = $area_id;//城市
        $city_province      = $area_data->parent_id;//城市所属省份
        $city_province_city = [];//城市所属省份下的所有城市
        //对意向城市处理
        if (count($city_province) > 0) {
            $city_province_city_data = BaseArea::find()
                ->andWhere([
                    'parent_id' => $city_province,
                    'level'     => 2,
                ])
                ->asArray()
                ->all();
            foreach ($city_province_city_data as $city_province_city_item) {
                array_push($city_province_city, $city_province_city_item['id']);
            }
        }

        $this->job_area_id = [
            'city'               => $city,
            'city_province'      => $city_province,
            'city_province_city' => $city_province_city,
        ];

        return true;
    }

    /**
     * 获取当前列表缓存key
     */
    private function getCacheKey()
    {
        //意向职位_意向地区_学历水平_学科专业_类表类型_分页_每页数量
        $major_ids = explode(',', $this->job_info->major_id);
        sort($major_ids);
        $major_id_str = implode('/', $major_ids);
        $key_arr      = [
            $this->job_info->job_category_id,
            $this->job_info->city_id,
            $this->job_info->education_type,
            $major_id_str,
            $this->list_type,
            $this->page,
            $this->page_size,
        ];
        //过滤箱不为空也让它缓存
        if ($this->params['checkedInSevenDays'] == 1) {
            array_push($key_arr, 'checkedInSevenDays');
        }
        if ($this->params['invitedInSevenDays'] == 1) {
            array_push($key_arr, 'invitedInSevenDays');
        }
        if ($this->params['accepted'] == 1) {
            array_push($key_arr, 'accepted');
        }
        if ($this->params['hideViewLess65Percentage'] == 1) {
            array_push($key_arr, 'hideViewLess65Percentage');
        }
        $key        = md5(implode('_', $key_arr));
        $key_prefix = $this->list_type == self::LIST_TYPE_RECOMMEND ? Cache::MATCH_JOB_PERSONAL_RECOMMEND_LIST : Cache::MATCH_JOB_PERSONAL_NEW_LIST;

        return $key_prefix . ':' . $key;
    }

    /**
     * 给队列只做一个入口
     */
    public function runQueue()
    {
        if ($this->init_bool) {
            throw new Exception('请先初始化');
        }
        //组装缓存Key
        $cache_key = $this->getCacheKey();
        //查看缓存是否存在
        $cache_data = Cache::get($cache_key);
        if (!$cache_data) {
            //获取数据
            $data = $this->getData();
            //缓存当前页数据
            if (count($data['list']) > 0) {
                Cache::setex($this->getCacheKey(), $this->cache_expire, json_encode($data));
            }
        }

        return true;
    }
}