<?php

namespace common\service\downloadTask;

use admin\models\MajorAi;
use admin\models\NginxLog;
use app\controller\Index;
use admin\models\TalentExport;
use common\base\models\BaseAdminDownloadTask;
use common\helpers\DebugHelper;
use common\service\CommonService;
use common\service\newResumeActivity\AdminListService;
use common\service\v2\job\IndexService;

/**
 * 这个只是单纯的消费应该处理的任务,不做实际的下载任务,具体的任务在对应的服务里面处理
 */
class ExecuteAdminService extends BaseService
{

    public function run($id)
    {
        $this->getTask($id);
        $this->executeTask();
        $this->sendToWxOa();
    }

    public function getTask($id)
    {
        $this->taskModel = BaseAdminDownloadTask::find()
            ->where([
                'status' => BaseAdminDownloadTask::STATUS_WAIT,
                'id'     => $id,
            ])
            ->one();
        $this->adminId   = $this->taskModel->admin_id;
    }

    public function executeTask()
    {
        if (!$this->taskModel) {
            return;
        }

        // 先锁定任务
        $this->taskModel->status     = BaseAdminDownloadTask::STATUS_IN_EXECUTE;
        $this->taskModel->begin_time = CUR_DATETIME;
        $this->taskModel->save();

        DebugHelper::log($this->taskModel->attributes);

        try {
            switch ($this->taskModel->type) {
                case BaseAdminDownloadTask::TYPE_COMPANY_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new AllCompanyListService())->run($params);
                    $this->createExcel($data['data'], $data['headers']);
                    break;
                case BaseAdminDownloadTask::TYPE_FREE_COMPANY_LIST:
                    $data = (new AllFreeCompanyListService())->run();
                    $this->createExcel($data['data'], $data['headers']);
                    break;
                case BaseAdminDownloadTask::TYPE_REGISTRATION_FORM_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new AllRegistrationFormListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_REGISTRATION_FORM_FILE_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new AllRegistrationFormFileListService())->run($params);
                    $this->createFile($data['zipPath']);
                    break;
                case BaseAdminDownloadTask::TYPE_MY_COMPANY_LIST:
                    $data = (new MyCompanyListService())->setParams(['adminId' => $this->adminId])
                        ->run();
                    $this->createFile($data);
                    break;
                case BaseAdminDownloadTask::TYPE_COOPERATION_JOB_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new IndexService())->setPlatform(CommonService::PLATFORM_TIMER)
                        ->exportCooperation($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_DELIVERY_INVITE_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new DeliveryInviteListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_ORDER_PERSON_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new OrderPersonListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_PERSON_EQUITY_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new EquityPersonListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_SLICE_JOB_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new JobListService())->runSlice($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_STATEMENT_REPORT_BUILDER:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new StatementReportBuilderService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_PAY_TRANSFORM_LOG:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new PayTransformLogService())->setParams($params)
                        ->run();
                    $this->createFile($data);
                    break;
                case BaseAdminDownloadTask::TYPE_ACTIVITY_FORM_OPTION_SIGN:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new ActivityFormOptionSignService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_RESUME_EQUITY_ADMIN_SETTING:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new ResumeEquityAdminSettingService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_SEO_HOT_WORD:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new SeoHotWordService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_NEW_RESUME_SHARE:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new AdminListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_COMPANY_GROUP_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new CompanyGroupListService())->run($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_NGINX_LOG:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new NginxLog())->create($params);
                    $this->createFile($data);
                    break;
                case BaseAdminDownloadTask::TYPE_MAJOR_AI_LOG:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = MajorAi::getAiTextList($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_ANNOUNCEMENT_LIST:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = (new \common\service\v2\announcement\IndexService())->setPlatform(CommonService::PLATFORM_TIMER)
                        ->exportCooperation($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
                case BaseAdminDownloadTask::TYPE_TALENT_EXPORT:
                    $params = json_decode($this->taskModel->params, 'true');
                    $data   = TalentExport::getExportData($params);
                    $this->createExcel($data['data'], $data['headers'], $data['fileName']);
                    break;
            }

            $this->taskModel->status      = BaseAdminDownloadTask::STATUS_FINISH;
            $this->taskModel->path        = $this->aliyunPath;
            $this->taskModel->finish_time = date('Y-m-d H:i:s');
            $this->taskModel->save();
        } catch (\Exception $e) {
            $this->taskModel->status = BaseAdminDownloadTask::STATUS_FAIL;
            $this->taskModel->reason = $e->getMessage();
            $this->taskModel->save();
            // 通知下载人员
            $this->noticeToWxOa();
        }
    }
}
