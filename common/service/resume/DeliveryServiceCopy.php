<?php

namespace common\service\resume;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobApplyTopEquityRecord;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageDetail;
use common\base\models\BaseResumeEquitySetting;
use common\helpers\DebugHelper;
use common\helpers\MaskHelper;
use common\libs\EmailQueue;
use common\libs\WxPublic;
use common\service\CommonService;
use frontendPc\models\ResumeEducation;
use h5\models\ResumeAttachment;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 处理职位投递流程
 * 1、使用当前服务类应当阅读类前面定义的变量常量
 * 2、其次阅读调用的操作方法头部介绍
 */
class DeliveryServiceCopy extends CommonService
{
    //简历模块大模块数量
    const  RESUME_MODULE_AMOUNT = 7;
    /** start 操作类型 */
    /** 检测公告下的职位列表 */
    const OPERATION_TYPE_JOB_LIST = 'job_list';
    /** 检测申请 申请弹窗 */
    const OPERATION_TYPE_CHECK_APPLY = 'check_apply';
    /** 申请 */
    const OPERATION_TYPE_APPLY = 'apply';
    /** 系统投递 赋值时默认 */
    const TYPE_APPLY_SYSTEM = 1;
    /** 线下招聘会投递*/
    const TYPE_APPLY_FAIR = 2;
    /** RPO招聘会投递 */
    const TYPE_APPLY_RPO = 3;
    /** end 操作类型 */

    /** 传递参数 */
    private $requstData = [];
    /** 求职者的基本信息与一些相关信息 */
    private $memberResumeInfo;
    /** 职位与单位信息 */
    private $jobCompanyInfo;
    /** 公告信息 */
    private $announcementInfo;
    /** 职位者最高学历 */
    private $memberMaxEducationId;
    /** 满多少完整度才能投递的配置 */
    private $configComplete;
    /** 求职者简历完整度 */
    private $complete;
    /** 用户member_id */
    private $memberId;

    /** start 文本检测与申请时候的文本身 */
    /**
     * 文本定义介绍：
     *    1、所有文本变量定义都是按照界面从上到下、从左到右依次向下数
     *    2、为了方便文本修改定义文本配置也将遵循从上到下、从左到右依次向下数，同一位置多个文本时将增加后缀顺序
     *    3、由于各端文本有差异各端对弈配置相互不受影响,各端文本就算一样也将在各端独立配置防止文本文本变化
     *    4、配置文本重的特殊替换文本表示,替换示例：{p1} {p2} {p3} ......
     *  解释变量名：
     *  端简称+功能(ckeck检测)+弹窗类型+界面位置顺序+当前位置的文本多个文本序号
     *  功能:check(检测界面)
     *  弹窗类型：1站内 2站外
     */
    /** web/pc文本定义 */
    private $web_check_1_1_1     = '系统校验到该职位最低学历要求：{p1}，建议您确认学历要求后再投递';
    private $web_check_1_2_1     = '简历完善度须达到{p1}%方可投递';
    private $web_check_1_2_2     = '您的【{p1}】内容未完善，完善后可提高您的竞争力哦～';
    private $web_check_replace_1 = '项目经历';
    private $web_check_replace_2 = '学术成果';
    private $web_check_replace_3 = '项目经历&学术成果';
    private $web_check_1_2_3     = '简历完成度{p1}%';
    private $web_check_1_3_1     = '完善';
    private $web_check_1_3_2     = '预览';
    private $web_check_1_4_1     = '<span style="color: #FA635C;">*</span>公告正文提示报名需提交附件材料，请确认是否已上传。';
    private $web_check_1_4_2     = '<span style="color: #FA635C;">*</span>需要上传职位所要求的应聘材料';
    private $web_check_1_4_3     = '请按招聘要求上传材料';
    private $web_check_1_5_1     = '该职位为我平台整理后发布，<span class="color-primary">我平台将帮您代投简历至用人部门邮箱，同时抄送至您 {p1} 的邮箱，</span>请注意查收邮件以获得后续反馈进度。投递成功后，可至“投递”-“站外投递”查看您的投递记录。';
    private $web_check_1_5_2     = '';
    private $web_check_1_5_3     = '';
    private $web_check_2_1_1     = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $web_check_2_1_2     = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $web_apply_1_1_1     = '对不起，您的学历不符合要求。该职位最低学历要求：{p1}';
    private $web_apply_1_1_2     = '投递须上传职位所要求的应聘材料，请先上传文件';
    //线下招聘会
    private $web_check_fair_1_1_1   = '对不起，您的学历不符合要求。该场次最低学历要求：{p1}';
    private $web_success_fair_1_1_1 = '您已成功报名{p1}！会前将有工作人员联系您，请保持电话畅通并按时参会。';
    private $web_success_fair_1_2_1 = '扫下方二维码添加高才助手，加入本场微信群了解招聘会最新动态。';

    /** h5文本定义 */
    private $h5_check_1_1_1        = '注意：应要求，本职位须通过单位招聘系统进行简历投递。当前页面投递，单位可能不做处理，请您知悉。<a href="{p1}" target="_blank">去投递</a>';
    private $h5_check_1_1_2        = '应要求，本职位须以{p1}形式进行简历投递，请仔细查看公告&职位详情。当前页面投递，单位可能不做处理，请您知悉。';
    private $h5_check_1_2_1        = '您的【{p1}】内容未完善，完善后可提高您的竞争力哦～';
    private $h5_check_replace_1    = '项目经历';
    private $h5_check_replace_2    = '学术成果';
    private $h5_check_replace_3    = '项目经历&学术成果';
    private $h5_check_1_2_2        = '您的简历有{p1}个待完善项，简历越完整，被查看的几率越高哦～';
    private $h5_check_1_2_3        = '当前简历完善度<span>{p1}%</span>';
    private $h5_check_1_2_4        = '您的在线简历完善度<span>{p1}%</span>，简历完善度达<span class="full">{p2}%</span>方可投递。请先完善简历。';
    private $h5_check_1_3_1        = '该职位为我平台整理后发布，我平台将帮您代投简历至用人部门邮箱，同时抄送至您{p1}的邮箱，请注意查收邮件以获得后续反馈进度。投递成功后，可至“投递”-“站外投递：查看您的投递记录。';
    private $h5_check_1_4_1        = '系统校验到该职位最低学历要求：{p1}，建议您确认学历要求后再投递';
    private $h5_check_1_4_2        = '投递须上传职位所要求的应聘材料，请先上传文件';
    private $h5_check_notice_1_4_1 = '<span style="color: #FA635C;">*</span>公告正文提示报名需提交附件材料，请确认是否已上传。';
    private $h5_check_notice_1_4_2 = '<span style="color: #FA635C;">*</span>需要上传职位所要求的应聘材料';
    private $h5_check_notice_1_4_3 = '请按招聘要求上传材料';
    private $h5_check_2_1_1        = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $h5_check_2_1_2        = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $h5_apply_1_1_1        = '该职位最低学历要求：{p1}';
    private $h5_apply_1_1_2        = '投递须上传职位所要求的应聘材料，请先上传文件';
    //线下招聘会
    private $h5_check_fair_1_1_1   = '对不起，您的学历不符合要求。该场次最低学历要求：{p1}';
    private $h5_success_fair_1_1_1 = '您已成功报名{p1}！会前将有工作人员联系您，请保持电话畅通并按时参会。';
    private $h5_success_fair_1_2_1 = '扫码添加高才助手，进群关注招聘会最新动态';

    /** Mini文本定义 */
    private $mini_check_1_1_1        = '注意：应要求，本职位须通过单位招聘系统进行简历投递。当前页面投递，单位可能不做处理，请您知悉。<a href="{p1}" target="_blank">去投递</a>';
    private $mini_check_1_1_2        = '应要求，本职位须以{p1}形式进行简历投递，请仔细查看公告&职位详情。当前页面投递，单位可能不做处理，请您知悉。';
    private $mini_check_1_2_1        = '您的【{p1}】内容未完善，完善后可提高您的竞争力哦～';
    private $mini_check_replace_1    = '项目经历';
    private $mini_check_replace_2    = '学术成果';
    private $mini_check_replace_3    = '项目经历&学术成果';
    private $mini_check_1_2_2        = '您的简历有{p1}个待完善项，简历越完整，被查看的几率越高哦～';
    private $mini_check_1_2_3        = '当前简历完善度<span>{p1}%</span>';
    private $mini_check_1_2_4        = '您的在线简历完善度<span>{p1}%</span>，简历完善度达<span class="full">{p2}%</span>方可投递。请先完善简历。';
    private $mini_check_1_3_1        = '该职位为我平台整理后发布，我平台将帮您代投简历至用人部门邮箱，同时抄送至您{p1}的邮箱，请注意查收邮件以获得后续反馈进度。投递成功后，可至“投递”-“站外投递：查看您的投递记录。';
    private $mini_check_1_4_1        = '系统校验到该职位最低学历要求：{p1}，建议您确认学历要求后再投递';
    private $mini_check_1_4_2        = '投递须上传职位所要求的应聘材料，请先上传文件';
    private $mini_check_notice_1_4_1 = '<span style="color: #FA635C;">*</span>公告正文提示报名需提交附件材料，请确认是否已上传。';
    private $mini_check_notice_1_4_2 = '<span style="color: #FA635C;">*</span>需要上传职位所要求的应聘材料';
    private $mini_check_notice_1_4_3 = '请按招聘要求上传材料';
    private $mini_check_2_1_1        = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $mini_check_2_1_2        = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private $mini_apply_1_1_1        = '该职位最低学历要求：{p1}';
    private $mini_apply_1_1_2        = '投递须上传职位所要求的应聘材料，请先上传文件';
    //线下招聘会
    private $mini_check_fair_1_1_1   = '对不起，您的学历不符合要求。该场次最低学历要求：{p1}';
    private $mini_success_fair_1_1_1 = '您已成功报名{p1}！会前将有工作人员联系您，请保持电话畅通并按时参会。';
    private $mini_success_fair_1_2_1 = '扫码添加高才助手，进群关注招聘会最新动态';

    /** end 文本检测与申请时候的文本身 */

    private $equity_tips_1 = '使用后您的简历在单位端将置顶展示（剩余<span class="color-primary">{p1}</span>次）';
    private $equity_tips_2 = '使用后您的简历在单位端将置顶展示（剩余<span class="color-primary">0</span>次）';
    private $equity_tips_3 = '该职位30天内已使用过投递置顶权益（剩余<span class="color-primary">{p1}</span>次）';

    /**
     * 设置用户
     */
    public function setUser()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
            case self::PLATFORM_WEB_PERSON:
            case self::PLATFORM_H5:
                //检测是否有用户登录
                if (!Yii::$app->user->isGuest) {
                    //获取登录用户ID
                    $this->memberId = Yii::$app->user->id;
                    if ($this->memberId <= 0) {
                        $this->redirectLogin();
                    }
                } else {
                    $this->redirectLogin();
                }
                break;
            case self::PLATFORM_MINI:
                //小程序获取member_id
                $this->memberId = BaseMember::getMiniMemberId();
                if ($this->memberId <= 0) {
                    $this->redirectLogin();
                }
                break;
            default:
                throw new Exception('您没有权限执行此操作！');
        }
    }

    /**
     * 初始化数据
     * @param array $init
     * @return $this
     * @throws Exception
     */
    public function init($init = [])
    {
        $this->line('================Start Init====================');
        if (empty($this->operationPlatform)) {
            throw new Exception('请设置操作平台');
        }
        if (empty($this->operationType)) {
            throw new Exception('请设置操作类型');
        }
        //获取完整度配置
        $this->configComplete = Yii::$app->params['completeResumePercent'];
        $this->requstData     = array_merge(Yii::$app->request->post(), Yii::$app->request->get());
        //兼容处理
        $this->requstData = array_merge($this->requstData, $init);
        //设置用户member_id
        $this->setUser();
        //初始化设置求职者用户信息与简历信息
        $this->memberResumeInfo = BaseResume::find()
            ->where([
                'member_id' => $this->memberId,
                //'status'    => BaseResume::STATUS_ACTIVE,
            ])
            ->with([
                'member',
            ])
            ->asArray()
            ->one();
        if (empty($this->memberResumeInfo) || (!isset($this->memberResumeInfo['id']) && $this->memberResumeInfo['id'] <= 0) || (!isset($this->memberResumeInfo['member']['id']) && $this->memberResumeInfo['member']['id'] <= 0)) {
            throw new Exception('用户信息或者简历信息不存在');
        }
        //获取当前求职者的最高学历
        $this->memberMaxEducationId = ResumeEducation::findOneVal(['id' => $this->memberResumeInfo['last_education_id']],
            'education_id');
        //特殊处理一下求职者最高学历为5的将它值修改一下为0
        if ($this->memberMaxEducationId == 5) {
            $this->memberMaxEducationId = 0;
        }
        //求职者简历完整度
        $this->complete = $this->memberResumeInfo['complete'];
        //参数含有职位ID则初始化职位信息与单位信息
        if (isset($this->requstData['jobId']) && $this->requstData['jobId'] > 0) {
            //初始化申请职位信息与单位信息
            $this->jobCompanyInfo = BaseJob::getJobCompanyInfo($this->requstData['jobId']);

            if (empty($this->jobCompanyInfo)) {
                throw new Exception('职位信息已下线或不存在');
            }
            //特殊处理一下职位学历要求为5的将它值修改一下为0
            if ($this->jobCompanyInfo['education_type'] == 5) {
                $this->jobCompanyInfo['education_type'] = 0;
            }
        }
        //根据公告ID获取公告信息
        $announcement_id = 0;
        if ($this->jobCompanyInfo['announcement_id'] > 0) {
            $announcement_id = $this->jobCompanyInfo['announcement_id'];
        } elseif (isset($this->requstData['announcementId']) && $this->requstData['announcementId'] > 0) {
            $announcement_id = $this->requstData['announcementId'];
        }
        if ($announcement_id > 0) {
            $this->announcementInfo = BaseAnnouncement::findOne($announcement_id);
        }
        $this->line('================End Init====================');

        return $this;
    }

    /**
     * 执行操作
     * @return false|mixed|string
     * @throws Exception
     */
    public function run()
    {
        $this->line('================Start Run================');
        //新增双选会逻辑判断 type 1系统投递 2线下招聘会投递流程 3rpo招聘会投递流程
        $type = self::TYPE_APPLY_SYSTEM;
        if (!empty($this->announcementInfo)) {
            if ($this->announcementInfo['template_id'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                $type = self::TYPE_APPLY_FAIR;
            }
        }
        switch ($this->operationType) {
            //检测公告职位列表
            case self::OPERATION_TYPE_JOB_LIST:
                $this->line('++++++++++++++Execute jobList++++++++++++++');
                $result = $this->jobList();
                break;
            //检测申请操作
            case self::OPERATION_TYPE_CHECK_APPLY:
                $this->line('++++++++++++++Execute checkApply++++++++++++++');
                if ($type == self::TYPE_APPLY_SYSTEM) {
                    $result = $this->checkApply();
                } elseif ($type == self::TYPE_APPLY_FAIR) {
                    $result = $this->checkApplyFair();
                }
                break;
            //申请职位操作
            case self::OPERATION_TYPE_APPLY:
                $this->line('++++++++++++++Execute Apply++++++++++++++');
                if ($type == self::TYPE_APPLY_SYSTEM) {
                    $result = $this->apply();
                } elseif ($type == self::TYPE_APPLY_FAIR) {
                    $result = $this->applyFair();
                }
                break;
            default:
                throw new Exception('当前操作目前不支持');
        }
        $this->line('================End Run==================');
        $this->afterExec();

        return $result;
    }

    /**
     * 获取公告下的在线职位列表
     * ```EOT
     * 参数说明
     *    announcementId int 公告ID
     * ```EOT
     * @return array
     * @throws Exception
     */
    private function jobList()
    {
        //获取传递的参数 --公告ID
        $id = $this->requstData['announcementId'];
        if ($id <= 0) {
            throw new Exception('参数错误');
        }
        //检测当前公告是否在线
        $info = BaseAnnouncement::find()
            ->with([
                'article' => function ($query) {
                    $query->where([
                        'status'    => BaseArticle::STATUS_ONLINE,
                        'is_show'   => BaseArticle::IS_SHOW_YES,
                        'is_delete' => BaseArticle::IS_DELETE_NO,
                    ]);
                },
            ])
            ->where(['id' => $id])
            ->asArray()
            ->one();
        if (empty($info['article'])) {
            throw new Exception('公告已下线');
        }
        //获取单位信息
        $companyInfo = BaseCompany::findOne($info['company_id']);
        //获取有效职位列表
        $list        = BaseJob::find()
            ->select([
                'id',
                'name',
                'delivery_way',
            ])
            ->andWhere([
                'announcement_id' => $id,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();
        $apply_count = 0;
        foreach ($list as &$item) {
            $jobStatus = BaseJobApplyRecord::checkJobApplyStatus($this->memberResumeInfo['id'], $item['id']);
            if ($jobStatus == BaseJob::JOB_APPLY_STATUS_YES) {
                $item['type'] = true;
                $apply_count++;
            } else {
                $item['type'] = false;
            }
            unset($item['delivery_way']);
        }

        return [
            //是否列表全部投递 1是 2否
            'isApplyStatus' => $apply_count == count($list) ? 1 : 2,
            'list'          => $list,
        ];
    }

    /**
     * 检测职位是否可以投递
     * ```EOT
     * 参数说明
     *    jobId int 职位ID
     * 返回参数说明
     * [
     *     complete                 => 简历完整度
     *     toastType                => 弹窗类型1站内2站外
     *     applyButtonStatus        => 申请按钮是否需要置灰 0不置灰 1置灰
     *     resumeAttachmentList     => 附件简历列表
     *     defaultResumeAttachment  => 默认附件简历token
     *     onLink                   => 跳转链接
     *     checkTextOne             => 文本1
     *     checkTextTwo             => 文本2
     *     checkTextThree           => 文本3
     *     checkTextFour            => 文本4
     *     checkTextFive            => 文本5
     * ]
     * ```EOT
     * @return array
     * @throws Exception
     */
    private function checkApply()
    {
        //验证一下必选参数
        if ($this->requstData['jobId'] <= 0) {
            throw new Exception('参数错误');
        }

        // 从这里开始是直聊加入的判断,暂时多加一层,看看是否有影响
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['delivery_way'];
        } elseif ($this->jobCompanyInfo['announcement']['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['announcement']['delivery_way'];
        } else {
            throw new Exception('职位申请失败');
        }
        //判断用户是否已经投递了该职位
        if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES && $delivery_way != BaseJob::DELIVERY_WAY_LINK) {
            throw new Exception('您已投递，请勿重复投递！');
        }
        // 从这里开始是直聊加入的判断,暂时多加一层,看看是否有影响


        //定义返回-保持所有请求返回结构一致性
        $checkTextOne      = '';
        $checkTextTwo      = '';
        $checkTextThree    = '';
        $checkTextFour     = '';
        $checkTextFive     = '';
        $applyButtonStatus = 1;// 申请按钮是否需要置灰 0不置灰 1置灰
        //职位学历ID
        $jobEducationId = $this->jobCompanyInfo['education_type'];
        //职位要求学历
        $jobEducationName = BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION, $jobEducationId);
        $isSenior         = false;//是否是硕博人才

        //获取简历填写情况
        $completeInfo = BaseResumeComplete::getCompleteInfo($this->memberResumeInfo['member']['id']);
        if (!empty($completeInfo)) {
            if (in_array($this->memberMaxEducationId, BaseDictionary::EDUCATION_DOCTORN_AND_MASTER_ID)) {
                $isSenior = true;
            }
        }
        //获取用户简历完成情况
        $resumeCompleteInfo = BaseResumeComplete::getCompleteList($this->memberResumeInfo['member']['id']);
        //是否有学术成果
        $hasAcademic = $resumeCompleteInfo['hasAcademic'];
        //是否有科研项目
        $hasProject = $resumeCompleteInfo['hasProject'];
        //剩余未完善模块数量
        $notCompleteModuleNum              = $resumeCompleteInfo['notCompleteModuleNum'];
        $result                            = [];
        $result['deliveryTopIsShow']       = false;//true显示false不显示
        $result['deliveryTopIsCheckBox']   = 2;//1选中2不选中
        $result['deliveryTopSourceAmount'] = 0;
        $result['deliveryTopTips']         = '';
        $result['deliveryTopButton']       = false;//true显示false不显示
        $userEmail                         = '';//用户邮箱
        $onlineApply                       = '';//保存链接跳转
        $otherTxt                          = '';//其他投递方式文本
        $isEmailApply                      = false;//邮箱投递
        $isOnlineApply                     = false;//链接投递
        $isOtherApply                      = false;//其他投递
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            $applyTypeTxt = '';
            $applyTypeArr = [];
            if ($this->jobCompanyInfo['apply_type']) {
                $applyTypeArr = explode(',', $this->jobCompanyInfo['apply_type']);
                //如果是其他投递
                $applyTypeName = '';
                foreach ($applyTypeArr as $item) {
                    $applyTypeName .= BaseDictionary::getDataName(BaseDictionary::TYPE_SIGN_UP, $item) . '/';
                }
                $applyTypeTxt = trim($applyTypeName, '/');
            }
            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $isEmailApply = true;
                // $userEmail    = StringHelper::strEmailDes($this->memberResumeInfo['member']['email']);
                $userEmail = MaskHelper::getEmail($this->memberResumeInfo['member']['email']);
            } elseif (in_array(BaseJob::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
                $isOnlineApply = true;
                $onlineApply   = $this->jobCompanyInfo['apply_address'];
            } else {
                $onlineApply  = $this->jobCompanyInfo['apply_address'];
                $isOtherApply = true;
                $otherTxt     = $applyTypeTxt;
            }
        } else {
            $applyTypeTxt = '';
            $applyTypeArr = [];
            if ($this->jobCompanyInfo['announcement']['apply_type']) {
                $applyTypeArr = explode(',', $this->jobCompanyInfo['announcement']['apply_type']);
                //如果是其他投递
                $applyTypeName = '';
                foreach ($applyTypeArr as $item) {
                    $applyTypeName .= BaseDictionary::getDataName(BaseDictionary::TYPE_SIGN_UP, $item) . '/';
                }
                $applyTypeTxt = trim($applyTypeName, '/');
            }
            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $isEmailApply = true;
                // $userEmail    = StringHelper::strEmailDes($this->memberResumeInfo['member']['email']);
                $userEmail = MaskHelper::getEmail($this->memberResumeInfo['member']['email']);
            } elseif (in_array(BaseJob::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
                $isOnlineApply = true;
                $onlineApply   = $this->jobCompanyInfo['announcement']['apply_address'];
            } else {
                $onlineApply  = $this->jobCompanyInfo['announcement']['apply_address'];
                $isOtherApply = true;
                $otherTxt     = $applyTypeTxt;
            }
        }
        //单位合作合作类型
        $isCooperation = $this->jobCompanyInfo['company']['is_cooperation'];
        //弹窗类型
        if ($this->complete < $this->configComplete) {
            //全部弹窗站内完善
            $toastType  = 1;
            $isDelivery = false;//是否允许投递
        } else {
            $isDelivery = true;
            if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                if ($this->jobCompanyInfo['delivery_type'] > 0) {//职位属性
                    $delivery_type = $this->jobCompanyInfo['delivery_type'];
                } else {//公告属性
                    //这里特殊处理  公告也没有时候把他当前成职位是站内投递
                    $delivery_type = $this->jobCompanyInfo['announcement']['delivery_type'] > 0 ? $this->jobCompanyInfo['announcement']['delivery_type'] : BaseJob::DELIVERY_TYPE_INSIDE;
                }
                if ($delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) {//站外
                    $toastType = 2;
                } else {//站内
                    $toastType = 1;
                    //权益验证
                    $equity_result                     = $this->equityVerify();
                    $result['deliveryTopIsShow']       = $equity_result['deliveryTopIsShow'];
                    $result['deliveryTopIsCheckBox']   = $equity_result['deliveryTopIsCheckBox'];
                    $result['deliveryTopSourceAmount'] = $equity_result['deliveryTopSourceAmount'];
                    $result['deliveryTopTips']         = $equity_result['deliveryTopTips'];
                    $result['deliveryTopButton']       = $equity_result['deliveryTopButton'];
                }
            } else {
                //非合作单位
                if ($isEmailApply) {//邮箱
                    $toastType = 1;
                } else {//非邮箱
                    $toastType = 2;
                }
            }
        }
        $result['complete']          = intval($this->complete);
        $result['isDelivery']        = $isDelivery;
        $result['toastType']         = $toastType;
        $result['applyButtonStatus'] = $applyButtonStatus;
        //根据端类型+弹窗类型返回相应数据
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                if ($toastType == 1) {
                    //先看是否设置了限制条件+是合作单位+投递方式非网址+可以投递
                    if ($isCooperation && !$isOnlineApply && $isDelivery) {
                        $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                        if ($this->memberMaxEducationId < $jobEducationId) {
                            ///学历校验
                            $checkTextOne = $this->textReplace($this->web_check_1_1_1, $jobEducationName);//文本1
                        }
                        if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                            ///应聘材料的校验
                            $checkTextFour = $this->web_check_1_4_2;//文本4
                        } elseif (!empty($this->announcementInfo) && $this->announcementInfo['is_attachment_notice'] == BaseAnnouncement::IS_ATTACHMENT_NOTICE_YES) {
                            $checkTextFour = $this->web_check_1_4_1;//文本4
                        } else {
                            $checkTextFour = $this->web_check_1_4_3;//文本4
                        }
                    }
                    //可投递+是否硕博
                    if ($isDelivery && $isSenior) {
                        $replace_text = '';
                        //去掉文案---您的【*】内容未完善，完善后可提高您的竞争力哦～
                        //                        if (!$hasAcademic && $hasProject) {
                        //                            $replace_text = $this->web_check_replace_2;
                        //                        } elseif ($hasAcademic && !$hasProject) {
                        //                            $replace_text = $this->web_check_replace_1;
                        //                        } elseif (!$hasAcademic && !$hasProject) {
                        //                            $replace_text = $this->web_check_replace_3;
                        //                        }
                        if (!empty($replace_text)) {
                            $checkTextTwo = $this->textReplace($this->web_check_1_2_2, $replace_text);//文本2
                        } else {
                            $checkTextTwo = $this->textReplace($this->web_check_1_2_3, $this->complete);//文本2
                        }
                    } else {
                        $checkTextTwo = $this->textReplace($this->web_check_1_2_3, $this->complete);//文本2
                    }
                    //完成度
                    if ($this->complete == 100) {
                        //$checkTextThree = $this->web_check_1_3_2;//文本3
                    } else {
                        //$checkTextThree = $this->web_check_1_3_1;//文本3
                        if ($this->complete < $this->configComplete) {//不可投递
                            $checkTextTwo = $this->textReplace($this->web_check_1_2_1, $this->configComplete);//文本2
                        }
                    }
                    //文本5
                    //单位性质
                    if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO) {
                        if ($isEmailApply) {
                            $checkTextFive = $this->textReplace($this->web_check_1_5_1, $userEmail);//文本5
                        }
                    } else {
                        if ($isOtherApply) {
                            $checkTextFive = $this->textReplace($this->web_check_1_5_2, $otherTxt);//文本5
                        }
                        //这种情况是不存在的
                        //elseif ($isOnlineApply) {
                        //    $checkTextFive = $this->textReplace($this->web_check_1_5_3, $onlineApply);//文本5
                        //}
                    }
                } else {
                    $checkTextOne = $this->textReplace($this->web_check_2_1_1, $applyTypeTxt);//文本1
                    //特殊处理一下
                    $checkTextTwo = $this->textReplace($this->web_check_1_2_3, $this->complete);//文本2
                    //$checkTextThree = $this->complete != 100 ? $this->web_check_1_3_1 : $this->web_check_1_3_2;//文本3
                }
                $result['checkTextOne']   = $checkTextOne;
                $result['checkTextTwo']   = $checkTextTwo;
                $result['checkTextThree'] = $checkTextThree;
                $result['checkTextFour']  = $checkTextFour;
                $result['checkTextFive']  = $checkTextFive;
                break;
            case self::PLATFORM_H5:
                $resumeTips     = '';
                $attachmentTips = '';
                $applyTips      = '';
                $outSiteTips    = '';
                if ($toastType == 1) {
                    if ($this->complete < 100) {
                        //可投递
                        if ($isDelivery) {
                            //完成度大于75%
                            if ($isSenior) {
                                $replace_text = '';
                                // 完善度≧75%，且求职者简历最高学历为博士，检测到简历未填写【科研项目】&【学术成果】
                                if (!$hasAcademic && $hasProject) {
                                    $replace_text = $this->h5_check_replace_2;
                                } elseif ($hasAcademic && !$hasProject) {
                                    $replace_text = $this->h5_check_replace_1;
                                } elseif (!$hasAcademic && !$hasProject) {
                                    $replace_text = $this->h5_check_replace_3;
                                }
                                if (!empty($replace_text)) {
                                    $resumeTips = $this->textReplace($this->h5_check_1_2_1, $replace_text);
                                } else {
                                    //非以上情况，检测到简历有未填写模块（仅检测 个人优势/研究方向/科研项目/工作经历/学术成果/荣誉奖励/技能特长 7个模块）
                                    $resumeTips = $this->textReplace($this->h5_check_1_2_2, $notCompleteModuleNum);
                                }
                            } else {
                                //非以上情况，检测到简历有未填写模块（仅检测 个人优势/研究方向/科研项目/工作经历/学术成果/荣誉奖励/技能特长 7个模块）
                                $resumeTips = $this->textReplace($this->h5_check_1_2_2, $notCompleteModuleNum);
                            }
                        } else {
                            //完成度小于75%
                            $resumeTips = $this->textReplace($this->h5_check_1_2_4, [
                                $this->complete,
                                $this->configComplete,
                            ]);
                        }
                    }
                    //先看是否设置了限制条件+是合作单位+投递方式非网址+可以投递
                    if ($isCooperation && !$isOnlineApply && $isDelivery) {
                        $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                        if ($this->memberMaxEducationId < $jobEducationId) {
                            ///学历校验
                            $applyTips = $this->textReplace($this->h5_check_1_4_1, $jobEducationName);
                        } elseif (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                            ///应聘材料的校验
                            $applyTips = $this->h5_check_1_4_2;
                        }
                        if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                            $attachmentTips = $this->h5_check_notice_1_4_2;
                        } elseif (!empty($this->announcementInfo) && $this->announcementInfo['is_attachment_notice'] == BaseAnnouncement::IS_ATTACHMENT_NOTICE_YES) {
                            $attachmentTips = $this->h5_check_notice_1_4_1;//文本4
                        } else {
                            $attachmentTips = $this->h5_check_notice_1_4_3;//文本4
                        }
                    }
                    if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO && $isEmailApply) {
                        $applyTips = $this->textReplace($this->h5_check_1_3_1, $userEmail);
                    }
                } else {
                    $outSiteTips = $this->textReplace($this->h5_check_2_1_1, $applyTypeTxt);
                }
                $result['attachmentTips'] = $attachmentTips;
                $result['resumeTips']     = $resumeTips;
                $result['applyTips']      = $applyTips;
                $result['outSiteTips']    = $outSiteTips;
                break;
            case self::PLATFORM_MINI:
                $resumeTips     = '';
                $attachmentTips = '';
                $applyTips      = '';
                $outSiteTips    = '';
                if ($toastType == 1) {
                    if ($this->complete < 100) {
                        //可投递
                        if ($isDelivery) {
                            //完成度大于75%
                            if ($isSenior) {
                                $replace_text = '';
                                // 完善度≧75%，且求职者简历最高学历为博士，检测到简历未填写【科研项目】&【学术成果】
                                if (!$hasAcademic && $hasProject) {
                                    $replace_text = $this->mini_check_replace_2;
                                } elseif ($hasAcademic && !$hasProject) {
                                    $replace_text = $this->mini_check_replace_1;
                                } elseif (!$hasAcademic && !$hasProject) {
                                    $replace_text = $this->mini_check_replace_3;
                                }
                                if (!empty($replace_text)) {
                                    $resumeTips = $this->textReplace($this->mini_check_1_2_1, $replace_text);
                                } else {
                                    //非以上情况，检测到简历有未填写模块（仅检测 个人优势/研究方向/科研项目/工作经历/学术成果/荣誉奖励/技能特长 7个模块）
                                    $resumeTips = $this->textReplace($this->mini_check_1_2_2, $notCompleteModuleNum);
                                }
                            } else {
                                //非以上情况，检测到简历有未填写模块（仅检测 个人优势/研究方向/科研项目/工作经历/学术成果/荣誉奖励/技能特长 7个模块）
                                $resumeTips = $this->textReplace($this->mini_check_1_2_2, $notCompleteModuleNum);
                            }
                        } else {
                            //完成度小于75%
                            $resumeTips = $this->textReplace($this->mini_check_1_2_4, [
                                $this->complete,
                                $this->configComplete,
                            ]);
                        }
                    }
                    //先看是否设置了限制条件+是合作单位+投递方式非网址+可以投递
                    if ($isCooperation && !$isOnlineApply && $isDelivery) {
                        $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                        if ($this->memberMaxEducationId < $jobEducationId) {
                            ///学历校验
                            $applyTips = $this->textReplace($this->mini_check_1_4_1, $jobEducationName);
                        }
                        //elseif (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                        //    ///应聘材料的校验
                        //    $applyTips = $this->mini_check_1_4_2;
                        //}
                        if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                            $attachmentTips = $this->mini_check_notice_1_4_2;
                        } elseif (!empty($this->announcementInfo) && $this->announcementInfo['is_attachment_notice'] == BaseAnnouncement::IS_ATTACHMENT_NOTICE_YES) {
                            $attachmentTips = $this->mini_check_notice_1_4_1;//文本4
                        } else {
                            $attachmentTips = $this->mini_check_notice_1_4_3;//文本4
                        }
                    }
                    if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO && $isEmailApply) {
                        $applyTips = $this->textReplace($this->mini_check_1_3_1, $userEmail);
                    }
                } else {
                    $outSiteTips = $this->textReplace($this->mini_check_2_1_1, $applyTypeTxt);
                }
                $result['attachmentTips'] = $attachmentTips;
                $result['resumeTips']     = $resumeTips;
                $result['applyTips']      = $applyTips;
                $result['outSiteTips']    = $outSiteTips;
                break;
            default:
                break;
        }

        //获取附件列表
        $resumeAttachmentList = BaseResumeAttachment::getList($this->memberResumeInfo['member']['id']);
        //获取最近一次站内投递的附件简历ID
        $lastResumeAttachmentId    = BaseJobApply::find()
            ->select('resume_attachment_id')
            ->andWhere(['resume_id' => $this->memberResumeInfo['id']])
            ->andWhere([
                '>',
                'resume_attachment_id',
                0,
            ])
            ->orderBy('id desc')
            ->scalar();
        $defaultResumeAttachment   = '';
        $resumeAttachmentLastToken = '';
        $newList                   = [];
        foreach ($resumeAttachmentList as $item) {
            $itemList          = [];
            $itemList['label'] = $item['fileName'];
            $itemList['value'] = $item['token'];
            array_push($newList, $itemList);
            if ($item['is_default'] == BaseResumeAttachment::IS_DEFAULT_YES) {
                $defaultResumeAttachment = $item['token'];
            }
            if ($lastResumeAttachmentId == $item['id']) {
                $resumeAttachmentLastToken = $item['token'];
            }
        }
        $result['resumeAttachmentList']      = $newList;
        $result['resumeAttachmentLastToken'] = $resumeAttachmentLastToken;
        $result['defaultResumeAttachment']   = $defaultResumeAttachment;
        $result['onLink']                    = $onlineApply;
        $result['resumeStep']                = BaseResumeComplete::getResumeStep($this->memberResumeInfo['id']);
        if ($this->memberResumeInfo['complete'] < $this->configComplete) {
            //获取用户简历完成步数
            $result['memberResumeCheckInfo']['resumeStepNum'] = $result['resumeStep'];
            $result['memberResumeCheckInfo']['title']         = '提示';
            $result['memberResumeCheckInfo']['content']       = '您的在线简历完善度' . $this->memberResumeInfo['complete'] . '%，简历完善度达' . $this->configComplete . '%方可投递。请先完善简历';
        }

        return $result;
    }

    /**
     * 检测线下招聘会
     * @return array
     */
    private function checkApplyFair()
    {
        //1、点击”立即报名“按钮，校验用户是否已登录；
        //① 若未登录，当前页面弹出快捷登录弹窗；
        //② 若已登录成功，校验是否已完善简历前三步；
        //若未完善完简历前三步，则当前页面toast提示5s：“当前简历信息不完善，请先完善简历！5S后将自动帮您打开简历完善页面（倒计时展示）”，5S后，新页面自动打开待完善信息的步骤页；继续后续简历完善流程；
        //若已完善简历前三步，须校验该职位是否有配置学历校验；若职位有配置学历校验，且求职者最高学历不满足职位学历要求，则无法进行报名，页面须针对性给出报错：
        //对不起，您的学历不符合要求。该场次最低学历要求：XX{职位最低学历要求}
        //2、若求职者符合以上要求，则点击“立即报名”即报名成功；当前页面弹出报名成功提示弹窗；“立即报名”按钮文案变为“已报名”，按钮置灰禁用；该职位/场次报名状态各端同步；
        //若因其他原因报名失败，同样须文案提示失败原因；
        //当前用户简历完成到第几步
        $resume_step    = BaseResumeComplete::getResumeStep($this->memberResumeInfo['id']);
        $isDelivery     = false;
        $checkTextThree = '去完善';
        if ($resume_step >= 4) {
            $isDelivery   = true;
            $checkTextTwo = '简历完成度' . $this->complete . '%';
            if ($this->complete == 100) {
                $checkTextThree = '';
            }
        } else {
            $checkTextTwo = '请先完善简历前第三步';
        }
        //获取附件列表
        $resumeAttachmentList    = ResumeAttachment::getList($this->memberResumeInfo['member']['id']);
        $defaultResumeAttachment = '';
        $newList                 = [];
        foreach ($resumeAttachmentList as $item) {
            $itemList          = [];
            $itemList['label'] = $item['fileName'];
            $itemList['value'] = $item['token'];
            array_push($newList, $itemList);
            if ($item['is_default'] == BaseResumeAttachment::IS_DEFAULT_YES) {
                $defaultResumeAttachment = $item['token'];
            }
        }

        $result = [
            'complete'                => $this->complete,
            'isDelivery'              => $isDelivery,
            'toastType'               => 1,
            'checkTextOne'            => '',
            'checkTextTwo'            => $checkTextTwo,
            'checkTextThree'          => $checkTextThree,
            'checkTextFour'           => '',
            'checkTextFive'           => '',
            'applyButtonStatus'       => 0,
            'resumeAttachmentList'    => $newList,
            'defaultResumeAttachment' => $defaultResumeAttachment,
            'onLink'                  => '',
            'resumeStep'              => $resume_step,
        ];

        if ($this->memberResumeInfo['complete'] < $this->configComplete) {
            //获取用户简历完成步数
            $result['memberResumeCheckInfo']['resumeStepNum'] = $result['resumeStep'];
            $result['memberResumeCheckInfo']['title']         = '提示';
            $result['memberResumeCheckInfo']['content']       = '您的在线简历完善度' . $this->memberResumeInfo['complete'] . '%，简历完善度达' . $this->configComplete . '%方可投递。请先完善简历';
        }

        return $result;
    }

    /**
     * 职位申请
     * ```EOT
     * 参数说明
     *    jobId int 职位ID
     *    token string 附件简历标识
     *    isDefault int 附件简历是否设置默认
     *    stuffFileId string 应聘材料ID逗号拼接
     * 返回参数说明
     *    返回跳转链接地址
     * ```EOT
     * @return mixed|string
     * @throws Exception
     */
    private function apply()
    {
        //验证一下必选参数
        if ($this->requstData['jobId'] <= 0) {
            throw new Exception('参数错误');
        }
        //获取参数
        $token       = $this->requstData['token'] ?? '';
        $isDefault   = $this->requstData['isDefault'] ?? '';
        $stuffFileId = $this->filtrationStuffFileId($this->requstData['stuffFileId']);
        // 检查一下这个材料是否是这个求职者的,把不是的给过滤了
        // $stuffFileId = $this->requstData['stuffFileId'] ?? '';

        //判断简历完整度
        if ($this->complete < $this->configComplete) {
            throw new Exception('完成度不足,请先去完善简历！');
        }
        // 找到今天总投递次数
        $todayCount = BaseJobApply::find()
            ->where(['resume_member_id' => $this->memberResumeInfo['member']['id']])
            ->andWhere([
                '>=',
                'add_time',
                CUR_DATE,
            ])
            ->count();

        if ($todayCount >= Yii::$app->params['applyJobDayLimit']) {
            // Yii::error($this->memberResumeInfo['member']['id'] . '用户今天投递次数超过限制');
            throw new Exception('今天投递次数已用完');
        }
        $applyTypeArr = [];//应聘方式
        $email        = '';//邮箱
        $companyUrl   = '';//链接
        if (!empty($this->jobCompanyInfo['apply_type'])) {
            //应聘方式
            $applyTypeArr = explode(',', $this->jobCompanyInfo['apply_type']);
        }
        //单位合作合作类型
        $isCooperation = $this->jobCompanyInfo['company']['is_cooperation'];

        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            if ($this->jobCompanyInfo['delivery_type'] > 0) {//职位属性
                $delivery_type = $this->jobCompanyInfo['delivery_type'];
            } else {//公告属性
                //这里特殊处理  公告也没有时候把他当前成职位是站内投递
                $delivery_type = $this->jobCompanyInfo['announcement']['delivery_type'] > 0 ? $this->jobCompanyInfo['announcement']['delivery_type'] : BaseJob::DELIVERY_TYPE_INSIDE;
            }
        } else {
            $delivery_type = BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE;
        }
        //合作单位站内投递+非合作单位邮箱投递
        if (($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $delivery_type == BaseJob::DELIVERY_TYPE_INSIDE) || ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO && in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL,
                    $applyTypeArr))) {
            //判断请求平台来源返回对应文本
            $jobEducationName = BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION,
                $this->jobCompanyInfo['education_type']);
            switch ($this->operationPlatform) {
                case self::PLATFORM_WEB:
                    $applyTextEducation = $this->textReplace($this->web_apply_1_1_1, $jobEducationName);//学历
                    $applyTextFile      = $this->web_apply_1_1_2;//材料
                    break;
                case self::PLATFORM_H5:
                    $applyTextEducation = $this->textReplace($this->h5_apply_1_1_1, $jobEducationName);//学历
                    $applyTextFile      = $this->h5_apply_1_1_2;//材料
                    break;
                case self::PLATFORM_MINI:
                    $applyTextEducation = $this->textReplace($this->mini_apply_1_1_1, $jobEducationName);//学历
                    $applyTextFile      = $this->mini_apply_1_1_2;//材料
                    break;
                default:
                    throw new Exception('操作不支持');
            }
            //判断职位是否有限制学历+应聘材料
            if (!empty($this->jobCompanyInfo['delivery_limit_type'])) {
                $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_EDUCATION,
                        $deliveryLimitTypeArr) && $this->memberMaxEducationId < $this->jobCompanyInfo['education_type']) {
                    throw new Exception($applyTextEducation);
                } elseif (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr) && empty($stuffFileId)) {
                    throw new Exception($applyTextFile);
                }
            }
        }
        //确认投递方式
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['delivery_way'];
        } elseif ($this->jobCompanyInfo['announcement']['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['announcement']['delivery_way'];
        } else {
            throw new Exception('职位申请失败');
        }
        //判断用户是否已经投递了该职位
        if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES && $delivery_way != BaseJob::DELIVERY_WAY_LINK) {
            //用户已经投递，暂时不可投递
            // 测试代码.上线记得屏蔽
            // $env = \Yii::$app->params['environment'];
            // if ($env == 'prod') {
            // }
            throw new Exception('您已投递过该职位，请勿重复投递！');
        }

        //判断附件简历信息
        $resumeAttachmentId = 0;
        if (!empty($token)) {
            $resumeAttachmentInfo = BaseResumeAttachment::find()
                ->where([
                    'token'     => $token,
                    'status'    => BaseResumeAttachment::STATUS_ACTIVE,
                    'member_id' => $this->memberResumeInfo['member']['id'],
                ])
                ->asArray()
                ->one();
            if (empty($resumeAttachmentInfo)) {
                throw new Exception('附件简历不存在');
            }
            $resumeAttachmentId = $resumeAttachmentInfo['id'];
        }
        //判断应聘材料是否超出限制
        if (!empty($stuffFileId)) {
            $stuffFileIdArr = explode(',', $stuffFileId);
            if (count($stuffFileIdArr) > BaseJobApply::STUFF_FILE_LIMIT_NUM) {
                throw new Exception('应聘材料不得超过' . BaseJobApply::STUFF_FILE_LIMIT_NUM . '个');
            }
        }
        $platform = $this->getPlatform();
        //投递记录确认
        // 站内投递
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $delivery_type == BaseJob::DELIVERY_TYPE_INSIDE) {
            //前提：对该次投递使用投递置顶权益
            //1、站内投递需要检验资源够不够
            //2、站内投递需要检验30天内对该职位是否使用过
            if ($this->requstData['deliveryTopIsCheckBox'] == 1) {
                $resumeEquityPackageData = BaseResumeEquityPackage::getEquityList($this->memberResumeInfo['id'],
                    BaseResumeEquitySetting::ID_DELIVERY_TOP, BaseResumeEquityPackage::STATUS_EXPIRE);

                if (count($resumeEquityPackageData) > 0) {
                    $source_amount = array_sum(array_column($resumeEquityPackageData, 'amount'));
                    if ($source_amount <= 0) {
                        throw new Exception('投递置顶资源不足');
                    }
                } else {
                    throw new Exception('您的没有投递置顶权益');
                }
                if (BaseJobApplyTopEquityRecord::isUseEquity($this->jobCompanyInfo['id'],
                    $this->memberResumeInfo['id'])) {
                    throw new Exception('该职位30天内已使用过投递置顶权益');
                }
            }

            //继续执行申请职位操作
            //BaseJob::apply($this->jobCompanyInfo['id'], $this->memberResumeInfo['member']['id'],
            //    $this->memberResumeInfo['id'], $resumeAttachmentId, $stuffFileId, $delivery_way, $platform);
            //做一个投递统计
            $data = [
                'job_id'               => $this->jobCompanyInfo['id'],
                'member_id'            => $this->memberResumeInfo['member']['id'],
                'resume_id'            => $this->memberResumeInfo['id'],
                'resume_attachment_id' => $resumeAttachmentId,
                'stuff_file_id'        => $stuffFileId,
                'delivery_way'         => $delivery_way,
                'platform'             => $platform,
                'operation_platform'   => $this->operationPlatform,
                'equity_status'        => $this->requstData['deliveryTopIsCheckBox'] == 1 ? 1 : 0,
            ];
            //继续执行申请职位操作
            $applyId = BaseJob::apply($data);
            //投递完毕做一个消耗权益的处理
            if ($this->requstData['deliveryTopIsCheckBox'] == 1) {
                //扣除权益资源
                $resume_equity_package_result = BaseResumeEquityPackage::deductEquityAmount($this->memberResumeInfo['id'],
                    BaseResumeEquitySetting::ID_DELIVERY_TOP);
                if (isset($resume_equity_package_result['resume_equity_package_id']) && $resume_equity_package_result['resume_equity_package_id'] > 0) {
                    $resume_equity_package_detail_result = BaseResumeEquityPackageDetail::deductEquityAmount($this->memberResumeInfo['id'],
                        $resume_equity_package_result['package_category_id'], BaseResumeEquitySetting::ID_DELIVERY_TOP);
                    //做一个投递置顶权益使用记录
                    $model = new BaseJobApplyTopEquityRecord();
                    $model->setAttributes([
                        'job_id'                   => $this->jobCompanyInfo['id'],
                        'resume_id'                => $this->memberResumeInfo['id'],
                        'apply_id'                 => $applyId,
                        'equity_package_detail_id' => $resume_equity_package_detail_result['resume_equity_package_detail_id'],
                        'equity_status'            => BaseJobApplyTopEquityRecord::EQUITY_STATUS_EFFECT,
                        'expire_type'              => BaseJobApplyTopEquityRecord::EXPIRE_TYPE_EFFECT,
                        'add_time'                 => date('Y-m-d H:i:s'),
                        'update_time'              => date('Y-m-d H:i:s'),
                    ]);
                    $model->save();
                    //流水
                    $equityIds     = [BaseResumeEquitySetting::ID_DELIVERY_TOP];
                    $before_amount = [BaseResumeEquitySetting::ID_DELIVERY_TOP => $resume_equity_package_result['before_amount']];
                    $after_amount  = [BaseResumeEquitySetting::ID_DELIVERY_TOP => $resume_equity_package_result['after_amount']];
                    BaseResumeEquityActionRecord::saveActionRecord(date('Y-m-d H:i:s'), $this->memberResumeInfo['id'],
                        $equityIds, BaseResumeEquityActionRecord::EQUITY_TYPE_USED,
                        BaseResumeEquityActionRecord::ACTION_USED_DELIVERY_TOP, $applyId,
                        BaseResumeEquityActionRecord::TYPE_RELATION_REMARK_DELIVERY_TOP,
                        BaseResumeEquityActionRecord::TYPE_OPERATION_RESUME, $this->memberResumeInfo['id'], 0,
                        $before_amount, $after_amount);
                } else {

                }
            }
            //做一个投递统计
            BaseJobApplyRecordExtra::JobApplyCount($this->jobCompanyInfo['id'], $this->memberResumeInfo['id'],
                $platform);
        } else {// 站外投递
            $wage = BaseJob::formatWage($this->jobCompanyInfo['min_wage'], $this->jobCompanyInfo['max_wage'],
                $this->jobCompanyInfo['wage_type']);
            if (!empty($this->jobCompanyInfo['apply_type'])) {
                if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                    $applyStatus = BaseOffSiteJobApply::APPLY_STATUS_DELIVERY;
                    $email       = $this->jobCompanyInfo['apply_address'];
                } else {
                    $companyUrl = $this->jobCompanyInfo['apply_address'];
                }
            } else {
                if (!empty($this->jobCompanyInfo['announcement']['apply_type'])) {
                    if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL,
                        explode(',', $this->jobCompanyInfo['announcement']['apply_type']))) {
                        $applyStatus = BaseOffSiteJobApply::APPLY_STATUS_DELIVERY;
                        $email       = $this->jobCompanyInfo['announcement']['apply_address'];
                    } else {
                        $companyUrl = $this->jobCompanyInfo['announcement']['apply_address'];
                    }
                } else {
                    throw new Exception('职位申请失败');
                }
            }
            $offSiteJobApplyData = [
                //公告标题
                'applyStatus'        => $applyStatus ?: BaseOffSiteJobApply::APPLY_STATUS_EMPLOYED_WAIT,
                'title'              => BaseAnnouncement::findOneVal(['id' => $this->jobCompanyInfo['announcement_id']],
                    'title'),
                'job_name'           => $this->jobCompanyInfo['name'],
                'applyDate'          => CUR_DATE,
                'company_id'         => $this->jobCompanyInfo['company_id'],
                'delivery_way'       => $delivery_way,
                'salary'             => $wage,
                'link'               => $companyUrl,
                'source'             => BaseOffSiteJobApply::SOURCE_WEBSITE,
                'jobId'              => $this->jobCompanyInfo['id'],
                'announcementId'     => $this->jobCompanyInfo['announcement_id'],
                'resumeAttachmentId' => $resumeAttachmentId,
                'stuffFileId'        => $stuffFileId,
                'email'              => $email,
                'platform'           => $platform,
                'operation_platform' => $this->operationPlatform,
            ];
            //新增站外投递记录
            //看看是不是链接投递
            $record_bool = true;
            //是否有投递记录
            $job_apply_info = BaseJobApplyRecord::find()
                ->where([
                    'job_id'        => $this->jobCompanyInfo['id'],
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'delivery_way'  => BaseJobApplyRecord::DELIVERY_WAY_LINK,
                ])
                ->asArray()
                ->one();
            if ($delivery_way == BaseJob::DELIVERY_WAY_LINK && !empty($job_apply_info)) {
                $record_bool = false;
            }
            if ($record_bool) {
                //站外投递
                $offSiteJobApplyId = BaseOffSiteJobApply::saveInfo($offSiteJobApplyData,
                    $this->memberResumeInfo['member']['id']);
                //做一个投递统计
                BaseJobApplyRecordExtra::JobApplyCount($this->jobCompanyInfo['id'], $this->memberResumeInfo['id'],
                    $platform);
            }
            //判断是发送电子邮件，还是条跳转网页
            if (!empty($email)) {
                //创建邮件发送记录
                $offData = [
                    'jobId'             => $this->jobCompanyInfo['id'],
                    'resumeId'          => $this->memberResumeInfo['id'],
                    'offSiteJobApplyId' => $offSiteJobApplyId ?: '',
                ];
                if ($resumeAttachmentId > 0) {
                    $offData['resumeAttachmentId'] = $resumeAttachmentId;
                }
                if ($stuffFileId > 0) {
                    $offData['stuffFileId'] = $stuffFileId;
                }
                //站外投递
                $emailLogId                         = Producer::email($email, 1, EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    $offData);
                $offSiteJobApplyModel               = BaseOffSiteJobApply::findOne($offSiteJobApplyId);
                $offSiteJobApplyModel->email_log_id = $emailLogId;
                $offSiteJobApplyModel->save();
            }
        }

        //判断是否设置默认简
        if ($isDefault == BaseResumeAttachment::IS_DEFAULT_YES && $resumeAttachmentId > 0) {
            BaseResumeAttachment::setDefaultResume($resumeAttachmentId,
                $this->memberResumeInfo['member']['id']);//设置默认简历
        }

        $wxBindQrCodeImageUrl = $applySuccessMsg = '';
        if ($this->operationPlatform == CommonService::PLATFORM_WEB) {
            $resumeId = BaseResume::findOneVal(['member_id' => $this->memberId], 'id');
            try {
                $data                 = WxPublic::getInstance($this->memberResumeInfo['member']['type'])
                    ->createBindQrCode($resumeId);
                $wxBindQrCodeImageUrl = $data['url'];
                $applySuccessMsg      = '微信扫码绑定，实时接收投递反馈。';
            } catch (\Exception $e) {
                $wxBindQrCodeImageUrl = '';
                $applySuccessMsg      = '请及时关注邮箱、服务号等渠道发送的投递反馈';
            }
        }


        return [
            'link'                 => $companyUrl,
            'wxBindQrCodeImageUrl' => $wxBindQrCodeImageUrl,
            'applySuccessMsg'      => $applySuccessMsg,
            'apply_status'         => $delivery_way == BaseJob::DELIVERY_WAY_LINK ? BaseJob::JOB_APPLY_STATUS_NO : BaseJob::JOB_APPLY_STATUS_YES,
            'applyId'              => $applyId,
        ];
    }

    /**
     * 线下招聘会场次申请
     * ```EOT
     * 参数说明
     *    jobId int 职位ID
     * 返回参数说明
     *    success_one  成功文本一
     *    success_one  成功文本二
     *    qrcode_link  二维码图片地址
     * ```EOT
     * @return array
     * @throws Exception
     */
    private function applyFair()
    {
        //当前用户简历完成到第几步
        $resume_step = BaseResumeComplete::getResumeStep($this->memberResumeInfo['id']);
        if ($resume_step < 4) {
            throw new Exception('当前简历信息不完善，请先完善简历！');
        }
        //判断用户是否已经投递了该职位
        if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES) {
            //用户已经投递，暂时不可投递
            throw new Exception('您已报名该场次，请勿重复报名！');
        }
        $jobEducationName = BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION,
            $this->jobCompanyInfo['education_type']);
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                $toast_education = $this->textReplace($this->web_check_fair_1_1_1, $jobEducationName);
                $success_one     = $this->textReplace($this->web_success_fair_1_1_1, $this->jobCompanyInfo['name']);
                $success_two     = $this->web_success_fair_1_2_1;
                break;
            case self::PLATFORM_H5:
                $toast_education = $this->textReplace($this->h5_check_fair_1_1_1, $jobEducationName);
                $success_one     = $this->textReplace($this->h5_success_fair_1_1_1, $this->jobCompanyInfo['name']);
                $success_two     = $this->h5_success_fair_1_2_1;
                break;
            case self::PLATFORM_MINI:
                $toast_education = $this->textReplace($this->mini_check_fair_1_1_1, $jobEducationName);
                $success_one     = $this->textReplace($this->mini_success_fair_1_1_1, $this->jobCompanyInfo['name']);
                $success_two     = $this->mini_success_fair_1_2_1;
                break;
            default:
                throw new Exception('操作不支持');
        }
        //判断职位是否有限制学历
        if (!empty($this->jobCompanyInfo['delivery_limit_type'])) {
            $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
            //判断请求平台来源返回对应文本

            if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_EDUCATION,
                    $deliveryLimitTypeArr) && $this->memberMaxEducationId < $this->jobCompanyInfo['education_type']) {
                throw new Exception($toast_education);
            }
        }
        //招聘会必须是合作单位类型
        if ($this->jobCompanyInfo['company']['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_NO) {
            throw new Exception('职位单位类型出错');
        }
        //确认投递方式
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['delivery_way'];
        } elseif ($this->jobCompanyInfo['announcement']['delivery_way'] > 0) {
            $delivery_way = $this->jobCompanyInfo['announcement']['delivery_way'];
        } else {
            throw new Exception('职位申请失败');
        }
        //平台
        $platform = $this->getPlatform();
        $data     = [
            'job_id'               => $this->jobCompanyInfo['id'],
            'member_id'            => $this->memberResumeInfo['member']['id'],
            'resume_id'            => $this->memberResumeInfo['id'],
            'resume_attachment_id' => 0,
            'stuff_file_id'        => '',
            'delivery_way'         => $delivery_way,
            'platform'             => $platform,
            'operation_platform'   => $this->operationPlatform,
        ];
        //继续执行申请记录
        BaseJob::apply($data);

        return [
            'success_one'  => $success_one,
            'success_two'  => $success_two,
            'qrcode_link'  => Yii::$app->params['doubleMeetingActivity2022']['deliverySuccessQrcode'],
            'apply_status' => BaseJob::JOB_APPLY_STATUS_YES,
        ];
    }

    private function filtrationStuffFileId($stuffFileId)
    {
        if (!$stuffFileId) {
            return '';
        }
        $memberId       = $this->memberResumeInfo['member']['id'];
        $stuffFileArray = explode(',', $stuffFileId);
        $idArray        = BaseFile::find()
            ->select('id')
            ->where([
                'id'         => $stuffFileArray,
                'creator_id' => $memberId,
            ])
            ->asArray()
            ->column();

        return implode(',', $idArray) ?: '';
    }

    /**
     * 换取平台类型
     */
    private function getPlatform()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                $platform = BaseJobApplyRecord::PLATFORM_PC;
                break;
            case self::PLATFORM_H5:
                $platform = BaseJobApplyRecord::PLATFORM_H5;
                break;
            case self::PLATFORM_MINI:
                $platform = BaseJobApplyRecord::PLATFORM_MINI;
                break;
            default:
                $platform = 0;
                break;
        }

        return $platform;
    }

    /**
     * 权益验证
     */
    private function equityVerify()
    {
        //这里校验一下是否有投递置顶权益
        $resumeEquityPackageData = BaseResumeEquityPackage::getEquityList($this->memberResumeInfo['id'],
            BaseResumeEquitySetting::ID_DELIVERY_TOP, BaseResumeEquityPackage::STATUS_EXPIRE);
        $result                  = [
            'deliveryTopIsShow'       => false,
            'deliveryTopIsCheckBox'   => 2,
            'deliveryTopSourceAmount' => 0,
            'deliveryTopTips'         => '',
            'deliveryTopButton'       => false,
        ];
        if (count($resumeEquityPackageData) > 0) {
            $result['deliveryTopIsShow']       = true;
            $source_amount                     = array_sum(array_column($resumeEquityPackageData, 'amount'));
            $result['deliveryTopSourceAmount'] = $source_amount;
            if ($source_amount >= 1) {
                //检验一下该职位30天内是否有投递置顶权益
                $isEquity = BaseJobApplyTopEquityRecord::isUseEquity($this->jobCompanyInfo['id'],
                    $this->memberResumeInfo['id']);
                if ($isEquity) {
                    //30天内使用过
                    $result['deliveryTopTips'] = $this->textReplace($this->equity_tips_3, $source_amount);
                } else {
                    $result['deliveryTopIsCheckBox'] = 1;
                    $result['deliveryTopTips']       = $this->textReplace($this->equity_tips_1, $source_amount);
                }
            } else {
                $result['deliveryTopTips']   = $this->equity_tips_2;
                $result['deliveryTopButton'] = true;
            }
        }

        return $result;
    }

    //优化
    private function checkItemWeb()
    {
    }

    private function checkItemH5()
    {
    }

    private function checkItemMini()
    {
    }
}