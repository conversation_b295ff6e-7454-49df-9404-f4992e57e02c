# 求职者注销180天限制功能实现

## 概述

本次开发完成了求职者注销后180天内限制重新注册的功能。当用户注销账号后，系统会自动添加180天的限制记录，防止用户使用相同的手机号或邮箱重新注册。

## 实现的功能

### 1. 新增服务类：ResumeCancelRestrictionService

**文件位置**：`common/service/memberCancel/ResumeCancelRestrictionService.php`

**主要方法**：

- `addRestriction($mobile, $email, $mobileCode, $cancelLogId)` - 添加180天限制记录
- `checkRestriction($mobile, $email, $mobileCode)` - 检查限制状态
- `cleanExpiredRestrictions()` - 清理过期限制记录
- `getRestrictionByCancelLogId($cancelLogId)` - 根据注销日志ID获取限制记录
- `getUserRestrictions($mobile, $email, $mobileCode)` - 获取用户的所有限制记录
- `removeRestriction($restrictionId, $adminId)` - 删除限制记录（特殊情况使用）

### 2. 修改现有服务

#### ResumeCancelDataCleanService
**修改内容**：完善了 `addRestrictionRecord()` 方法
- 调用新的限制服务添加180天限制记录
- 添加了完整的错误处理和日志记录

#### ResumeCancelTimerService  
**修改内容**：完善了 `cleanExpiredRestrictions()` 方法
- 调用新的限制服务清理过期记录
- 添加了完整的错误处理和日志记录

## 业务流程

### 1. 添加限制记录流程

```
用户注销完成 → ResumeCancelDataCleanService::executeCancel()
                ↓
              addRestrictionRecord()
                ↓
              ResumeCancelRestrictionService::addRestriction()
                ↓
              创建限制记录（注销时间+180天）
```

### 2. 限制检查流程

```
用户注册 → BaseMemberLoginForm::checkResumeCancelRestrictionExits()
            ↓
          查询 resume_cancel_restriction 表
            ↓
          检查是否存在有效限制记录
            ↓
          如果存在：抛出异常，阻止注册
          如果不存在：允许注册
```

### 3. 清理过期记录流程

```
定时任务 → ResumeCancelTimerService::cleanExpiredRestrictions()
            ↓
          ResumeCancelRestrictionService::cleanExpiredRestrictions()
            ↓
          删除过期的限制记录
```

## 数据库表结构

**表名**：`resume_cancel_restriction`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键ID |
| add_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| mobile | varchar(16) | 手机号 |
| mobile_code | varchar(16) | 手机号区号 |
| email | varchar(256) | 邮箱 |
| cancel_log_id | int | 注销日志ID |
| restriction_end_time | datetime | 限制结束时间（注销时间+180天） |

## 关键特性

### 1. 自动化处理
- 用户注销完成后自动添加限制记录
- 用户注册时自动检查限制状态
- 定时任务自动清理过期记录

### 2. 完整的错误处理
- 所有方法都有异常处理
- 详细的错误日志记录
- 友好的错误提示信息

### 3. 灵活的查询条件
- 支持手机号查询（包含区号）
- 支持邮箱查询
- 支持手机号和邮箱的组合查询

### 4. 日志记录
- 所有关键操作都有日志记录
- 使用 'resume-cancel' 日志分类
- 包含操作结果和关键参数

## 使用示例

### 添加限制记录
```php
$restrictionService = new ResumeCancelRestrictionService();
$restrictionId = $restrictionService->addRestriction(
    '13800138000',      // 手机号
    '<EMAIL>', // 邮箱
    '86',               // 手机号区号
    123                 // 注销日志ID
);
```

### 检查限制状态
```php
$restrictionService = new ResumeCancelRestrictionService();
$result = $restrictionService->checkRestriction(
    '13800138000',      // 手机号
    '<EMAIL>', // 邮箱
    '86'                // 手机号区号
);

if ($result['isRestricted']) {
    echo $result['message']; // 显示限制提示
}
```

### 清理过期记录
```php
$restrictionService = new ResumeCancelRestrictionService();
$result = $restrictionService->cleanExpiredRestrictions();
echo $result['message']; // 显示清理结果
```

## 注意事项

1. **数据一致性**：限制记录与注销日志通过 `cancel_log_id` 关联
2. **时间计算**：180天限制从注销完成时间开始计算
3. **查询优化**：限制检查查询已优化，使用索引提高性能
4. **错误处理**：所有异常都会被捕获并记录日志
5. **向后兼容**：新功能与现有的注册检查逻辑完全兼容

## 测试建议

1. **功能测试**：
   - 测试注销完成后是否正确添加限制记录
   - 测试限制期内注册是否被正确阻止
   - 测试限制期结束后是否可以正常注册

2. **边界测试**：
   - 测试只有手机号的情况
   - 测试只有邮箱的情况
   - 测试手机号和邮箱都有的情况

3. **异常测试**：
   - 测试无效参数的处理
   - 测试数据库连接异常的处理
   - 测试并发操作的处理

## 部署说明

1. 确保数据库表 `resume_cancel_restriction` 已创建
2. 部署新的服务类文件
3. 更新现有的服务类文件
4. 配置定时任务定期清理过期记录
5. 验证功能正常工作

## 维护说明

1. **日志监控**：定期检查 'resume-cancel' 分类的日志
2. **性能监控**：监控限制检查查询的性能
3. **数据清理**：确保定时任务正常运行，及时清理过期记录
4. **异常处理**：关注异常日志，及时处理问题
