<?php

namespace common\service\memberCancel;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageSetting;
use common\components\MessageException;
use common\helpers\MaskHelper;
use common\libs\JwtAuth;
use common\libs\SmsQueue;
use queue\Producer;
use Yii;

/**
 * 求职者注销核心业务服务
 */
class ResumeCancelService
{

    /**
     * 检查注销资格
     * @param int $resumeId 简历ID
     * @return array 资格检查结果
     * @throws \Exception
     */
    public function checkEligibility($resumeId)
    {
        $resume = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        $memberId = $resume->member_id;
        $member   = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        // 检查用户状态
        if ($member->status != BaseMember::STATUS_ACTIVE) {
            throw new \Exception('用户状态异常，无法申请注销');
        }

        // 检查是否有进行中的注销申请
        $existingLog = BaseResumeCancelLog::find()
            ->where([
                'member_id' => $memberId,
                'status'    => BaseResumeCancelLog::STATUS_APPLYING,
            ])
            ->one();

        if ($existingLog) {
            throw new \Exception('您已有进行中的注销申请，请勿重复提交');
        }

        // 这个时候看是否有进行中的服务
        $activeServices = $this->checkActiveJobServices($resumeId);
        if (!$activeServices) {
            return [
                'hasActiveServices' => false,
            ];
        }

        return [
            // 是否有进行中的服务
            'hasActiveServices' => true,
            'serviceWarning'    => '您当前有生效中的' . implode('、',
                    $activeServices) . '。注销账号后，所有资源将被彻底清空且无法恢复，您确定要注销吗？',
        ];
    }

    /**
     * 发送注销短信验证码
     * @param string $mobile     手机号
     * @param string $mobileCode 手机号区号
     * @return void
     * @throws \Exception
     */
    public function sendCancelSms($mobile, $mobileCode = '86')
    {
        if (empty($mobile)) {
            throw new \Exception('手机号不能为空');
        }

        // 使用标准的短信队列系统发送验证码
        // 参数说明：手机号, 用户类型, 短信类型, 手机区号
        Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);
    }

    /**
     * 验证短信验证码
     * @param string $mobile 手机号
     * @param string $code   验证码
     * @return void
     * @throws \Exception
     */
    private function validateSmsCode($mobile, $code, $mobileCode = '86')
    {
        if (empty($mobile) || empty($code)) {
            throw new \Exception('手机号和验证码不能为空');
        }

        // 使用标准的 SmsQueue 验证机制
        $sms = new SmsQueue($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);

        if (!$sms->validation($code)) {
            throw new \Exception('验证码错误或已过期');
        }
    }

    /**
     * 提交注销申请
     * @param array $params 申请参数
     * @return array 申请结果
     * @throws \Exception
     */
    public function applyCancel($params)
    {
        $resumeId = $params['resumeId'];
        $resume   = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        $memberId = $resume->member_id;
        $member   = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        // 验证短信验证码
        $this->validateSmsCode($member->mobile, $params['smsCode'], $member->mobile_code);

        // 检查用户状态
        if ($member->status != BaseMember::STATUS_ACTIVE) {
            throw new \Exception('用户状态异常，无法申请注销');
        }

        // 检查是否有进行中的注销申请
        $existingLog = BaseResumeCancelLog::find()
            ->where([
                'member_id' => $memberId,
                'status'    => BaseResumeCancelLog::STATUS_APPLYING,
            ])
            ->one();

        if ($existingLog) {
            throw new \Exception('您已有进行中的注销申请，请勿重复提交');
        }

        // 创建注销日志记录
        $cancelLog                       = new BaseResumeCancelLog();
        $cancelLog->member_id            = $memberId;
        $cancelLog->resume_id            = $resumeId;
        $cancelLog->cancel_reason_type   = $params['cancelReasonType'];
        $cancelLog->cancel_reason_detail = $params['cancelReasonDetail'] ?? '';
        $cancelLog->apply_time           = date('Y-m-d H:i:s');
        //    发起注销后的第7天（含发起注销日）23：59执行最终注销：
        $cancelLog->cooldown_end_time = date('Y-m-d H:i:s', strtotime('+6 days 23:59:59'));
        $cancelLog->status            = BaseResumeCancelLog::STATUS_APPLYING;
        $cancelLog->ip                = $params['ip'] ?? '';

        if (!$cancelLog->save()) {
            throw new \Exception('创建注销日志失败：' . $cancelLog->getFirstErrorsMessage());
        }

        // 执行预处理操作（立即执行，不等到冷静期结束）
        $dataCleanService = new ResumeCancelDataCleanService();
        $dataCleanService->executePreCancelOperations($resume, $member);

        // 更新短信发送状态
        $cancelLog->sms_status = BaseResumeCancelLog::SMS_STATUS_APPLY_SENT;
        $cancelLog->save();

        // 发送申请成功通知
        $this->sendApplyNotification($member->mobile, $member->mobile_code);

        return [
            'cancelLogId'     => $cancelLog->id,
            'cooldownEndTime' => $cancelLog->cooldown_end_time,
        ];
    }

    /**
     * 获取注销配置信息
     * @return array 注销配置信息
     * @throws \Exception
     */
    public function getCancelConfig($userId)
    {
        // 注销原因列表
        $cancelReasonList = [];
        foreach (BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST as $key => $value) {
            $cancelReasonList[] = [
                'value'  => $key,
                'label'  => $value,
                'notice' => $this->getCancelNotice($key),
            ];
        }

        // 找到这个用户的手机号和code
        $memberInfo = BaseMember::find()
            ->where(['id' => $userId])
            ->select([
                'mobile',
                'mobile_code',
            ])
            ->asArray()
            ->one();

        return [
            'cancelReasonList' => $cancelReasonList,
            'userMobileCode'   => $memberInfo['mobile_code'],
            'userMobile'       => MaskHelper::getPhoneLast($memberInfo['mobile']),
            // 'cancelDialogContent' => $cancelDialogContent,
            // 'cancelAgreementUrl'  => $cancelAgreementUrl,
        ];
    }

    /**
     * 获取注销弹窗内容
     * @return string HTML格式的弹窗内容
     */
    private function getCancelDialogContent()
    {
        return '<div class="cancel-dialog-content">
            <h3>账号注销须知</h3>
            <p>在您申请注销账号前，请仔细阅读以下重要信息：</p>
            <ul>
                <li><strong>冷静期：</strong>提交注销申请后，您的账号将进入7天冷静期，期间可以撤回申请</li>
                <li><strong>数据清理：</strong>注销完成后，您的个人信息将被永久删除且无法恢复</li>
                <li><strong>服务影响：</strong>注销后您将无法使用任何求职服务，包括简历投递、职位收藏等</li>
                <li><strong>重新注册限制：</strong>注销完成后180天内，相同手机号和邮箱无法重新注册</li>
                <li><strong>业务数据：</strong>为了维护平台正常运营，部分去标识化的业务数据将被保留</li>
            </ul>
            <p class="warning">请确认您已充分了解注销的影响，并同意相关条款后再进行操作。</p>
        </div>';
    }

    /**
     * 获取注销协议URL
     * @return string 注销协议的URL地址
     */
    private function getCancelAgreementUrl()
    {
        // 这里可以根据实际情况返回注销协议的URL
        // 可以是静态页面或者动态生成的协议页面
        return '/agreement/cancel-account';
    }

    /**
     * 撤回注销申请
     * @return array 撤回结果
     * @throws \Exception
     */
    public function withdrawCancel($token)
    {
        $jwtAuth = new JwtAuth();
        $memberId = $jwtAuth->checkToken($token);

        if ($memberId === false) {
            throw new MessageException('无效的用户');
        }

        $member = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        // 查找进行中的注销申请
        $cancelLog = BaseResumeCancelLog::find()
            ->where([
                'member_id' => $memberId,
                'status'    => BaseResumeCancelLog::STATUS_APPLYING,
            ])
            ->one();

        if (!$cancelLog) {
            throw new \Exception('没有找到进行中的注销申请');
        }

        // 检查是否还在冷静期内
        if (strtotime($cancelLog->cooldown_end_time) <= time()) {
            throw new \Exception('冷静期已结束，无法撤回注销申请');
        }

        // 更新注销日志状态为已撤回
        $cancelLog->status        = BaseResumeCancelLog::STATUS_WITHDRAWN;
        $cancelLog->withdraw_time = date('Y-m-d H:i:s');
        if (!$cancelLog->save()) {
            throw new \Exception('更新注销日志失败：' . $cancelLog->getFirstErrorsMessage());
        }

        // 恢复预处理操作
        $dataCleanService = new ResumeCancelDataCleanService();
        $dataCleanService->restorePreCancelOperations($memberId, $cancelLog->resume_id, $cancelLog);

        return [
            'success'      => true,
            'message'      => '注销申请已成功撤回',
            'withdrawTime' => $cancelLog->withdraw_time,
        ];
    }

    /**
     * 发送申请成功通知
     * @param string $mobile     手机号
     * @param string $mobileCode 手机号区号
     * @return void
     * @throws \Exception
     */
    private function sendApplyNotification($mobile, $mobileCode = '86')
    {
        // 使用项目现有的短信队列系统发送通知
        // 参数说明：手机号, 用户类型, 短信类型, 手机区号
        Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_APPLY_SUCCESS, $mobileCode);
    }

    /**
     * 发送注销提醒短信（冷静期第6天）
     * @param string $mobile     手机号
     * @param string $mobileCode 手机号区号
     * @return void
     * @throws \Exception
     */
    public static function sendCancelReminder($mobile, $mobileCode = '86')
    {
        // 使用项目现有的短信队列系统发送提醒
        // 参数说明：手机号, 用户类型, 短信类型, 手机区号
        Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_REMINDER, $mobileCode);
    }

    // 获取当前生效中的权益包内容，、隔开，如果没有就返回false
    private function checkActiveJobServices($resumeId)
    {
        $activeServices = BaseResumeEquityPackage::find()
            ->select(['b.name'])
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEquityPackageSetting::tableName()],
                'a.package_category_id=b.equity_package_category_id')
            ->where([
                'a.resume_id'     => $resumeId,
                'a.expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->groupBy(['b.id'])
            ->column();

        return $activeServices ?: false;
    }

    // 根据不同的注销原因获取不同的温馨提示文案
    private function getCancelNotice($cancelType)
    {
        switch ($cancelType) {
            case BaseResumeCancelLog::CANCEL_REASON_TYPE_FOUND_JOB:
                $text = '恭喜您找到新工作，建议您隐藏简历，不被打扰，再有需要时，仍可继续使用呢';
                break;
            case BaseResumeCancelLog::CANCEL_REASON_TYPE_CHANGE_MOBILE:
                $text = '无须注销账号，简单两步，换绑新手机号';
                break;
            case BaseResumeCancelLog::CANCEL_REASON_TYPE_WRONG_ACCOUNT:
                $text = '您可以直接隐藏简历，不展示给任何人，无须注销账号';
                break;
            case BaseResumeCancelLog::CANCEL_REASON_TYPE_NO_INVITATION:
                $text = '关闭职位订阅，将不再接收任何订阅类邀约邮件及通知，是否确认关闭？';
                break;
            default:
                $text = '';
                break;
        }

        return $text;
    }

}
