<?php

/**
 * 数据清理功能测试用例
 * 
 * 这个文件展示了如何测试executeDataClean方法的功能
 * 注意：这只是测试代码，不应该在生产环境中直接运行
 */

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeWork;
use common\service\memberCancel\ResumeCancelDataCleanService;

/**
 * 测试数据清理功能
 */
function testDataClean()
{
    echo "=== 开始测试数据清理功能 ===\n";
    
    // 1. 准备测试数据
    $testData = prepareTestData();
    if (!$testData) {
        echo "测试数据准备失败\n";
        return false;
    }
    
    $memberId = $testData['memberId'];
    $resumeId = $testData['resumeId'];
    
    echo "测试用户ID: {$memberId}, 简历ID: {$resumeId}\n";
    
    // 2. 记录清理前的数据状态
    $beforeClean = recordDataBeforeClean($memberId, $resumeId);
    echo "清理前数据状态:\n";
    printDataStatus($beforeClean);
    
    // 3. 执行数据清理
    try {
        $service = new ResumeCancelDataCleanService();
        $resume = BaseResume::findOne($resumeId);
        $member = BaseMember::findOne($memberId);
        
        // 调用私有方法进行测试（实际使用中这是在executeCancel中调用的）
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('executeDataClean');
        $method->setAccessible(true);
        $method->invoke($service, $resume, $member);
        
        echo "数据清理执行成功\n";
    } catch (Exception $e) {
        echo "数据清理执行失败: " . $e->getMessage() . "\n";
        return false;
    }
    
    // 4. 验证清理后的数据状态
    $afterClean = recordDataAfterClean($memberId, $resumeId);
    echo "清理后数据状态:\n";
    printDataStatus($afterClean);
    
    // 5. 验证清理效果
    $result = validateCleanResult($beforeClean, $afterClean);
    
    if ($result['success']) {
        echo "=== 数据清理测试通过 ===\n";
        return true;
    } else {
        echo "=== 数据清理测试失败 ===\n";
        echo "失败原因: " . implode(', ', $result['errors']) . "\n";
        return false;
    }
}

/**
 * 准备测试数据
 */
function prepareTestData()
{
    // 这里应该创建测试用户和简历数据
    // 实际实现中需要根据具体的测试环境来创建
    
    // 示例：假设我们有一个测试用户
    $memberId = 999999; // 测试用户ID
    $resumeId = 999999; // 测试简历ID
    
    // 验证测试数据是否存在
    $member = BaseMember::findOne($memberId);
    $resume = BaseResume::findOne($resumeId);
    
    if (!$member || !$resume) {
        echo "测试数据不存在，请先创建测试用户和简历\n";
        return false;
    }
    
    return [
        'memberId' => $memberId,
        'resumeId' => $resumeId,
    ];
}

/**
 * 记录清理前的数据状态
 */
function recordDataBeforeClean($memberId, $resumeId)
{
    $member = BaseMember::findOne($memberId);
    $resume = BaseResume::findOne($resumeId);
    
    $data = [
        'member' => [
            'mobile' => $member->mobile,
            'email' => $member->email,
            'username' => $member->username,
            'avatar' => $member->avatar,
        ],
        'resume' => [
            'name' => $resume->name,
            'english_name' => $resume->english_name,
            'advantage' => $resume->advantage,
            'residence' => $resume->residence,
            'id_card' => $resume->id_card,
        ],
        'sub_tables' => []
    ];
    
    // 记录子表数据状态
    $subTables = [
        'education' => BaseResumeEducation::find()->where(['member_id' => $memberId, 'status' => 1])->count(),
        'work' => BaseResumeWork::find()->where(['member_id' => $memberId, 'status' => 1])->count(),
        // 可以添加更多子表的统计
    ];
    
    $data['sub_tables'] = $subTables;
    
    return $data;
}

/**
 * 记录清理后的数据状态
 */
function recordDataAfterClean($memberId, $resumeId)
{
    // 重新查询数据
    $member = BaseMember::findOne($memberId);
    $resume = BaseResume::findOne($resumeId);
    
    $data = [
        'member' => [
            'mobile' => $member->mobile,
            'email' => $member->email,
            'username' => $member->username,
            'avatar' => $member->avatar,
        ],
        'resume' => [
            'name' => $resume->name,
            'english_name' => $resume->english_name,
            'advantage' => $resume->advantage,
            'residence' => $resume->residence,
            'id_card' => $resume->id_card,
        ],
        'sub_tables' => []
    ];
    
    // 记录子表数据状态
    $subTables = [
        'education' => BaseResumeEducation::find()->where(['member_id' => $memberId, 'status' => 1])->count(),
        'work' => BaseResumeWork::find()->where(['member_id' => $memberId, 'status' => 1])->count(),
        // 可以添加更多子表的统计
    ];
    
    $data['sub_tables'] = $subTables;
    
    return $data;
}

/**
 * 打印数据状态
 */
function printDataStatus($data)
{
    echo "会员信息:\n";
    echo "  手机号: " . ($data['member']['mobile'] ?: '(空)') . "\n";
    echo "  邮箱: " . ($data['member']['email'] ?: '(空)') . "\n";
    echo "  用户名: " . ($data['member']['username'] ?: '(空)') . "\n";
    echo "  头像: " . ($data['member']['avatar'] ?: '(空)') . "\n";
    
    echo "简历信息:\n";
    echo "  姓名: " . ($data['resume']['name'] ?: '(空)') . "\n";
    echo "  英文名: " . ($data['resume']['english_name'] ?: '(空)') . "\n";
    echo "  个人优势: " . (strlen($data['resume']['advantage']) > 20 ? substr($data['resume']['advantage'], 0, 20) . '...' : $data['resume']['advantage']) . "\n";
    echo "  居住地: " . ($data['resume']['residence'] ?: '(空)') . "\n";
    echo "  身份证: " . ($data['resume']['id_card'] ?: '(空)') . "\n";
    
    echo "子表数据:\n";
    foreach ($data['sub_tables'] as $table => $count) {
        echo "  {$table}: {$count} 条记录\n";
    }
    echo "\n";
}

/**
 * 验证清理结果
 */
function validateCleanResult($beforeClean, $afterClean)
{
    $errors = [];
    
    // 验证会员敏感信息是否清理
    if ($afterClean['member']['mobile'] !== '') {
        $errors[] = '手机号未清理';
    }
    if ($afterClean['member']['email'] !== '') {
        $errors[] = '邮箱未清理';
    }
    if ($afterClean['member']['username'] !== '') {
        $errors[] = '用户名未清理';
    }
    if ($afterClean['member']['avatar'] !== '') {
        $errors[] = '头像未清理';
    }
    
    // 验证简历敏感信息是否清理
    if ($afterClean['resume']['name'] !== '已注销用户') {
        $errors[] = '姓名未正确设置为"已注销用户"';
    }
    if ($afterClean['resume']['english_name'] !== '') {
        $errors[] = '英文名未清理';
    }
    if ($afterClean['resume']['advantage'] !== '') {
        $errors[] = '个人优势未清理';
    }
    if ($afterClean['resume']['residence'] !== '') {
        $errors[] = '居住地未清理';
    }
    if ($afterClean['resume']['id_card'] !== '') {
        $errors[] = '身份证未清理';
    }
    
    // 验证子表数据是否清理
    foreach ($afterClean['sub_tables'] as $table => $count) {
        if ($count > 0) {
            $errors[] = "子表 {$table} 仍有 {$count} 条有效记录";
        }
    }
    
    return [
        'success' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * 测试个人信息清理功能
 */
function testPersonalInfoClean()
{
    echo "=== 测试个人信息清理功能 ===\n";
    
    // 这里可以单独测试个人信息清理功能
    // 实际实现需要根据具体需求来编写
    
    echo "个人信息清理测试完成\n";
}

/**
 * 测试简历内容清理功能
 */
function testResumeContentClean()
{
    echo "=== 测试简历内容清理功能 ===\n";
    
    // 这里可以单独测试简历内容清理功能
    // 实际实现需要根据具体需求来编写
    
    echo "简历内容清理测试完成\n";
}

// 运行测试（注释掉，避免意外执行）
// testDataClean();
// testPersonalInfoClean();
// testResumeContentClean();

echo "测试用例文件加载完成。请根据需要调用相应的测试函数。\n";
echo "注意：请在测试环境中运行，不要在生产环境中执行！\n";
