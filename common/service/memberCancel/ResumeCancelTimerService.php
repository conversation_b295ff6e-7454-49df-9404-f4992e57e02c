<?php

namespace common\service\memberCancel;

use common\base\models\BaseMember;
use common\base\models\BaseResumeCancelLog;
use common\libs\SmsQueue;
use queue\Producer;
use Yii;

/**
 * 求职者注销定时任务服务
 * 处理注销相关的定时任务，包括提醒短信和自动注销
 */
class ResumeCancelTimerService
{
    /**
     * 发送注销提醒短信（冷静期第6天）
     * 每天执行一次，查找明天即将完成注销的用户
     *
     * @return array 执行结果
     */
    public function sendCancelReminders()
    {
        $successCount = 0;
        $failCount    = 0;
        $errors       = [];

        try {
            // 查找明天即将完成注销的申请（冷静期第6天）
            $tomorrow      = date('Y-m-d', strtotime('+1 day'));
            $tomorrowStart = $tomorrow . ' 00:00:00';
            $tomorrowEnd   = $tomorrow . ' 23:59:59';

            $cancelLogs = BaseResumeCancelLog::find()
                ->where([
                    'status' => BaseResumeCancelLog::STATUS_APPLYING,
                ])
                ->andWhere([
                    'between',
                    'cooldown_end_time',
                    $tomorrowStart,
                    $tomorrowEnd,
                ])
                ->all();

            Yii::info("找到 " . count($cancelLogs) . " 个需要发送提醒的注销申请", 'resume-cancel-timer');

            foreach ($cancelLogs as $cancelLog) {
                try {
                    // 获取用户信息
                    $member = BaseMember::findOne($cancelLog->member_id);
                    if (!$member) {
                        $errors[] = "用户不存在，cancelLogId: {$cancelLog->id}";
                        $failCount++;
                        continue;
                    }

                    // 检查用户状态是否仍为注销中
                    if ($member->cancel_status != BaseMember::CANCEL_STATUS_CANCELING) {
                        Yii::info("用户状态已变更，跳过提醒，memberId: {$member->id}, cancelStatus: {$member->cancel_status}",
                            'resume-cancel-timer');
                        continue;
                    }

                    // 发送提醒短信
                    Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_REMINDER,
                        $member->mobile_code);

                    // 更新短信发送状态
                    $this->updateSmsStatus($cancelLog, BaseResumeCancelLog::SMS_STATUS_REMINDER_SENT);

                    $successCount++;
                    Yii::info("注销提醒短信发送成功，memberId: {$member->id}, mobile: {$member->mobile}",
                        'resume-cancel-timer');
                } catch (\Exception $e) {
                    $errors[] = "发送提醒失败，cancelLogId: {$cancelLog->id}, 错误: " . $e->getMessage();
                    $failCount++;
                    Yii::error("发送注销提醒失败：" . $e->getMessage(), 'resume-cancel-timer');
                }
            }

            return [
                'success'      => true,
                'successCount' => $successCount,
                'failCount'    => $failCount,
                'totalCount'   => count($cancelLogs),
                'errors'       => $errors,
                'message'      => "注销提醒发送完成，成功: {$successCount}, 失败: {$failCount}",
            ];
        } catch (\Exception $e) {
            Yii::error("发送注销提醒任务执行失败：" . $e->getMessage(), 'resume-cancel-timer');

            return [
                'success'      => false,
                'message'      => '任务执行失败：' . $e->getMessage(),
                'successCount' => $successCount,
                'failCount'    => $failCount,
                'errors'       => $errors,
            ];
        }
    }

    /**
     * 执行自动注销（冷静期结束）
     * 每天执行一次，查找今天冷静期结束的用户
     *
     * @return array 执行结果
     */
    public function executeAutoCancel()
    {
        $successCount = 0;
        $failCount    = 0;
        $errors       = [];

        try {
            // 查找今天冷静期结束的申请
            $today      = date('Y-m-d');
            $todayStart = $today . ' 00:00:00';
            $todayEnd   = $today . ' 23:59:59';

            $cancelLogs = BaseResumeCancelLog::find()
                ->where([
                    'status' => BaseResumeCancelLog::STATUS_APPLYING,
                ])
                ->andWhere([
                    'between',
                    'cooldown_end_time',
                    $todayStart,
                    $todayEnd,
                ])
                ->all();

            Yii::info("找到 " . count($cancelLogs) . " 个需要自动注销的申请", 'resume-cancel-timer');

            foreach ($cancelLogs as $cancelLog) {
                try {
                    // 检查冷静期是否真的已结束
                    if (strtotime($cancelLog->cooldown_end_time) > time()) {
                        continue; // 冷静期未结束，跳过
                    }

                    // 执行注销操作
                    $dataCleanService = new ResumeCancelDataCleanService();
                    $result           = $dataCleanService->executeCancel($cancelLog->id, 0); // 0表示系统自动操作

                    $successCount++;
                    Yii::info("自动注销执行成功，cancelLogId: {$cancelLog->id}, memberId: {$cancelLog->member_id}",
                        'resume-cancel-timer');
                } catch (\Exception $e) {
                    $errors[] = "自动注销失败，cancelLogId: {$cancelLog->id}, 错误: " . $e->getMessage();
                    $failCount++;
                    Yii::error("自动注销执行失败：" . $e->getMessage(), 'resume-cancel-timer');
                }
            }

            return [
                'success'      => true,
                'successCount' => $successCount,
                'failCount'    => $failCount,
                'totalCount'   => count($cancelLogs),
                'errors'       => $errors,
                'message'      => "自动注销执行完成，成功: {$successCount}, 失败: {$failCount}",
            ];
        } catch (\Exception $e) {
            Yii::error("自动注销任务执行失败：" . $e->getMessage(), 'resume-cancel-timer');

            return [
                'success'      => false,
                'message'      => '任务执行失败：' . $e->getMessage(),
                'successCount' => $successCount,
                'failCount'    => $failCount,
                'errors'       => $errors,
            ];
        }
    }

    /**
     * 更新短信发送状态
     * @param BaseResumeCancelLog $cancelLog
     * @param int                 $smsStatus
     * @return bool
     */
    private function updateSmsStatus($cancelLog, $smsStatus)
    {
        try {
            $cancelLog->sms_status  = $smsStatus;
            $cancelLog->update_time = date('Y-m-d H:i:s');

            if (!$cancelLog->save()) {
                throw new \Exception('更新短信状态失败：' . $cancelLog->getFirstErrorsMessage());
            }

            return true;
        } catch (\Exception $e) {
            Yii::error("更新短信状态失败：" . $e->getMessage(), 'resume-cancel-timer');

            return false;
        }
    }

    /**
     * 清理过期的限制记录（180天后）
     * 每天执行一次，清理过期的注销限制记录
     *
     * @return array 执行结果
     */
    public function cleanExpiredRestrictions()
    {
        try {
            // 调用 ResumeCancelRestrictionService 的清理方法
            $restrictionService = new ResumeCancelRestrictionService();
            $result = $restrictionService->cleanExpiredRestrictions();

            if ($result['success']) {
                Yii::info("清理过期限制记录成功：" . $result['message'], 'resume-cancel-timer');
            } else {
                Yii::error("清理过期限制记录失败：" . $result['message'], 'resume-cancel-timer');
            }

            return $result;
        } catch (\Exception $e) {
            Yii::error("清理过期限制记录失败：" . $e->getMessage(), 'resume-cancel-timer');

            return [
                'success' => false,
                'message' => '清理失败：' . $e->getMessage(),
            ];
        }
    }
}
