<?php

namespace common\service\memberCancel;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelSnapshot;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeWork;
use common\base\models\BaseResumeProject;
use common\base\models\BaseResumeSkill;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseMemberBind;
use common\base\models\BaseResumeSetting;
use Yii;

/**
 * 求职者数据快照服务
 */
class ResumeCancelSnapshotService
{
    /**
     * 创建数据快照
     * @param int $resumeId    简历ID
     * @param int $cancelLogId 注销日志ID
     * @return int 快照记录ID
     * @throws \Exception
     */
    public function createSnapshot($resumeId, $cancelLogId)
    {
        $resume = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        $memberId = $resume->member_id;
        $member   = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        try {
            // 使用现有的 checkResumeInfo 方法获取完整的简历数据
            $completeResumeData = BaseResume::checkResumeInfo($memberId);

            // 创建快照记录
            $snapshot                = new BaseResumeCancelSnapshot();
            $snapshot->member_id     = $memberId;
            $snapshot->resume_id     = $resumeId;
            $snapshot->cancel_log_id = $cancelLogId;

            // 设置关键联系信息
            $snapshot->mobile      = $member->mobile ?? '';
            $snapshot->mobile_code = $member->mobile_code ?? '86';
            $snapshot->email       = $member->email ?? '';
            $snapshot->username    = $member->username ?? '';
            $snapshot->name        = $resume->name ?? '';

            // 设置JSON数据（使用完整的简历数据）
            $snapshot->setResumeData($completeResumeData);

            if (!$snapshot->save()) {
                throw new \Exception('保存数据快照失败：' . $snapshot->getFirstErrorsMessage());
            }

            return $snapshot->id;
        } catch (\Exception $e) {
            Yii::error('创建数据快照失败：' . $e->getMessage(), 'resume-cancel');
            throw $e;
        }
    }

    /**
     * 获取快照数据
     * @param int $cancelLogId 注销日志ID
     * @return array 包含各模块数据的数组
     * @throws \Exception
     */
    public function getSnapshotData($cancelLogId)
    {
        $snapshot = BaseResumeCancelSnapshot::findByCancelLogId($cancelLogId);
        if (!$snapshot) {
            throw new \Exception('数据快照不存在');
        }

        return [
            'memberData'  => $snapshot->getMemberData(),
            'resumeData'  => $snapshot->getResumeData(),
            'relatedData' => $snapshot->getRelatedData(),
            'settingData' => $snapshot->getSettingData(),
            'summary'     => $snapshot->getSummary(),
        ];
    }
}
