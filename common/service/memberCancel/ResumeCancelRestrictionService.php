<?php

namespace common\service\memberCancel;

use common\base\models\BaseMember;
use common\base\models\BaseResumeCancelRestriction;
use Yii;

/**
 * 求职者注销限制服务
 * 处理180天重新注册限制
 */
class ResumeCancelRestrictionService
{
    /**
     * 添加180天限制记录
     * @param string $mobile      手机号
     * @param string $email       邮箱
     * @param string $mobileCode  手机号区号
     * @param int    $cancelLogId 注销日志ID
     * @return int 限制记录ID
     * @throws \Exception
     */
    public function addRestriction($mobile, $email, $mobileCode, $cancelLogId)
    {
        if (empty($mobile) && empty($email)) {
            throw new \Exception('手机号和邮箱不能同时为空');
        }

        if (empty($cancelLogId)) {
            throw new \Exception('注销日志ID不能为空');
        }

        try {
            // 计算限制结束时间（注销时间+180天）
            $restrictionEndTime = date('Y-m-d H:i:s', strtotime('+180 days'));

            // 创建限制记录
            $restriction                       = new BaseResumeCancelRestriction();
            $restriction->mobile               = $mobile ?: '';
            $restriction->mobile_code          = $mobileCode ?: '';
            $restriction->email                = $email ?: '';
            $restriction->cancel_log_id        = $cancelLogId;
            $restriction->restriction_end_time = $restrictionEndTime;

            if (!$restriction->save()) {
                throw new \Exception('保存限制记录失败：' . $restriction->getFirstErrorsMessage());
            }

            Yii::info("添加180天限制记录成功，限制ID: {$restriction->id}, 手机号: {$mobile}, 邮箱: {$email}, 限制结束时间: {$restrictionEndTime}",
                'resume-cancel');

            return $restriction->id;
        } catch (\Exception $e) {
            Yii::error("添加180天限制记录失败：" . $e->getMessage(), 'resume-cancel');
            throw new \Exception('添加限制记录失败：' . $e->getMessage());
        }
    }

    /**
     * 检查限制状态
     * @param string $mobile     手机号
     * @param string $email      邮箱
     * @param string $mobileCode 手机号区号
     * @return array 检查结果
     * @throws \Exception
     */
    public function checkRestriction($mobile = '', $email = '', $mobileCode = '86')
    {
        if (empty($mobile) && empty($email)) {
            return [
                'isRestricted' => false,
                'message'      => '',
                'endTime'      => null,
            ];
        }

        try {
            $query = BaseResumeCancelRestriction::find()
                ->andWhere([
                    '>=',
                    'restriction_end_time',
                    date('Y-m-d H:i:s'),
                ]);

            // 构建查询条件
            $conditions = ['or'];

            if ($mobile) {
                $conditions[] = [
                    'and',
                    ['mobile' => $mobile],
                    ['mobile_code' => $mobileCode],
                ];
            }

            if ($email) {
                $conditions[] = ['email' => $email];
            }

            $query->andWhere($conditions);

            $restriction = $query->one();

            if ($restriction) {
                $message = '当前手机号近半年内有账号注销记录，暂无法注册，请更换其他手机号尝试';
                if ($email && $restriction->email == $email) {
                    $message = '当前邮箱近半年内有账号注销记录，暂无法注册，请更换其他邮箱尝试';
                }

                return [
                    'isRestricted'  => true,
                    'message'       => $message,
                    'endTime'       => $restriction->restriction_end_time,
                    'restrictionId' => $restriction->id,
                ];
            }

            return [
                'isRestricted' => false,
                'message'      => '',
                'endTime'      => null,
            ];
        } catch (\Exception $e) {
            Yii::error("检查限制状态失败：" . $e->getMessage(), 'resume-cancel');
            throw new \Exception('检查限制状态失败：' . $e->getMessage());
        }
    }

    /**
     * 清理过期的限制记录
     * @return array 清理结果
     */
    public function cleanExpiredRestrictions()
    {
        try {
            $currentTime = date('Y-m-d H:i:s');

            // 查找过期的限制记录
            $expiredCount = BaseResumeCancelRestriction::find()
                ->where([
                    '<',
                    'restriction_end_time',
                    $currentTime,
                ])
                ->count();

            if ($expiredCount > 0) {
                // 删除过期记录
                $deletedCount = BaseResumeCancelRestriction::deleteAll([
                    '<',
                    'restriction_end_time',
                    $currentTime,
                ]);

                Yii::info("清理过期限制记录成功，删除记录数: {$deletedCount}", 'resume-cancel');

                return [
                    'success'      => true,
                    'deletedCount' => $deletedCount,
                    'message'      => "成功清理 {$deletedCount} 条过期限制记录",
                ];
            } else {
                return [
                    'success'      => true,
                    'deletedCount' => 0,
                    'message'      => '没有过期的限制记录需要清理',
                ];
            }
        } catch (\Exception $e) {
            Yii::error("清理过期限制记录失败：" . $e->getMessage(), 'resume-cancel');

            return [
                'success'      => false,
                'deletedCount' => 0,
                'message'      => '清理失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 根据注销日志ID获取限制记录
     * @param int $cancelLogId 注销日志ID
     * @return BaseResumeCancelRestriction|null
     */
    public function getRestrictionByCancelLogId($cancelLogId)
    {
        return BaseResumeCancelRestriction::findOne(['cancel_log_id' => $cancelLogId]);
    }

    /**
     * 获取用户的所有限制记录
     * @param string $mobile     手机号
     * @param string $email      邮箱
     * @param string $mobileCode 手机号区号
     * @return array
     */
    public function getUserRestrictions($mobile = '', $email = '', $mobileCode = '86')
    {
        if (empty($mobile) && empty($email)) {
            return [];
        }

        $query = BaseResumeCancelRestriction::find();

        // 构建查询条件
        $conditions = ['or'];

        if ($mobile) {
            $conditions[] = [
                'and',
                ['mobile' => $mobile],
                ['mobile_code' => $mobileCode],
            ];
        }

        if ($email) {
            $conditions[] = ['email' => $email];
        }

        $query->andWhere($conditions);
        $query->orderBy('add_time DESC');

        return $query->asArray()
            ->all();
    }

    /**
     * 删除限制记录（仅用于特殊情况，如数据修正）
     * @param int $restrictionId 限制记录ID
     * @param int $adminId       操作管理员ID
     * @return bool
     * @throws \Exception
     */
    public function removeRestriction($restrictionId, $adminId = 0)
    {
        $restriction = BaseResumeCancelRestriction::findOne($restrictionId);
        if (!$restriction) {
            throw new \Exception('限制记录不存在');
        }

        try {
            $mobile = $restriction->mobile;
            $email  = $restriction->email;

            if ($restriction->delete()) {
                Yii::info("删除限制记录成功，限制ID: {$restrictionId}, 手机号: {$mobile}, 邮箱: {$email}, 操作管理员: {$adminId}",
                    'resume-cancel');

                return true;
            } else {
                throw new \Exception('删除限制记录失败');
            }
        } catch (\Exception $e) {
            Yii::error("删除限制记录失败：" . $e->getMessage(), 'resume-cancel');
            throw new \Exception('删除限制记录失败：' . $e->getMessage());
        }
    }
}
