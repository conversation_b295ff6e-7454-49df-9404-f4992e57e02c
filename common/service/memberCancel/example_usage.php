<?php

/**
 * ResumeCancelRestrictionService 使用示例
 * 
 * 这个文件展示了如何使用新创建的限制服务
 * 注意：这只是示例代码，不应该在生产环境中直接运行
 */

use common\service\memberCancel\ResumeCancelRestrictionService;

// 示例1：添加180天限制记录
function addRestrictionExample()
{
    $restrictionService = new ResumeCancelRestrictionService();
    
    try {
        $restrictionId = $restrictionService->addRestriction(
            '13800138000',  // 手机号
            '<EMAIL>',  // 邮箱
            '86',  // 手机号区号
            123  // 注销日志ID
        );
        
        echo "添加限制记录成功，ID: {$restrictionId}\n";
    } catch (Exception $e) {
        echo "添加限制记录失败：" . $e->getMessage() . "\n";
    }
}

// 示例2：检查限制状态
function checkRestrictionExample()
{
    $restrictionService = new ResumeCancelRestrictionService();
    
    try {
        $result = $restrictionService->checkRestriction(
            '13800138000',  // 手机号
            '<EMAIL>',  // 邮箱
            '86'  // 手机号区号
        );
        
        if ($result['isRestricted']) {
            echo "用户受限：" . $result['message'] . "\n";
            echo "限制结束时间：" . $result['endTime'] . "\n";
        } else {
            echo "用户未受限，可以注册\n";
        }
    } catch (Exception $e) {
        echo "检查限制状态失败：" . $e->getMessage() . "\n";
    }
}

// 示例3：清理过期限制记录
function cleanExpiredRestrictionsExample()
{
    $restrictionService = new ResumeCancelRestrictionService();
    
    $result = $restrictionService->cleanExpiredRestrictions();
    
    if ($result['success']) {
        echo "清理成功：" . $result['message'] . "\n";
        echo "删除记录数：" . $result['deletedCount'] . "\n";
    } else {
        echo "清理失败：" . $result['message'] . "\n";
    }
}

// 示例4：获取用户的所有限制记录
function getUserRestrictionsExample()
{
    $restrictionService = new ResumeCancelRestrictionService();
    
    $restrictions = $restrictionService->getUserRestrictions(
        '13800138000',  // 手机号
        '<EMAIL>',  // 邮箱
        '86'  // 手机号区号
    );
    
    echo "用户限制记录数：" . count($restrictions) . "\n";
    foreach ($restrictions as $restriction) {
        echo "限制ID: {$restriction['id']}, 结束时间: {$restriction['restriction_end_time']}\n";
    }
}

// 示例5：在注销流程中的使用（ResumeCancelDataCleanService中的实际使用）
function usageInCancelProcessExample($member, $cancelLogId)
{
    try {
        // 这是在ResumeCancelDataCleanService::addRestrictionRecord方法中的实际使用
        $restrictionService = new ResumeCancelRestrictionService();
        $restrictionId = $restrictionService->addRestriction(
            $member->mobile,
            $member->email,
            $member->mobile_code,
            $cancelLogId
        );

        echo "添加180天限制记录成功，用户ID: {$member->id}, 限制记录ID: {$restrictionId}\n";
    } catch (Exception $e) {
        echo "添加180天限制记录失败，用户ID: {$member->id}, 错误: " . $e->getMessage() . "\n";
        throw new Exception('添加180天限制记录失败：' . $e->getMessage());
    }
}

// 示例6：在定时任务中的使用（ResumeCancelTimerService中的实际使用）
function usageInTimerServiceExample()
{
    try {
        // 这是在ResumeCancelTimerService::cleanExpiredRestrictions方法中的实际使用
        $restrictionService = new ResumeCancelRestrictionService();
        $result = $restrictionService->cleanExpiredRestrictions();

        if ($result['success']) {
            echo "清理过期限制记录成功：" . $result['message'] . "\n";
        } else {
            echo "清理过期限制记录失败：" . $result['message'] . "\n";
        }

        return $result;
    } catch (Exception $e) {
        echo "清理过期限制记录失败：" . $e->getMessage() . "\n";

        return [
            'success' => false,
            'message' => '清理失败：' . $e->getMessage(),
        ];
    }
}

// 注意事项：
// 1. 限制记录会在用户注销完成后自动创建
// 2. 限制检查会在用户注册时自动进行（在BaseMemberLoginForm中）
// 3. 过期记录清理需要通过定时任务定期执行
// 4. 所有操作都有完整的日志记录
// 5. 错误处理确保系统稳定性
