<?php

namespace common\service\memberCancel;

use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeWxBind;
use common\libs\SmsQueue;
use common\models\MemberBind;
use common\models\ResumeLibrary;
use common\service\jobSubscribe\JobSubscribeApplication;
use common\service\resume\ResumeLibraryService;
use queue\Producer;
use Yii;

/**
 * 求职者注销执行服务
 * 专门处理冷静期结束后的实际注销操作
 */
class ResumeCancelDataCleanService
{
    /**
     * 执行注销操作（唯一对外方法）
     * 由定时任务或运营后台调用
     *
     * @param int $cancelLogId 注销日志ID
     * @param int $adminId     管理员ID（人工操作时传入，系统操作时为0）
     * @return array 执行结果
     * @throws \Exception
     */
    public function executeCancel($cancelLogId, $adminId = 0)
    {
        // 1. 验证注销申请状态
        $cancelLog = $this->validateCancelLog($cancelLogId);
        $resume    = $this->getResumeInfo($cancelLog->resume_id);
        $member    = $this->getMemberInfo($cancelLog->member_id);

        // 2. 判断操作类型
        $isManualOperation = $adminId > 0;

        try {
            // 3. 创建数据快照（用于备份和恢复）
            $snapshotId = $this->createDataSnapshot($cancelLog);

            // 4. 执行数据清理（清除敏感信息）
            $this->executeDataClean($resume, $member);

            // 5. 更新用户状态为已注销
            $this->updateMemberStatus($member);

            // 6. 更新注销日志状态
            $this->updateCancelLogStatus($cancelLog, $adminId);

            // 7. 添加180天限制记录
            $this->addRestrictionRecord($member, $cancelLog->id);

            // 8. 发送注销成功通知（仅系统自动执行时）
            if (!$isManualOperation) {
                $this->sendCancelSuccessNotification($member);
                // 更新短信发送状态
                $cancelLog->sms_status = BaseResumeCancelLog::SMS_STATUS_COMPLETE_SENT;
                $cancelLog->save();
            }

            // 9. 记录操作日志
            $this->logOperation($member->id, 'success', '', $adminId);

            return [
                'success'      => true,
                'cancelLogId'  => $cancelLogId,
                'snapshotId'   => $snapshotId,
                'completeTime' => date('Y-m-d H:i:s'),
                'adminId'      => $adminId,
            ];
        } catch (\Exception $e) {
            // 记录失败日志
            $this->logOperation($member->id, 'failed', $e->getMessage(), $adminId);
            throw $e;
        }
    }

    /**
     * 验证注销申请状态
     * @param int $cancelLogId
     * @return BaseResumeCancelLog
     * @throws \Exception
     */
    private function validateCancelLog($cancelLogId)
    {
        $cancelLog = BaseResumeCancelLog::findOne($cancelLogId);
        if (!$cancelLog) {
            throw new \Exception('注销申请记录不存在');
        }

        if ($cancelLog->status != BaseResumeCancelLog::STATUS_APPLYING) {
            throw new \Exception('注销申请状态异常，无法执行注销');
        }

        // TODO: 验证冷静期是否已结束
        if (strtotime($cancelLog->cooldown_end_time) > time()) {
            throw new \Exception('冷静期尚未结束，无法执行注销');
        }

        return $cancelLog;
    }

    /**
     * 获取简历信息
     * @param int $resumeId
     * @return BaseResume
     * @throws \Exception
     */
    private function getResumeInfo($resumeId)
    {
        $resume = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        return $resume;
    }

    /**
     * 获取会员信息
     * @param int $memberId
     * @return BaseMember
     * @throws \Exception
     */
    private function getMemberInfo($memberId)
    {
        $member = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        return $member;
    }

    /**
     * 创建数据快照
     * @param BaseResumeCancelLog $cancelLog
     * @return int 快照ID
     * @throws \Exception
     */
    private function createDataSnapshot($cancelLog)
    {
        // TODO: 调用快照服务创建数据快照
        return (new ResumeCancelSnapshotService())->createSnapshot($cancelLog->resume_id, $cancelLog->id);
    }

    /**
     * 执行预注销操作（在申请注销时立即执行）
     * @param BaseResume          $resume
     * @param BaseMember          $member
     * @param BaseResumeCancelLog $cancelLog
     * @throws \Exception
     */
    public function executePreCancelOperations($resume, $member, $cancelLog)
    {
        try {
            // 0. 保存用户原始消息通知设置（在修改前保存）
            $this->saveOriginalSettings($resume->id, $member->id, $cancelLog);

            // 1. 取消与小程序的绑定关系
            $this->unbindMiniProgram($resume->id);

            // 2. 退出人才库
            $this->exitResumeLibrary($resume->id);

            // 3. 关闭职位订阅
            $this->cancelJobSubscription($resume->id);

            // 4. 关闭新消息通知
            $this->cancelMessageNotification($resume->id);

            // 5. 更新用户注销状态为"注销中"
            $this->updateMemberCancelStatus($member->id, BaseMember::CANCEL_STATUS_CANCELING);
        } catch (\Exception $e) {
            throw new \Exception('预注销操作失败：' . $e->getMessage());
        }
    }

    /**
     * 保存用户原始消息通知设置
     * @param int                 $resumeId
     * @param int                 $memberId
     * @param BaseResumeCancelLog $cancelLog
     * @return array
     * @throws \Exception
     */
    private function saveOriginalSettings($resumeId, $memberId, $cancelLog)
    {
        try {
            // 获取用户原始消息通知设置
            $resumeSettings = $this->getResumeSettings($resumeId);

            // 保存到现有的 resume_setting 字段
            $cancelLog->resume_setting = json_encode($resumeSettings, JSON_UNESCAPED_UNICODE);
            if (!$cancelLog->save()) {
                throw new \Exception('保存原始设置失败：' . $cancelLog->getFirstErrorsMessage());
            }

            Yii::info("保存用户原始消息通知设置成功，resumeId: {$resumeId}, memberId: {$memberId}", 'resume-cancel');

            return $resumeSettings;
        } catch (\Exception $e) {
            throw new \Exception('保存原始设置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取简历设置信息
     * @param int $resumeId
     * @return array
     */
    private function getResumeSettings($resumeId)
    {
        $setting = BaseResumeSetting::findOne($resumeId);
        if (!$setting) {
            return [];
        }

        return [
            'is_job_feedback'    => $setting->is_job_feedback,
            'is_system_message'  => $setting->is_system_message,
            'is_todo_notice'     => $setting->is_todo_notice,
            'is_job_invite'      => $setting->is_job_invite,
            'is_company_view_me' => $setting->is_company_view_me,
        ];
    }

    /**
     * 执行数据清理
     * @param BaseResume $resume
     * @param BaseMember $member
     * @throws \Exception
     */
    private function executeDataClean($resume, $member)
    {
        // TODO: 实现数据清理
        // 1. 清理个人敏感信息（姓名、手机、邮箱等）
        // 2. 清理简历内容
        // 3. 保留必要的业务关联数据

        Yii::info("数据清理完成，resumeId: {$resume->id}", 'resume-cancel');
    }

    /**
     * 更新用户状态为已注销
     * @param BaseMember $member
     * @throws \Exception
     */
    private function updateMemberStatus($member)
    {
        // 更新注销状态为已注销
        $this->updateMemberCancelStatus($member->id, BaseMember::CANCEL_STATUS_CANCELED);

        // 保持原有的status字段逻辑（如果需要的话）
        // $member->status = BaseMember::STATUS_RESUME_CANCELED;
        // if (!$member->save()) {
        //     throw new \Exception('更新用户状态失败：' . $member->getFirstErrorsMessage());
        // }
    }

    /**
     * 更新注销日志状态
     * @param BaseResumeCancelLog $cancelLog
     * @param int                 $adminId
     * @throws \Exception
     */
    private function updateCancelLogStatus($cancelLog, $adminId)
    {
        $cancelLog->status        = BaseResumeCancelLog::STATUS_COMPLETED;
        $cancelLog->complete_time = date('Y-m-d H:i:s');
        $cancelLog->admin_id      = $adminId; // 记录操作的管理员ID

        if (!$cancelLog->save()) {
            throw new \Exception('更新注销日志失败：' . $cancelLog->getFirstErrorsMessage());
        }
    }

    /**
     * 添加180天限制记录
     * @param BaseMember $member
     * @param int        $cancelLogId
     * @throws \Exception
     */
    private function addRestrictionRecord($member, $cancelLogId)
    {
        // TODO: 调用限制服务添加180天限制
        $restrictionService = new ResumeCancelRestrictionService();
        $restrictionService->addRestriction($member->mobile, $member->email, $member->mobile_code, $cancelLogId);
    }

    /**
     * 发送注销完成通知
     * @param BaseMember $member
     */
    private function sendCancelSuccessNotification($member)
    {
        try {
            Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_COMPLETE,
                $member->mobile_code);
        } catch (\Exception $e) {
            // 短信发送失败不影响注销流程，只记录日志
            Yii::error('发送注销完成短信失败：' . $e->getMessage(), 'resume-cancel');
        }
    }

    /**
     * 记录操作日志
     * @param int    $memberId
     * @param string $result
     * @param string $errorMessage
     * @param int    $adminId
     */
    private function logOperation($memberId, $result, $errorMessage = '', $adminId = 0)
    {
        $operatorType = $adminId > 0 ? 'manual' : 'system';
        $logMessage   = "用户注销执行{$result} - 用户ID: {$memberId}, 操作类型: {$operatorType}";

        if ($adminId > 0) {
            $logMessage .= ", 管理员ID: {$adminId}";
        }

        if ($errorMessage) {
            $logMessage .= ", 错误: {$errorMessage}";
        }

        if ($result === 'success') {
            Yii::info($logMessage, 'resume-cancel');
        } else {
            Yii::error($logMessage, 'resume-cancel');
        }
    }

    // =====================================================
    // 预处理操作相关方法
    // =====================================================

    /**
     * 取消与小程序的绑定关系
     * @param int $memberId 会员ID
     * @return bool
     * @throws \Exception
     */
    private function unbindMiniProgram($resumeId)
    {
        BaseResumeWxBind::cancelWxBind($resumeId);
    }

    /**
     * 退出人才库
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function exitResumeLibrary($resumeId)
    {
        $resume                    = BaseResume::findOne($resumeId);
        $resume->is_resume_library = BaseResume::IS_RESUME_LIBRARY_NO;

        $resume->save();
        // 这里是否考虑调用updateResume??
    }

    /**
     * 关闭职位订阅
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function cancelJobSubscription($resumeId)
    {
        $service = new JobSubscribeApplication();

        $service->cancel($resumeId);
    }

    /**
     * 关闭新消息通知
     * @param int $resumeId 会员ID
     * @return bool
     * @throws \Exception
     */
    private function cancelMessageNotification($resumeId)
    {
        $setting = BaseResumeSetting::findOne($resumeId);
        if (!$setting) {
            throw new \Exception('简历设置不存在');
        }

        $setting->is_job_feedback    = BaseResumeSetting::IS_JOB_FEEDBACK_NO;
        $setting->is_system_message  = BaseResumeSetting::IS_SYSTEM_MESSAGE_NO;
        $setting->is_todo_notice     = BaseResumeSetting::IS_TODO_NOTICE_NO;
        $setting->is_job_invite      = BaseResumeSetting::IS_JOB_INVITE_NO;
        $setting->is_company_view_me = BaseResumeSetting::IS_COMPANY_VIEW_ME_NO;
        $setting->update_time        = date('Y-m-d H:i:s');

        if (!$setting->save()) {
            throw new \Exception('关闭消息通知失败：' . $setting->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 更新用户注销状态
     * @param int $memberId     会员ID
     * @param int $cancelStatus 注销状态
     * @return bool
     * @throws \Exception
     */
    private function updateMemberCancelStatus($memberId, $cancelStatus)
    {
        try {
            $member = BaseMember::findOne($memberId);
            if (!$member) {
                throw new \Exception('用户不存在');
            }

            $member->cancel_status = $cancelStatus;
            $member->update_time   = date('Y-m-d H:i:s');

            if (!$member->save()) {
                throw new \Exception('更新用户注销状态失败：' . $member->getFirstErrorsMessage());
            }

            Yii::info("更新用户注销状态成功，memberId: {$memberId}, cancelStatus: {$cancelStatus}", 'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('更新用户注销状态失败：' . $e->getMessage());
        }
    }

    // =====================================================
    // 撤回注销时的恢复逻辑
    // =====================================================

    /**
     * 撤回注销时恢复预处理操作
     * @param BaseResumeCancelLog $cancelLog
     * @return bool
     * @throws \Exception
     */
    public function restorePreCancelOperations(BaseResumeCancelLog $cancelLog)
    {
        try {
            // 1. 恢复用户原始消息通知设置
            $this->restoreOriginalSettings($cancelLog);

            // 2. 恢复用户注销状态为正常
            $this->updateMemberCancelStatus($cancelLog->member_id, BaseMember::CANCEL_STATUS_NORMAL);

            // 3. 重新评估人才库资格
            $this->reevaluateResumeLibrary($cancelLog->resume_id);

            Yii::info("恢复预处理操作成功，memberId: {$cancelLog->member_id}, resumeId: {$cancelLog->resume_id}",
                'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('恢复预处理操作失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复用户原始消息通知设置
     * @param BaseResumeCancelLog $cancelLog
     * @return bool
     * @throws \Exception
     */
    private function restoreOriginalSettings($cancelLog)
    {
        try {
            // 从 resume_setting 字段读取原始设置
            $originalSettings = json_decode($cancelLog->resume_setting, true);

            if (!$originalSettings) {
                throw new \Exception('原始消息通知设置数据不存在或格式错误');
            }

            // 恢复消息通知设置
            $this->restoreResumeSettings($cancelLog->resume_id, $originalSettings);

            Yii::info("恢复用户原始消息通知设置成功，resumeId: {$cancelLog->resume_id}, memberId: {$cancelLog->member_id}", 'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('恢复原始设置失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复简历设置
     * @param int $resumeId
     * @param array $settings
     * @return bool
     * @throws \Exception
     */
    private function restoreResumeSettings($resumeId, $settings)
    {
        if (empty($settings)) {
            return true;
        }

        $setting = BaseResumeSetting::findOne($resumeId);
        if (!$setting) {
            throw new \Exception('简历设置不存在');
        }

        $setting->is_job_feedback = $settings['is_job_feedback'] ?? BaseResumeSetting::IS_JOB_FEEDBACK_YES;
        $setting->is_system_message = $settings['is_system_message'] ?? BaseResumeSetting::IS_SYSTEM_MESSAGE_YES;
        $setting->is_todo_notice = $settings['is_todo_notice'] ?? BaseResumeSetting::IS_TODO_NOTICE_YES;
        $setting->is_job_invite = $settings['is_job_invite'] ?? BaseResumeSetting::IS_JOB_INVITE_YES;
        $setting->is_company_view_me = $settings['is_company_view_me'] ?? BaseResumeSetting::IS_COMPANY_VIEW_ME_YES;
        $setting->update_time = date('Y-m-d H:i:s');

        if (!$setting->save()) {
            throw new \Exception('恢复简历设置失败：' . $setting->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 重新评估是否加入人才库
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function reevaluateResumeLibrary($resumeId)
    {
        try {
            // 调用人才库服务重新评估
            $service = new ResumeLibraryService();
            $service->setResumeId($resumeId)
                ->setOparetion(ResumeLibraryService::OPERATION_UPDATE_FILTER_RESUME_LIBRARY)
                ->run();

            Yii::info("重新评估人才库状态成功，resumeId: {$resumeId}", 'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('重新评估人才库状态失败：' . $e->getMessage());
        }
    }
}
