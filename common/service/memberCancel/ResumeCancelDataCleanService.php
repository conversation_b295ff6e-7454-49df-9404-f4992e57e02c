<?php

namespace common\service\memberCancel;

use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeWxBind;
use common\libs\SmsQueue;
use common\models\MemberBind;
use common\models\ResumeLibrary;
use common\service\jobSubscribe\JobSubscribeApplication;
use common\service\resume\ResumeLibraryService;
use queue\Producer;
use Yii;

/**
 * 求职者注销执行服务
 * 专门处理冷静期结束后的实际注销操作
 */
class ResumeCancelDataCleanService
{
    /**
     * 执行注销操作（唯一对外方法）
     * 由定时任务或运营后台调用
     *
     * @param int $cancelLogId 注销日志ID
     * @param int $adminId     管理员ID（人工操作时传入，系统操作时为0）
     * @return array 执行结果
     * @throws \Exception
     */
    public function executeCancel($cancelLogId, $adminId = 0)
    {
        // 1. 验证注销申请状态
        $cancelLog = $this->validateCancelLog($cancelLogId);
        $resume    = $this->getResumeInfo($cancelLog->resume_id);
        $member    = $this->getMemberInfo($cancelLog->member_id);

        // 2. 判断操作类型
        $isManualOperation = $adminId > 0;

        try {
            // 3. 创建数据快照（用于备份和恢复）
            $snapshotId = $this->createDataSnapshot($cancelLog);

            // 4. 执行数据清理（清除敏感信息）
            $this->executeDataClean($resume, $member);

            // 5. 更新用户状态为已注销
            $this->updateMemberStatus($member);

            // 6. 更新注销日志状态
            $this->updateCancelLogStatus($cancelLog, $adminId);

            // 7. 添加180天限制记录
            $this->addRestrictionRecord($member, $cancelLog->id);

            // 8. 发送注销成功通知（仅系统自动执行时）
            if (!$isManualOperation) {
                $this->sendCancelSuccessNotification($member);
                // 更新短信发送状态
                $cancelLog->sms_status = BaseResumeCancelLog::SMS_STATUS_COMPLETE_SENT;
                $cancelLog->save();
            }

            return [
                'success'      => true,
                'cancelLogId'  => $cancelLogId,
                'snapshotId'   => $snapshotId,
                'completeTime' => date('Y-m-d H:i:s'),
                'adminId'      => $adminId,
            ];
        } catch (\Exception $e) {
            // 记录失败日志
            $this->logOperation($member->id, 'failed', $e->getMessage(), $adminId);
            throw $e;
        }
    }

    /**
     * 验证注销申请状态
     * @param int $cancelLogId
     * @return BaseResumeCancelLog
     * @throws \Exception
     */
    private function validateCancelLog($cancelLogId)
    {
        $cancelLog = BaseResumeCancelLog::findOne($cancelLogId);
        if (!$cancelLog) {
            throw new \Exception('注销申请记录不存在');
        }

        if ($cancelLog->status != BaseResumeCancelLog::STATUS_APPLYING) {
            throw new \Exception('注销申请状态异常，无法执行注销');
        }

        // TODO: 验证冷静期是否已结束
        if (strtotime($cancelLog->cooldown_end_time) > time()) {
            throw new \Exception('冷静期尚未结束，无法执行注销');
        }

        return $cancelLog;
    }

    /**
     * 获取简历信息
     * @param int $resumeId
     * @return BaseResume
     * @throws \Exception
     */
    private function getResumeInfo($resumeId)
    {
        $resume = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        return $resume;
    }

    /**
     * 获取会员信息
     * @param int $memberId
     * @return BaseMember
     * @throws \Exception
     */
    private function getMemberInfo($memberId)
    {
        $member = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        return $member;
    }

    /**
     * 创建数据快照
     * @param BaseResumeCancelLog $cancelLog
     * @return int 快照ID
     * @throws \Exception
     */
    private function createDataSnapshot($cancelLog)
    {
        return (new ResumeCancelSnapshotService())->createSnapshot($cancelLog->resume_id, $cancelLog->id);
    }

    /**
     * 执行预注销操作（在申请注销时立即执行）
     * @param BaseResume          $resume
     * @param BaseMember          $member
     * @param BaseResumeCancelLog $cancelLog
     * @throws \Exception
     */
    public function executePreCancelOperations($resume, $member, $cancelLog)
    {
        try {
            // 0. 保存用户原始消息通知设置（在修改前保存）
            $this->saveOriginalSettings($resume->id, $member->id, $cancelLog);

            // 1. 取消与小程序的绑定关系
            $this->unbindMiniProgram($resume->id);

            // 2. 退出人才库
            $this->exitResumeLibrary($resume->id);

            // 3. 关闭职位订阅
            $this->cancelJobSubscription($resume->id);

            // 4. 关闭新消息通知
            $this->cancelMessageNotification($resume->id);
        } catch (\Exception $e) {
            throw new \Exception('预注销操作失败：' . $e->getMessage());
        }
    }

    /**
     * 保存用户原始消息通知设置
     * @param int                 $resumeId
     * @param int                 $memberId
     * @param BaseResumeCancelLog $cancelLog
     * @return array
     * @throws \Exception
     */
    private function saveOriginalSettings($resumeId, $memberId, $cancelLog)
    {
        try {
            // 获取用户原始消息通知设置
            $resumeSettings = $this->getResumeSettings($resumeId);

            // 保存到现有的 resume_setting 字段
            $cancelLog->resume_setting = json_encode($resumeSettings, JSON_UNESCAPED_UNICODE);
            if (!$cancelLog->save()) {
                throw new \Exception('保存原始设置失败：' . $cancelLog->getFirstErrorsMessage());
            }

            Yii::info("保存用户原始消息通知设置成功，resumeId: {$resumeId}, memberId: {$memberId}", 'resume-cancel');

            return $resumeSettings;
        } catch (\Exception $e) {
            throw new \Exception('保存原始设置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取简历设置信息
     * @param int $resumeId
     * @return array
     */
    private function getResumeSettings($resumeId)
    {
        $setting = BaseResumeSetting::findOne($resumeId);
        if (!$setting) {
            return [];
        }

        return [
            'is_job_feedback'    => $setting->is_job_feedback,
            'is_system_message'  => $setting->is_system_message,
            'is_todo_notice'     => $setting->is_todo_notice,
            'is_job_invite'      => $setting->is_job_invite,
            'is_company_view_me' => $setting->is_company_view_me,
        ];
    }

    /**
     * 执行数据清理
     * @param BaseResume $resume
     * @param BaseMember $member
     * @throws \Exception
     */
    private function executeDataClean($resume, $member)
    {
        try {
            // 1. 清理个人敏感信息
            $this->cleanPersonalSensitiveInfo($resume, $member);

            // 2. 清理简历内容（将所有简历相关子表的status设置为0）
            $this->cleanResumeContent($resume->id, $member->id);

            Yii::info("数据清理完成，resumeId: {$resume->id}, memberId: {$member->id}", 'resume-cancel');
        } catch (\Exception $e) {
            Yii::error("数据清理失败，resumeId: {$resume->id}, memberId: {$member->id}, 错误: " . $e->getMessage(),
                'resume-cancel');
            throw new \Exception('数据清理失败：' . $e->getMessage());
        }
    }

    /**
     * 清理个人敏感信息
     * @param BaseResume $resume
     * @param BaseMember $member
     * @throws \Exception
     */
    private function cleanPersonalSensitiveInfo($resume, $member)
    {
        try {
            //  * @property string $add_time 创建时间
            //  * @property string $update_time 修改时间
            //  * @property int $status 状态,1正常,-1限制登录
            //  * @property int $member_id 会员id
            //  * @property int $audit_status 审核状态,-1不通过,1通过,9待审核
            //  * @property string $name 名字
            //  * @property int $gender 性别,1男,2女
            //  * @property string $birthday 生日
            //  * @property string $residence 现居住地
            //  * @property string $height 身高
            //  * @property int $marriage 婚否(1已，2否）
            //  * @property int $education 学历
            //  * @property int $enter_job_time 可入职时间
            //  * @property string $native_place_area_id 籍贯
            //  * @property string $id_card 身份证号
            //  * @property string $refresh_time 刷新时间
            //  * @property int $click 查看次数
            //  * @property string $advantage 个人优势
            //  * @property int $political_status_id 政治面貌
            //  * @property string $english_name 英文名
            //  * @property int $nation_id 民族
            //  * @property string $title_id 职称id
            //  * @property int $household_register_id 户籍/国籍id
            //  * @property int $arrive_date_type 到岗时间类型(-1为自定义)
            //  * @property string $arrive_date 到岗时间(当类型为自定义的时候填写)
            //  * @property int $work_status 求职状态(1在职，2离职)
            //  * @property int $is_project_school 是否985/211
            //  * @property int $age 年龄
            //  * @property int $work_experience 工作经验（年）
            //  * @property string $last_apply_job_time 最后投递时间
            //  * @property int $last_education_id 最高教育经历id
            //  * @property int $top_education_code 最高学历code
            //  * @property int $last_work_id 最近工作经历id
            //  * @property string $birthday_code 生日月日代码(更新生日时同步)
            //  * @property string $work_begin_date_code 开始工作时间月日代码（更新工作经历时同步）
            //  * @property string $last_update_time 简历的最后更新时间
            //  * @property int $complete 简历完整度
            //  * @property int $vip_type 会员类型,0:普通用户,1:生效会员,-1:过期会员
            //  * @property int $vip_level 会员等级 0普通 1黄金 2钻石
            //  * @property string $vip_begin_time 会员生效时间
            //  * @property string $vip_expire_time 会员过期时间
            //  * @property int $identity_type 身份类型（-1缺失，1职场人，2应届生）
            //  * @property string $begin_work_date 参加工作时间
            //  * @property string $uuid uuid
            //  * @property int $is_abroad 是否海外经历,1是,2否
            //  * @property int $is_postdoc 是否有博士后经历,1是,2否
            //  * @property int $is_resume_library 是否在人才库  1是 2否
            //  * @property int $resume_type 简历类型  1精英简历 2优质简历 3普通简历

            // 清理简历表中的敏感信息 变成默认字段
            $resume->name                  = '用户已注销';
            $resume->age                   = 0;
            $resume->english_name          = '';
            $resume->advantage             = '';
            $resume->residence             = '';
            $resume->id_card               = '';
            $resume->birthday              = '0000-00-00';
            $resume->gender                = 0;
            $resume->marriage              = 0;
            $resume->height                = '';
            $resume->native_place_area_id  = '';
            $resume->political_status_id   = 0;
            $resume->nation_id             = 0;
            $resume->title_id              = '';
            $resume->household_register_id = 0;
            $resume->arrive_date_type      = 0;
            $resume->arrive_date           = '0000-00-00';
            $resume->work_status           = 0;
            $resume->is_project_school     = 0;
            $resume->work_experience       = 0;
            $resume->last_education_id     = 0;
            $resume->last_work_id          = 0;
            $resume->birthday_code         = '';
            $resume->work_begin_date_code  = '';
            $resume->is_abroad             = 0;
            $resume->is_postdoc            = 0;
            $resume->is_resume_library     = 0;
            $resume->resume_type           = 0;

            if (!$resume->save()) {
                throw new \Exception('清理简历敏感信息失败：' . $resume->getFirstErrorsMessage());
            }

            // 清理会员表中的敏感信息

            /**
             * @property int    $id                    id;主键id
             * @property string $add_time              创建时间
             * @property string $update_time           更新时间
             * @property int    $status                状态0删除,-1禁止登陆,1正常,9等待审核,-2非合作帐号
             * @property int    $type                  会员类型(1个人/2企业)
             * @property string $username              用户名
             * @property string $password              密码
             * @property string $email                 邮箱
             * @property string $mobile_code           手机号区号
             * @property string $mobile                手机号
             * @property string $avatar                头像
             * @property string $last_login_time       最近一次登录时间
             * @property int    $last_login_ip         最近一次登录的ip
             * @property int    $email_register_status 邮箱登录状态（-1禁止登录，1正常）
             * @property int    $source_type           注册来源（1web端  2H5）
             * @property string $last_active_time      最近活跃时间
             * @property int    $company_member_type   单位账号类型 0主账号 1子账号类型
             * @property int    $cancel_status         注销状态：0未申请注销，9注销中（冷静期），1注销成功（已完成）
             * @property int    $is_chat               是否开启直聊开关（1:是；2:否）
             * @property int    $is_chat_window        是否打开聊天窗 1是 2否
             * @property int    $is_greeting           是否启用招呼语 1启用 2关闭
             * @property int    $greeting_type         默认招呼语类型 1系统 2自定义
             * @property int    $greeting_default_id   默认招呼语ID
             * /
             */

            // 清理会员表中的敏感信息
            $member->mobile      = '';
            $member->mobile_code = '';
            $member->email       = '';
            $member->username    = '';
            $member->avatar      = 'https://img.gaoxiaojob.com/uploads/person/member-cancel.png';

            if (!$member->save()) {
                throw new \Exception('清理会员敏感信息失败：' . $member->getFirstErrorsMessage());
            }

            Yii::info("清理个人敏感信息成功，resumeId: {$resume->id}, memberId: {$member->id}", 'resume-cancel');
        } catch (\Exception $e) {
            throw new \Exception('清理个人敏感信息失败：' . $e->getMessage());
        }
    }

    /**
     * 清理简历内容（将所有简历相关子表的status设置为0）
     * @param int $resumeId
     * @param int $memberId
     * @throws \Exception
     */
    private function cleanResumeContent($resumeId, $memberId)
    {
        try {
            $cleanedTables = [];

            // 定义需要清理的表和对应的模型类
            $tablesToClean = [
                'resume_education'          => 'common\base\models\BaseResumeEducation',
                'resume_work'               => 'common\base\models\BaseResumeWork',
                'resume_research_project'   => 'common\base\models\BaseResumeResearchProject',
                'resume_academic_page'      => 'common\base\models\BaseResumeAcademicPage',
                'resume_academic_patent'    => 'common\base\models\BaseResumeAcademicPatent',
                'resume_academic_book'      => 'common\base\models\BaseResumeAcademicBook',
                'resume_academic_reward'    => 'common\base\models\BaseResumeAcademicReward',
                'resume_other_reward'       => 'common\base\models\BaseResumeOtherReward',
                'resume_certificate'        => 'common\base\models\BaseResumeCertificate',
                'resume_skill'              => 'common\base\models\BaseResumeSkill',
                'resume_other_skill'        => 'common\base\models\BaseResumeOtherSkill',
                'resume_research_direction' => 'common\base\models\BaseResumeResearchDirection',
                'resume_additional_info'    => 'common\base\models\BaseResumeAdditionalInfo',
                'resume_attachment'         => 'common\base\models\BaseResumeAttachment',
                'resume_intention'          => 'common\base\models\BaseResumeIntention',
            ];

            foreach ($tablesToClean as $tableName => $modelClass) {
                try {
                    // 检查模型类是否存在
                    if (!class_exists($modelClass)) {
                        Yii::warning("模型类不存在，跳过清理：{$modelClass}", 'resume-cancel');
                        continue;
                    }

                    // 更新该表中用户的所有记录，将status设置为0
                    $affectedRows = $modelClass::updateAll([
                        'status'      => 0,
                        'update_time' => date('Y-m-d H:i:s'),
                    ], [
                        'member_id' => $memberId,
                        'status'    => 1,
                    ]);

                    $cleanedTables[$tableName] = $affectedRows;

                    if ($affectedRows > 0) {
                        Yii::info("清理表 {$tableName} 成功，影响行数: {$affectedRows}", 'resume-cancel');
                    }
                } catch (\Exception $e) {
                    Yii::warning("清理表 {$tableName} 失败：" . $e->getMessage(), 'resume-cancel');
                    // 继续清理其他表，不中断整个流程
                }
            }

            Yii::info("简历内容清理完成，resumeId: {$resumeId}, memberId: {$memberId}, 清理结果: " . json_encode($cleanedTables),
                'resume-cancel');
        } catch (\Exception $e) {
            throw new \Exception('清理简历内容失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户状态为已注销
     * @param BaseMember $member
     * @throws \Exception
     */
    private function updateMemberStatus($member)
    {
        $member->status        = BaseMember::STATUS_RESUME_CANCELED;
        $member->cancel_status = BaseMember::CANCEL_STATUS_CANCELED;
        if (!$member->save()) {
            throw new \Exception('更新用户状态失败：' . $member->getFirstErrorsMessage());
        }
    }

    /**
     * 更新注销日志状态
     * @param BaseResumeCancelLog $cancelLog
     * @param int                 $adminId
     * @throws \Exception
     */
    private function updateCancelLogStatus($cancelLog, $adminId)
    {
        $cancelLog->status        = BaseResumeCancelLog::STATUS_COMPLETED;
        $cancelLog->complete_time = date('Y-m-d H:i:s');
        $cancelLog->admin_id      = $adminId; // 记录操作的管理员ID

        if (!$cancelLog->save()) {
            throw new \Exception('更新注销日志失败：' . $cancelLog->getFirstErrorsMessage());
        }
    }

    /**
     * 添加180天限制记录
     * @param BaseMember $member
     * @param int        $cancelLogId
     * @throws \Exception
     */
    private function addRestrictionRecord($member, $cancelLogId)
    {
        try {
            // 调用限制服务添加180天限制
            $restrictionService = new ResumeCancelRestrictionService();
            $restrictionId      = $restrictionService->addRestriction($member->mobile, $member->email,
                $member->mobile_code, $cancelLogId);

            Yii::info("添加180天限制记录成功，用户ID: {$member->id}, 限制记录ID: {$restrictionId}", 'resume-cancel');
        } catch (\Exception $e) {
            Yii::error("添加180天限制记录失败，用户ID: {$member->id}, 错误: " . $e->getMessage(), 'resume-cancel');
            throw new \Exception('添加180天限制记录失败：' . $e->getMessage());
        }
    }

    /**
     * 发送注销完成通知
     * @param BaseMember $member
     */
    private function sendCancelSuccessNotification($member)
    {
        Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_COMPLETE,
            $member->mobile_code);
    }

    /**
     * 记录操作日志
     * @param int    $memberId
     * @param string $result
     * @param string $errorMessage
     * @param int    $adminId
     */
    private function logOperation($memberId, $result, $errorMessage = '', $adminId = 0)
    {
        $operatorType = $adminId > 0 ? 'manual' : 'system';
        $logMessage   = "用户注销执行{$result} - 用户ID: {$memberId}, 操作类型: {$operatorType}";

        if ($adminId > 0) {
            $logMessage .= ", 管理员ID: {$adminId}";
        }

        if ($errorMessage) {
            $logMessage .= ", 错误: {$errorMessage}";
        }

        if ($result === 'success') {
            Yii::info($logMessage, 'resume-cancel');
        } else {
            Yii::error($logMessage, 'resume-cancel');
        }
    }

    // =====================================================
    // 预处理操作相关方法
    // =====================================================

    /**
     * 取消与小程序的绑定关系
     * @param int $memberId 会员ID
     * @return bool
     * @throws \Exception
     */
    private function unbindMiniProgram($resumeId)
    {
        BaseResumeWxBind::cancelWxBind($resumeId);
    }

    /**
     * 退出人才库
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function exitResumeLibrary($resumeId)
    {
        $resume                    = BaseResume::findOne($resumeId);
        $resume->is_resume_library = BaseResume::IS_RESUME_LIBRARY_NO;

        $resume->save();
        // 这里是否考虑调用updateResume??
    }

    /**
     * 关闭职位订阅
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function cancelJobSubscription($resumeId)
    {
        $service = new JobSubscribeApplication();

        $service->cancel($resumeId);
    }

    /**
     * 关闭新消息通知
     * @param int $resumeId 会员ID
     * @return bool
     * @throws \Exception
     */
    private function cancelMessageNotification($resumeId)
    {
        $setting = BaseResumeSetting::findOne($resumeId);

        $setting->is_job_feedback    = BaseResumeSetting::IS_JOB_FEEDBACK_NO;
        $setting->is_system_message  = BaseResumeSetting::IS_SYSTEM_MESSAGE_NO;
        $setting->is_todo_notice     = BaseResumeSetting::IS_TODO_NOTICE_NO;
        $setting->is_job_invite      = BaseResumeSetting::IS_JOB_INVITE_NO;
        $setting->is_company_view_me = BaseResumeSetting::IS_COMPANY_VIEW_ME_NO;

        $setting->save();
    }

    /**
     * 重新加载新消息通知
     * @param int $resumeId 会员ID
     * @return bool
     * @throws \Exception
     */
    private function reloadMessageNotification(BaseResumeCancelLog $logInfo)
    {
        $resumeSetting = json_decode($logInfo->resume_setting, true);

        $setting = BaseResumeSetting::findOne($logInfo->resume_id);

        if (isset($resumeSetting['is_job_feedback'])) {
            $setting->is_job_feedback = $resumeSetting['is_job_feedback'];
        }

        if (isset($resumeSetting['is_system_message'])) {
            $setting->is_system_message = $resumeSetting['is_system_message'];
        }

        if (isset($resumeSetting['is_todo_notice'])) {
            $setting->is_todo_notice = $resumeSetting['is_todo_notice'];
        }

        if (isset($resumeSetting['is_job_invite'])) {
            $setting->is_job_invite = $resumeSetting['is_job_invite'];
        }
        if (isset($resumeSetting['is_company_view_me'])) {
            $setting->is_company_view_me = $resumeSetting['is_company_view_me'];
        }
        $setting->update_time = CUR_DATETIME;
        $setting->save();
    }

    /**
     * 更新用户注销状态
     * @param int $memberId     会员ID
     * @param int $cancelStatus 注销状态
     * @return bool
     * @throws \Exception
     */
    private function updateMemberCancelStatus($memberId, $cancelStatus)
    {
        try {
            $member = BaseMember::findOne($memberId);
            if (!$member) {
                throw new \Exception('用户不存在');
            }

            $member->cancel_status = $cancelStatus;
            $member->update_time   = date('Y-m-d H:i:s');

            if (!$member->save()) {
                throw new \Exception('更新用户注销状态失败：' . $member->getFirstErrorsMessage());
            }

            Yii::info("更新用户注销状态成功，memberId: {$memberId}, cancelStatus: {$cancelStatus}", 'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('更新用户注销状态失败：' . $e->getMessage());
        }
    }

    // =====================================================
    // 撤回注销时的恢复逻辑
    // =====================================================

    /**
     * 撤回注销时恢复预处理操作
     * @param int $memberId 会员ID
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    public function restorePreCancelOperations(BaseResumeCancelLog $cancelLog)
    {
        try {
            // 1. 恢复用户注销状态为正常
            $this->updateMemberCancelStatus($cancelLog->member_id, BaseMember::CANCEL_STATUS_NORMAL);

            // 2. 重新进入人才库
            $this->reevaluateResumeLibrary($cancelLog->resume_id);

            // 3. 重新加载新消息通知
            $this->reloadMessageNotification($cancelLog);
            Yii::info("恢复预处理操作成功，memberId: {$cancelLog->member_id}, resumeId: {$cancelLog->resume_id}",
                'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('恢复预处理操作失败：' . $e->getMessage());
        }
    }

    /**
     * 重新评估是否加入人才库
     * @param int $resumeId 简历ID
     * @return bool
     * @throws \Exception
     */
    private function reevaluateResumeLibrary($resumeId)
    {
        try {
            // 调用人才库服务重新评估
            $service = new ResumeLibraryService();
            $service->setResumeId($resumeId)
                ->setOparetion(ResumeLibraryService::OPERATION_UPDATE_FILTER_RESUME_LIBRARY)
                ->run();

            Yii::info("重新评估人才库状态成功，resumeId: {$resumeId}", 'resume-cancel');

            return true;
        } catch (\Exception $e) {
            throw new \Exception('重新评估人才库状态失败：' . $e->getMessage());
        }
    }
}
