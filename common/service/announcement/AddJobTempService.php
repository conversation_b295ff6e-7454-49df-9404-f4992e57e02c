<?php

namespace common\service\announcement;

use admin\models\Job;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMajor;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\WxWork;
use Faker\Provider\Base;
use yii\base\Exception;
use common\helpers\ValidateHelper;

class AddJobTempService extends BaseService
{
    public $saveType;
    public $copyId;
    public $isTemp;
    public $jobTemplateId; //职位模版id

    // 单个添加
    const SAVE_TYPE_SINGLE = 1;
    // 复制添加
    const SAVE_TYPE_COPY = 2;

    /**
     * 执行添加
     */
    public function run()
    {
        switch ($this->saveType) {
            case self::SAVE_TYPE_SINGLE:
                $this->single();
                break;
            case self::SAVE_TYPE_COPY:
                $this->copy();
                break;
            default:
                throw new Exception('添加类型错误');
        }
    }

    /**
     * 设置单个添加
     * @return $this
     */
    public function setSingle(): AddJobTempService
    {
        $this->saveType = self::SAVE_TYPE_SINGLE;

        return $this;
    }

    /**
     * 设置复制添加
     * @return $this
     */
    public function setCopy($params): AddJobTempService
    {
        $this->saveType = self::SAVE_TYPE_COPY;
        $this->copyId   = $params['id'];
        $this->isTemp   = $params['isTemp'];

        return $this;
    }

    /**
     * 单个添加
     * @throws Exception
     */
    private function single()
    {
        $this->add();
    }

    /**
     * 复制添加
     * @throws Exception
     */
    private function copy()
    {
        $this->copyAdd();
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): AddJobTempService
    {
        //兼容处理
        if (!empty($this->companyId)) {
            $companyId = $this->companyId;
        } else {
            if (isset($data['company_id']) && $data['company_id'] > 0) {
                $companyId = $data['company_id'];
            } elseif (isset($data['id']) && $data['id'] > 0) {
                $tempData  = BaseJobTemp::findOne(['id' => $data['id']]);
                $companyId = $tempData->company_id;
            }
        }
        if (empty($companyId) || $companyId < 0) {
            throw new Exception('未获取到单位信息');
        }
        $this->companyModel = BaseCompany::findOne($companyId);
        $this->companyId    = $this->companyModel->id;
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO) {
            if (isset($data['delivery_way'])) {
                //非合作单位去掉delivery_way字段 永远走else分支
                unset($data['delivery_way']);
            }
            if ($data['extra_notify_address']) {
                throw new Exception('非合作单位不支持填写邮件通知地址');
            }
        }
        //特殊处理单位端过来的数据，且携带source ，source=1跟公告 source =2跟随自己
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY && isset($data['source']) && $data['source'] == 1) {
            //谨慎处理,才能进入特殊处理否侧让它独立
            if ($data['announcement_id'] > 0) {
                //获取公告数据
                $announcementInfo         = BaseAnnouncement::findOne($data['announcement_id']);
                $dataApplyTypeStr         = '';
                $announcementApplyTypeStr = '';
                //升-排序报名方式
                if ($data['apply_type']) {
                    $dataApplyType = explode(',', $data['apply_type']);
                    sort($dataApplyType);
                    $dataApplyTypeStr = implode(',', $dataApplyType);
                }
                if ($announcementInfo->apply_type) {
                    $announcementApplyType = explode(',', $announcementInfo->apply_type);
                    sort($announcementApplyType);
                    $announcementApplyTypeStr = implode(',', $announcementApplyType);
                }
                //是否用自己配置
                $myData = true;//用自己的
                if ($dataApplyTypeStr == $announcementApplyTypeStr && $data['apply_address'] == $announcementInfo->apply_address && $data['extra_notify_address'] == $announcementInfo->extra_notify_address) {
                    $myData = false;//说明跟公告相同那就是跟公告
                }
                if (!$myData) {//跟随公告
                    //将参数清理为空跟随公告数据
                    $data['apply_type']           = '';
                    $data['apply_address']        = '';
                    $data['extra_notify_address'] = '';
                    $data['delivery_way']         = 0;
                    $data['delivery_type']        = 0;
                }
            }
        }

        $checkData = [
            'name',
            'job_category_id',
            'education_type',
            'amount',
            'duty',
            'requirement',
            'province_id',
            'city_id',
        ];
        foreach ($checkData as $list) {
            if (strlen($data[$list]) < 1) {
                throw new Exception('参数' . $list . '不能为空');
            }
        }
        // 清除文本换行
        $data = StringHelper::cleanLineFeed($data, [
            'duty',
            'requirement',
            'remark',
        ]);

        $matches = preg_match('/^([1-9]\d{0,3}|\u82e5\u5e72)$/', $data['amount']);
        if (!$matches && $data['amount'] != '若干') {
            throw new Exception('招聘人数填写仅支持数字或若干');
        }

        if (!empty($data['announcement_period_date'])) {
            if (empty($data['period_date']) || $data['period_date'] > $data['announcement_period_date'] || strtotime($data['period_date']) < 1) {
                $data['period_date'] = $data['announcement_period_date'] ?: TimeHelper::ZERO_TIME;
            }
        } else {
            if (strtotime($data['period_date']) < 1) {
                $data['period_date'] = TimeHelper::ZERO_TIME;
            }
            if (strtotime($data['period_date']) > 1 && strtotime($data['period_date']) < strtotime(date('Y-m-d'))) {
                throw new Exception('职位有效时间不能小于当前时间');
            }
        }

        // 薪资范围(非年薪)
        if ($data['wage_type'] != BaseJob::WAGE_TYPE_YEAR) {
            // 如果非面议(前端直接传了类型过来)
            if ($data['is_negotiable'] == BaseJob::IS_NEGOTIABLE_NO) {
                if (!empty($data['wage_id'])) {
                    $wageInfo         = BaseDictionary::getMinAndMaxWage($data['wage_id']);
                    $data['min_wage'] = (int)$wageInfo['min'];
                    $data['max_wage'] = (int)$wageInfo['max'];
                }
            }
        }

        //报名方式与通知地址不可同时填写
        if ($data['apply_type'] && $data['extra_notify_address']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        if (isset($data['delivery_way']) && $data['delivery_way'] > 0) {
            if ($data['delivery_way'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($data['apply_type']) || empty($data['apply_address'])) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['apply_address']);
                } else {
                    if (!ValidateHelper::isUrl($data['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['apply_address']);
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {//delivery_way=1
                if ($data['delivery_way'] != BaseJob::DELIVERY_WAY_PLATFORM) {
                    if (empty($data['apply_type']) || empty($data['apply_address'])) {
                        throw new Exception('报名方式没有勾选或者投递地址为空');
                    }
                }
                $data['apply_type']    = '';
                $data['apply_address'] = '';
            }
        } else {
            if ($data['apply_type']) {
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if (empty($data['apply_address'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['apply_address']);
                } else {
                    if (!ValidateHelper::isUrl($data['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['apply_address']);
                //判断投递方式
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $data['apply_type']    = '';
                $data['apply_address'] = '';
                //如果是单位端就给默认平台投递,且不携带source数据源 如果是运营后台就默认成0跟随公告
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY && (!isset($data['source']) || (isset($data['source']) && $data['source'] == 2))) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_PLATFORM;
                } else {
                    $data['delivery_way'] = 0;
                }
            }
        }
        //检查通知邮箱的格式
        if ($data['extra_notify_address']) {
            $data['extra_notify_address'] = BaseJob::checkEmailApplyAddress($data['extra_notify_address']);
        }
        //处理投递类型；选择投递类型那就站外投递，没有就站内投递
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;//跟公告
            } elseif ($data['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }

            //职位协同设置
            if ($data['job_contact_synergy_ids']) {
                //验证协同账号的合法性
                if (count($data['job_contact_synergy_ids']) > 3) {
                    throw new Exception('协同账号最多设置3个');
                }
                $jobContactSynergyIds = [];
                foreach ($data['job_contact_synergy_ids'] as $item) {
                    $record_result_synergy = BaseCompanyMemberInfo::validateMemberRecordId($this->companyId, $item, 2);
                    if ($record_result_synergy) {
                        $jobContactSynergyIds[] = $item;
                    }
                }
                $data['job_contact_synergy_ids'] = count($jobContactSynergyIds) > 0 ? implode(',',
                    $jobContactSynergyIds) : '';
            } else {
                $data['job_contact_synergy_ids'] = '';
            }
            //职位联系人
            if (empty($data['job_contact_id'])) {//jobContactId
                throw new Exception('职位联系人必须设置');
            }
            $record_result = BaseCompanyMemberInfo::validateMemberRecordId($this->companyId, $data['job_contact_id'],
                3);
            if (!$record_result) {
                throw new Exception('职位联系人设置错误');
            }
            //获取单位主账号信息
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'company_id'          => $this->companyId,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
            if (!in_array($data['job_contact_id'],
                array_unique(array_merge(explode(',', $data['job_contact_synergy_ids']), [$companyMemberInfo->id])))) {
                throw new Exception('职位联系人必须是协同账号或者是单位主账号');
            }
        } else {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        }
        $jobCategoryIdLevel = BaseCategoryJob::findOneVal(['id' => $data['job_category_id']], 'level');
        if ($jobCategoryIdLevel == 1) {
            throw new Exception('岗位类型有误');
        }
        $data['major_id']    = implode(',', $data['major_id']);
        $this->jobTemplateId = $data['job_template_id'] ?: '';
        $this->jobTempData   = $data;

        return $this;
    }

    /**
     * 单个添加/编辑
     * @throws Exception
     */
    private function add()
    {
        $data   = $this->jobTempData;
        $model  = BaseJobTemp::findOne(['id' => $data['id']]) ?: new BaseJobTemp();
        $oldJob = BaseJob::findOne(['id' => $data['job_id']]);
        if ($oldJob) {
            if ($oldJob->delivery_type == 0 && $data['announcement_id'] > 0 && $data['delivery_type'] != 0) {
                $announcementInfo = BaseAnnouncement::findOne($data['announcement_id']);
                if ($announcementInfo->delivery_type != $data['delivery_type']) {
                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                }
            } elseif ($oldJob->delivery_type > 0 && $data['announcement_id'] > 0 && $data['delivery_type'] == 0 && $this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                //单位端去验证这一层吧 不是单位端就不验证了
                $announcementInfo = BaseAnnouncement::findOne($data['announcement_id']);
                if ($oldJob->delivery_type != $announcementInfo->delivery_type) {
                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                }
            } elseif ($oldJob->delivery_type > 0 && $data['delivery_type'] > 0) {
                if ($oldJob->delivery_type != $data['delivery_type']) {
                    throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                }
            }
        }
        $model->member_id            = $this->companyMemberModel->id;
        $model->company_id           = $this->companyModel->id;
        $model->announcement_id      = $data['announcement_id'] ?: 0;
        $model->job_id               = $data['job_id'] ?: 0;
        $model->is_temp              = BaseJobTemp::IS_TEMP_YES;
        $model->name                 = $data['name'];
        $model->code                 = $data['code'] ?: '';
        $model->job_category_id      = $data['job_category_id'] ?: 0;
        $model->education_type       = $data['education_type'] ?: 0;
        $model->major_id             = $data['major_id'] ?: '';
        $model->nature_type          = $data['nature_type'] ?: '';
        $model->wage_type            = $data['wage_type'] ?: 0;
        $model->is_negotiable        = $data['is_negotiable'] ?: BaseJob::IS_NEGOTIABLE_NO;
        $model->apply_type           = $data['apply_type'] ?: '';
        $model->apply_address        = $data['apply_address'] ?: '';
        $model->min_wage             = $data['min_wage'] ?: 0;
        $model->max_wage             = $data['max_wage'] ?: 0;
        $model->experience_type      = $data['experience_type'] ?: 0;
        $model->age_type             = $data['age_type'] ?: '0';
        $model->title_type           = $data['title_type'] ?: 0;
        $model->political_type       = $data['political_type'] ?: 0;
        $model->abroad_type          = $data['abroad_type'] ?: 0;
        $model->amount               = $data['amount'];
        $model->department           = $data['department'] ?: '';
        $model->province_id          = $data['province_id'] ?: 0;
        $model->city_id              = $data['city_id'] ?: 0;
        $model->address              = $data['address'] ?: '';
        $model->welfare_tag          = $data['welfare_tag'] ?: '';
        $model->period_date          = $data['period_date'];
        $model->duty                 = $data['duty'] ?: '详见公告正文';
        $model->requirement          = $data['requirement'] ?: '详见公告正文';
        $model->remark               = $data['remark'] ?: '';
        $model->audit_status         = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        $model->delivery_limit_type  = $data['delivery_limit_type'] ?: '';
        $model->delivery_type        = $data['delivery_type'] ?: 0;
        $model->delivery_way         = $data['delivery_way'] ?: 0;
        $model->extra_notify_address = $data['extra_notify_address'] ?: '';
        $model->contact_id           = $data['job_contact_id'] ?: 0;
        $model->contact_synergy_id   = $data['job_contact_synergy_ids'] ?: '';
        //判断操作平台为运营平台
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            $model->establishment_type = $data['establishment_type'] ?: '';
            $model->is_establishment   = !empty($data['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            if ($oldJob) {
                $model->establishment_type = $oldJob->establishment_type ?: '';
                $model->is_establishment   = $oldJob->is_establishment ?: BaseJob::IS_ESTABLISHMENT_NO;
            } else {
                $model->establishment_type = '';
                $model->is_establishment   = BaseJob::IS_ESTABLISHMENT_NO;
            }
        }

        // 判断临时职位的创建类型
        if ($oldJob && $data['job_id'] && ($oldJob->first_release_time != TimeHelper::ZERO_TIME || $oldJob->status == BaseJob::STATUS_WAIT)) {
            $model->create_type = self::CREATE_TYPE_EDIT;
        } else {
            $model->create_type = self::CREATE_TYPE_ADD;
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //如果id与job_id相同时候回滚本次操作
        if ($model->id == $model->job_id) {
            // 通知系统管理员，及时删除
            WxWork::getInstance()
                ->robotMessageToSystem('职位id与临时职位id相同，请及时处理   ' . $model->id);
            throw new Exception('职位id与临时职位id相同，请稍后再试！');
        }

        $model = $model->toArray();
        // 回显职位模版id
        $model['job_template_id'] = $this->jobTemplateId;

        $majorArr  = explode(',', $model['major_id']);
        $majorName = BaseMajor::find()
            ->where(['id' => $majorArr])
            ->select('name')
            ->asArray()
            ->all();

        $majorNameOne = [];
        foreach ($majorName as $item) {
            $majorNameOne[] = $item['name'];
        }

        $model['id']             = (string)$model['id'];
        $model['job_id']         = (string)$model['job_id'];
        $model['majorTxt']       = implode(';', $majorNameOne);
        $model['educationTxt']   = BaseDictionary::getEducationName($model['education_type']) ?: '';
        $model['areaTxt']        = BaseArea::getAreaName($model['city_id']) ?: '';
        $model['oldAuditStatus'] = $oldJob->audit_status ?: BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        if ($oldJob && $oldJob->first_release_time != TimeHelper::ZERO_TIME) {
            $model['statusTxt']      = BaseJob::JOB_SEARCH_STATUS_NAME[$oldJob->status];
            $model['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[$model['oldAuditStatus']];
        } else {
            $model['statusTxt']      = '待发布';
            $model['auditStatusTxt'] = $oldJob->audit_status == BaseJob::AUDIT_STATUS_WAIT ? '-' : BaseJob::JOB_AUDIT_STATUS_NAME[$model['oldAuditStatus']];
        }
        if (!$model['min_wage'] && !$model['max_wage']) {
            $model['wage'] = '面议';
        } else {
            $model['wage'] = BaseJob::formatWage($model['min_wage'], $model['max_wage'], $model['wage_type']) ?: '-';
        }
        // 合作单位&有审核通过历史职位不可删除
        $isCooperation = BaseCompany::findOneVal(['id' => $oldJob->company_id], 'is_cooperation');
        if ($oldJob && $oldJob->first_release_time != TimeHelper::ZERO_TIME && $isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $model['canDel'] = false;
        } else {
            $model['canDel'] = true;
        }
        $contact_synergy_ids              = $model['contact_synergy_id'] ? explode(',',
            $model['contact_synergy_id']) : [];
        $model['job_contact_synergy']     = count($contact_synergy_ids) > 0 ? BaseCompanyMemberInfo::getInfoMany($contact_synergy_ids) : [];
        $model['job_contact_synergy_num'] = count($model['job_contact_synergy']);
        foreach ($model['job_contact_synergy'] as &$item) {
            $item['is_contact'] = 0;
            if ($item['id'] == $model['contact_id']) {
                $item['is_contact'] = 1;
            }
        }
        $model['job_contact'] = BaseCompanyMemberInfo::getInfoOne($model['contact_id']);
        $information          = [];
        if ($model['areaTxt']) {
            array_push($information, $model['areaTxt']);
        }
        if ($model['amount']) {
            array_push($information, "招{$model['amount']}人");
        }
        if ($model['educationTxt']) {
            array_push($information, $model['educationTxt']);
        }
        if ($model['wage']) {
            array_push($information, $model['wage']);
        }
        if ($model['majorTxt']) {
            array_push($information, $model['majorTxt']);
        }
        $model['information'] = implode(' | ', $information);

        $this->jobTempData = $model;
    }

    /**
     * 复制添加
     */
    private function copyAdd()
    {
        if ($this->isTemp == BaseJobTemp::IS_TEMP_YES) {
            $data                            = BaseJobTemp::findOne(['id' => $this->copyId]);
            $data                            = $data->getAttributes();
            $data['job_contact_id']          = $data['contact_id'];
            $data['job_contact_synergy_ids'] = $data['contact_synergy_id'];
            unset($data['contact_id'], $data['contact_synergy_id']);
        } else {
            $data                            = BaseJob::findOne(['id' => $this->copyId]);
            $data                            = $data->getAttributes();
            $contact                         = Job::getJobContact($this->copyId);
            $data['job_contact_id']          = $contact['company_member_info_id'];
            $contact_synergy                 = Job::getJobContactSynergy($this->copyId);
            $data['job_contact_synergy_ids'] = implode(',', array_column($contact_synergy, 'company_member_info_id'));
        }

        if (empty($data['company_id'])) {
            $companyId = $this->companyId;
        } else {
            $companyId = $data['company_id'];
        }
        if (empty($companyId) || $companyId < 0) {
            throw new Exception('参数错误');
        }
        $this->companyModel = BaseCompany::findOne($companyId);
        if (empty($data)) {
            throw new Exception('数据不存在');
        }

        $model                       = new BaseJobTemp();
        $model->member_id            = $this->companyMemberModel->id;
        $model->company_id           = $this->companyModel->id;
        $model->announcement_id      = $data['announcement_id'] ?: 0;
        $model->is_temp              = BaseJobTemp::IS_TEMP_YES;
        $model->name                 = $data['name'];
        $model->code                 = $data['code'];
        $model->job_category_id      = $data['job_category_id'];
        $model->education_type       = $data['education_type'];
        $model->major_id             = $data['major_id'] ?: '';
        $model->nature_type          = $data['nature_type'];
        $model->wage_type            = $data['wage_type'];
        $model->is_negotiable        = $data['is_negotiable'];
        $model->apply_type           = $data['apply_type'] ?: '';
        $model->apply_address        = $data['apply_address'];
        $model->min_wage             = $data['min_wage'];
        $model->max_wage             = $data['max_wage'];
        $model->experience_type      = $data['experience_type'];
        $model->age_type             = $data['age_type'];
        $model->title_type           = $data['title_type'];
        $model->political_type       = $data['political_type'];
        $model->abroad_type          = $data['abroad_type'];
        $model->amount               = $data['amount'];
        $model->department           = $data['department'];
        $model->province_id          = $data['province_id'];
        $model->city_id              = $data['city_id'];
        $model->district_id          = $data['district_id'];
        $model->address              = $data['address'];
        $model->welfare_tag          = $data['welfare_tag'];
        $model->period_date          = $data['period_date'] ?: TimeHelper::ZERO_TIME;
        $model->duty                 = $data['duty'] ?: '详见公告正文';
        $model->requirement          = $data['requirement'] ?: '详见公告正文';
        $model->remark               = $data['remark'] ?: '';
        $model->audit_status         = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        $model->create_type          = self::CREATE_TYPE_ADD;
        $model->delivery_limit_type  = $data['delivery_limit_type'] ?: '';
        $model->delivery_type        = $data['delivery_type'] ?: 0;
        $model->delivery_way         = $data['delivery_way'] ?: 0;
        $model->extra_notify_address = $data['extra_notify_address'] ?: '';
        $model->establishment_type   = $data['establishment_type'] ?: '';
        $model->is_establishment     = !empty($data['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        $model->contact_id           = $data['job_contact_id'] ?: 0;
        $model->contact_synergy_id   = $data['job_contact_synergy_ids'] ?: '';

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        $model = $model->toArray();

        $majorArr  = explode(',', $model['major_id']);
        $majorName = BaseMajor::find()
            ->where(['id' => $majorArr])
            ->select('name')
            ->asArray()
            ->all();

        $majorNameOne = [];
        foreach ($majorName as $item) {
            $majorNameOne[] = $item['name'];
        }

        $model['id']             = (string)$model['id'];
        $model['auditStatusTxt'] = '-';
        $model['statusTxt']      = '待发布';
        $model['isCopy']         = true;
        $model['majorTxt']       = implode(';', $majorNameOne);
        $model['educationTxt']   = BaseDictionary::getEducationName($model['education_type']) ?: '';
        $model['areaTxt']        = BaseArea::getAreaName($model['city_id']) ?: '';
        $model['canDel']         = true;
        if (!$model['min_wage'] && !$model['max_wage']) {
            $model['wage'] = '面议';
        } else {
            $model['wage'] = BaseJob::formatWage($model['min_wage'], $model['max_wage'], $model['wage_type']) ?: '-';
        }
        $contact_synergy_ids              = $model['contact_synergy_id'] ? explode(',',
            $model['contact_synergy_id']) : [];
        $model['job_contact_synergy']     = count($contact_synergy_ids) > 0 ? BaseCompanyMemberInfo::getInfoMany($contact_synergy_ids) : [];
        $model['job_contact_synergy_num'] = count($contact_synergy_ids);
        foreach ($model['job_contact_synergy'] as &$item) {
            $item['is_contact'] = 0;
            if ($item['id'] == $model['contact_id']) {
                $item['is_contact'] = 1;
            }
        }
        $model['job_contact'] = BaseCompanyMemberInfo::getInfoOne($model['contact_id']);
        $information          = [];
        if ($model['areaTxt']) {
            array_push($information, $model['areaTxt']);
        }
        if ($model['amount']) {
            array_push($information, "招{$model['amount']}人");
        }
        if ($model['educationTxt']) {
            array_push($information, $model['educationTxt']);
        }
        if ($model['wage']) {
            array_push($information, $model['wage']);
        }
        if ($model['majorTxt']) {
            array_push($information, $model['majorTxt']);
        }
        $model['information'] = implode(' | ', $information);

        $this->jobTempData = $model;
    }

}
