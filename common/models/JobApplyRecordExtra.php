<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_record_extra".
 *
 * @property int $job_id 职位ID
 * @property int $total 投递总数
 * @property int $delivery_type_outer 站外投递总数
 * @property int $delivery_type_outside 站内投递总数
 * @property int $delivery_way_platform 投递方式平台投递总数
 * @property int $delivery_way_email 投递方式邮件投递总数
 * @property int $delivery_way_link 投递方式链接投递总数
 * @property int $platform_pc 投递平台pc总数
 * @property int $platform_h5 投递平台h5总数
 * @property int $platform_mini 投递平台mini总数
 * @property int $platform_app 投递平台app总数
 * @property int $interview 投递邀面数量
 * @property int $education_other 学历分布-其他
 * @property int $education_junior 学历分布-大专
 * @property int $education_undergraduate 学历分布-本科
 * @property int $education_master 学历分布-硕士
 * @property int $education_doctor 学历分布-博士
 * @property int $announcement_id 公告ID
 */
class JobApplyRecordExtra extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_record_extra';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['total', 'delivery_type_outer', 'delivery_type_outside', 'delivery_way_platform', 'delivery_way_email', 'delivery_way_link', 'platform_pc', 'platform_h5', 'platform_mini', 'platform_app', 'interview', 'education_other', 'education_junior', 'education_undergraduate', 'education_master', 'education_doctor', 'announcement_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'job_id' => 'Job ID',
            'total' => 'Total',
            'delivery_type_outer' => 'Delivery Type Outer',
            'delivery_type_outside' => 'Delivery Type Outside',
            'delivery_way_platform' => 'Delivery Way Platform',
            'delivery_way_email' => 'Delivery Way Email',
            'delivery_way_link' => 'Delivery Way Link',
            'platform_pc' => 'Platform Pc',
            'platform_h5' => 'Platform H5',
            'platform_mini' => 'Platform Mini',
            'platform_app' => 'Platform App',
            'interview' => 'Interview',
            'education_other' => 'Education Other',
            'education_junior' => 'Education Junior',
            'education_undergraduate' => 'Education Undergraduate',
            'education_master' => 'Education Master',
            'education_doctor' => 'Education Doctor',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
