<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_tag_relation".
 *
 * @property int $id
 * @property int $resume_id 求职者id
 * @property int $resume_tag_id 求职者标签id
 */
class ResumeTagRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_tag_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['resume_id', 'resume_tag_id'], 'required'],
            [['resume_id', 'resume_tag_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'resume_id' => 'Resume ID',
            'resume_tag_id' => 'Resume Tag ID',
        ];
    }
}
