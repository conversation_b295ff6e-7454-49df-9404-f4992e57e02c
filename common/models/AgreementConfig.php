<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "agreement_config".
 *
 * @property int $id
 * @property int $type 类型 1、VIP服务协议  2、求职快服务协议
 * @property string $name 协议名称
 * @property string $content 协议内容
 * @property string $version 版本号
 * @property string $add_time 添加时间
 * @property string $update_time 更新时间
 * @property int $status 状态:1当前显示版本，2历史版本
 * @property string $description 描述
 */
class AgreementConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'agreement_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'status'], 'integer'],
            [['content', 'version', 'add_time'], 'required'],
            [['content'], 'string'],
            [['add_time', 'update_time'], 'safe'],
            [['name', 'version', 'description'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'name' => 'Name',
            'content' => 'Content',
            'version' => 'Version',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'description' => 'Description',
        ];
    }
}
