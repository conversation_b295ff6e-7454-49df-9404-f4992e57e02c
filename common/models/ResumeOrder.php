<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_order".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property string $pay_time 支付时间
 * @property int $status 状态,0未支付,1已支付,-1已取消
 * @property int $equity_status 权益修改状态,0未录入,1已录入（支付状态为1,录入状态为0,则为异常订单）
 * @property int $resume_id 在线简历表主键id
 * @property int $equity_package_id 权益组合表主键id
 * @property int $payway 支付方式:1:微信,2:支付宝
 * @property int $platform 下单渠道:1:PC,2:H5,3:MINI
 * @property int $pay_channel 支付渠道:1:微信扫码,2:微信H5,3:微信JSAPI
 * @property string $ip IP
 * @property string $original_amount 原始金额
 * @property string $real_amount 真实金额
 * @property string $order_no 平台订单号
 * @property string $trade_no 交易订单号
 * @property string $remark 备注
 * @property string $snapshot_data 快照数据
 * @property int $equity_package_type 权益包的类型:1基础包,2活动包
 */
class ResumeOrder extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_order';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'pay_time'], 'safe'],
            [['status', 'equity_status', 'resume_id', 'equity_package_id', 'payway', 'platform', 'pay_channel', 'equity_package_type'], 'integer'],
            [['original_amount', 'real_amount'], 'number'],
            [['ip', 'order_no', 'trade_no', 'remark'], 'string', 'max' => 255],
            [['snapshot_data'], 'string', 'max' => 2048],
            [['order_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'pay_time' => 'Pay Time',
            'status' => 'Status',
            'equity_status' => 'Equity Status',
            'resume_id' => 'Resume ID',
            'equity_package_id' => 'Equity Package ID',
            'payway' => 'Payway',
            'platform' => 'Platform',
            'pay_channel' => 'Pay Channel',
            'ip' => 'Ip',
            'original_amount' => 'Original Amount',
            'real_amount' => 'Real Amount',
            'order_no' => 'Order No',
            'trade_no' => 'Trade No',
            'remark' => 'Remark',
            'snapshot_data' => 'Snapshot Data',
            'equity_package_type' => 'Equity Package Type',
        ];
    }
}
