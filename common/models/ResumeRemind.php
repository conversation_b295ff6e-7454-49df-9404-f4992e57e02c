<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_remind".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $resume_id 简历id
 * @property int $job_apply_all_count 全部职位投递状态数量
 * @property int $job_apply_wait_check_count 已投递状态数量
 * @property int $job_apply_check_count 已查看状态数量
 * @property int $job_apply_pass_count 通过初筛状态数量
 * @property int $job_apply_invite_count 要求面试状态数量
 * @property int $job_apply_no_pass_count 不合适状态数量
 * @property int $job_apply_employed_count 已录入状态数量
 * @property int $company_view_count 单位查看数量(谁看过我)
 * @property int $job_invite_count 职位邀约数量
 */
class ResumeRemind extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_remind';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['resume_id', 'job_apply_all_count', 'job_apply_wait_check_count', 'job_apply_check_count', 'job_apply_pass_count', 'job_apply_invite_count', 'job_apply_no_pass_count', 'job_apply_employed_count', 'company_view_count', 'job_invite_count'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'resume_id' => 'Resume ID',
            'job_apply_all_count' => 'Job Apply All Count',
            'job_apply_wait_check_count' => 'Job Apply Wait Check Count',
            'job_apply_check_count' => 'Job Apply Check Count',
            'job_apply_pass_count' => 'Job Apply Pass Count',
            'job_apply_invite_count' => 'Job Apply Invite Count',
            'job_apply_no_pass_count' => 'Job Apply No Pass Count',
            'job_apply_employed_count' => 'Job Apply Employed Count',
            'company_view_count' => 'Company View Count',
            'job_invite_count' => 'Job Invite Count',
        ];
    }
}
