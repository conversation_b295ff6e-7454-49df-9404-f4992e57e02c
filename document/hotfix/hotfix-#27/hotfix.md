# ReadMe

***

### 参与人员

- 龚传栋
- 李志豪

***

### 分支

|仓库|    bug分支    |备注|
|:----:|:-----------:|:----:|
|new_gaoxiao_yii| hotfix/#2.7 |-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |-|
|执行sql语句|  -   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |见下方"composer安装"|
|更新配置|  -   |见下方"更新配置"|
|创建队列|  -   |见下方"创建队列"|
|执行脚本|  是   |见下方"执行脚本"|
|删除redis缓存|  -   |见下方"删除redis缓存"|
|重启队列|  -   |上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|  -   |-|
|添加定时任务|  -   |见下方"定时任务"|
|群内通知部署完毕|  -   |-|


执行脚本


```
更新公告创建人
php timer_yii script/reload-announcement-creator-name
```

```
更新公告和职位的下线时间
php timer_yii script/update-offline-time
```