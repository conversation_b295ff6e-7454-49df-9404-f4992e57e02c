# 用户消息通知设置保存恢复方案

**版本**: V2.7.2  
**创建时间**: 2025-07-29  
**文档状态**: 实现完成

## 1. 需求背景

在求职者注销功能的预注销流程中，系统会立即关闭用户的所有消息通知设置。为了支持用户在冷静期内撤回注销申请时能够恢复到原始的消息通知状态，需要在预注销时保存用户的原始消息通知设置，并在撤回时进行精确恢复。

## 2. 涉及的消息通知设置

### 2.1 需要保存和恢复的字段

预注销时会关闭以下5个消息通知设置：

- `is_job_feedback` → `BaseResumeSetting::IS_JOB_FEEDBACK_NO` (2)
- `is_system_message` → `BaseResumeSetting::IS_SYSTEM_MESSAGE_NO` (2)
- `is_todo_notice` → `BaseResumeSetting::IS_TODO_NOTICE_NO` (2)
- `is_job_invite` → `BaseResumeSetting::IS_JOB_INVITE_NO` (2)
- `is_company_view_me` → `BaseResumeSetting::IS_COMPANY_VIEW_ME_NO` (2)

### 2.2 字段默认值

消息通知字段的默认值均为开启状态：
- `is_job_feedback` = `IS_JOB_FEEDBACK_YES` (1)
- `is_system_message` = `IS_SYSTEM_MESSAGE_YES` (1)
- `is_todo_notice` = `IS_TODO_NOTICE_YES` (1)
- `is_job_invite` = `IS_JOB_INVITE_YES` (1)
- `is_company_view_me` = `IS_COMPANY_VIEW_ME_YES` (1)

## 3. 数据库设计方案

### 3.1 使用现有字段

**使用 `resume_cancel_log` 表中现有的 `resume_setting` 字段**：

- 该字段原本用于保存用户设置信息
- 现在复用该字段来保存用户的原始消息通知设置
- 无需新增数据库字段，直接使用现有表结构

### 3.2 字段说明

- **字段名称**：`resume_setting`（现有字段）
- **数据类型**：`TEXT`
- **存储格式**：JSON格式
- **用途**：保存用户在申请注销前的原始消息通知设置，用于撤回时恢复
- **优势**：复用现有字段，无需数据库结构变更

## 4. JSON数据结构

### 4.1 数据结构

```json
{
  "is_job_feedback": 1,
  "is_system_message": 1,
  "is_todo_notice": 1,
  "is_job_invite": 1,
  "is_company_view_me": 1
}
```

### 4.2 数据结构说明

直接保存消息通知的5个字段值：
- **is_job_feedback**：求职反馈通知开关
- **is_system_message**：系统消息通知开关
- **is_todo_notice**：待办通知开关
- **is_job_invite**：职位邀请通知开关
- **is_company_view_me**：谁看过我通知开关

## 5. 核心业务逻辑

### 5.1 保存原始消息通知设置

```php
/**
 * 保存用户原始消息通知设置
 * @param int $resumeId
 * @param int $memberId
 * @param BaseResumeCancelLog $cancelLog
 * @return array
 */
private function saveOriginalSettings($resumeId, $memberId, $cancelLog)
{
    // 获取用户原始消息通知设置
    $resumeSettings = $this->getResumeSettings($resumeId);
    
    // 保存到现有的 resume_setting 字段
    $cancelLog->resume_setting = json_encode($resumeSettings, JSON_UNESCAPED_UNICODE);
    $cancelLog->save();
    
    return $resumeSettings;
}
```

### 5.2 恢复原始消息通知设置

```php
/**
 * 恢复用户原始消息通知设置
 * @param BaseResumeCancelLog $cancelLog
 * @return bool
 */
private function restoreOriginalSettings($cancelLog)
{
    // 从 resume_setting 字段读取原始设置
    $originalSettings = json_decode($cancelLog->resume_setting, true);
    
    // 恢复消息通知设置
    $this->restoreResumeSettings($cancelLog->resume_id, $originalSettings);
    
    return true;
}
```

## 6. 技术实现要点

### 6.1 方法签名变更

**原方法**：
```php
public function executePreCancelOperations($resume, $member)
```

**新方法**：
```php
public function executePreCancelOperations($resume, $member, $cancelLog)
```

**变更原因**：需要传入 `$cancelLog` 对象来保存原始消息通知设置

### 6.2 调用点更新

**ResumeCancelService.php 第172行**：
```php
// 原调用
$dataCleanService->executePreCancelOperations($resume, $member);

// 新调用
$dataCleanService->executePreCancelOperations($resume, $member, $cancelLog);
```

### 6.3 恢复逻辑优化

**新恢复方法**：
```php
public function restorePreCancelOperations($cancelLog)
```

**优势**：基于保存在 `resume_setting` 字段中的JSON数据进行精确恢复

## 7. 处理策略

### 7.1 消息通知设置恢复

**策略**：精确恢复到用户申请注销前的原始状态

**实现**：
- 从 `resume_setting` 字段的JSON中读取原始设置值
- 逐一恢复5个消息通知字段
- 如果JSON数据缺失，使用默认开启状态

### 7.2 其他设置处理

**人才库状态**：撤回时重新评估人才库资格，不依赖保存的状态
**职位订阅**：不自动恢复，需要用户手动重新设置
**微信绑定**：不自动恢复，需要用户手动重新绑定

## 8. 错误处理与容错

### 8.1 数据完整性检查

- JSON解析失败时抛出明确异常
- 关键数据缺失时使用默认开启状态
- 数据库操作失败时回滚事务

### 8.2 日志记录

- 保存原始消息通知设置成功/失败日志
- 恢复原始消息通知设置成功/失败日志

## 9. 部署注意事项

### 9.1 代码部署

- 需要同时更新 `ResumeCancelService` 和 `ResumeCancelDataCleanService`
- 无需数据库结构变更，使用现有字段

### 9.2 向后兼容

- 复用现有字段，不影响现有功能
- 对于没有原始设置的历史数据，使用默认开启状态

## 10. 总结

本方案通过复用 `resume_cancel_log` 表中现有的 `resume_setting` 字段，以JSON格式保存用户的原始消息通知设置，实现了预注销时的设置保存和撤回时的精确恢复。方案具有以下优势：

1. **无需数据库变更**：复用现有字段，避免数据库结构修改
2. **精确恢复**：确保用户撤回注销后消息通知状态完全一致
3. **简洁高效**：只保存必要的消息通知设置，减少存储开销
4. **向后兼容**：不影响现有功能，平滑升级

该方案已完成代码实现，可以直接进入测试和部署阶段。
