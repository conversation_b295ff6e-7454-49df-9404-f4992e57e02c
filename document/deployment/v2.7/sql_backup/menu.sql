INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (126, '2025-05-13 09:00:44', '0000-00-00 00:00:00', 1, '内容发布', 0, 1, 'add', 0);
INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (127, '2025-05-13 09:04:11', '0000-00-00 00:00:00', 1, '新增公告', 126, 2, 'announcementAdd', 0);
INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (128, '2025-05-13 09:04:32', '0000-00-00 00:00:00', 1, '新增职位', 126, 2, 'jobAdd', 0);
INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (129, '2025-05-13 09:12:34', '0000-00-00 00:00:00', 1, '职位审核', 26, 2, 'cmsJobAuditList', 0);
INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (130, '2025-05-13 09:19:02', '0000-00-00 00:00:00', 1, '广告管理', 0, 1, 'advertising', 0);
INSERT INTO `admin_menu` (`id`, `add_time`, `update_time`, `status`, `name`, `parent_id`, `level`,
                          `key`, `is_route`)
VALUES (132, '2025-05-13 09:48:13', '0000-00-00 00:00:00', 1, '发布资讯', 131, 2, 'addNews', 0);


UPDATE `admin_menu`
SET `add_time`    = '2021-11-11 14:42:12',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '栏目管理',
    `parent_id`   = 45,
    `level`       = 2,
    `key`         = 'cmsColumnList',
    `is_route`    = 0
WHERE `id` = 39;
UPDATE `admin_menu`
SET `add_time`    = '2021-11-12 22:09:38',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '栏目管理',
    `parent_id`   = 45,
    `level`       = 2,
    `key`         = 'column',
    `is_route`    = 0
WHERE `id` = 41;

UPDATE `admin_menu`
SET `add_time`    = '2021-10-27 19:44:12',
    `update_time` = '2021-10-27 19:44:12',
    `status`      = 1,
    `name`        = '公告查询',
    `parent_id`   = 26,
    `level`       = 2,
    `key`         = 'cmsAnnouncementList',
    `is_route`    = 0
WHERE `id` = 27;

UPDATE `admin_menu`
SET `add_time`    = '2021-12-21 09:48:12',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '职位管理',
    `parent_id`   = 26,
    `level`       = 2,
    `key`         = 'cmsJobList',
    `is_route`    = 0
WHERE `id` = 60;


UPDATE `admin_menu`
SET `add_time`    = '2021-11-15 09:51:22',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '广告查询',
    `parent_id`   = 130,
    `level`       = 2,
    `key`         = 'cmsAdvertisingList',
    `is_route`    = 0
WHERE `id` = 43;
UPDATE `admin_menu`
SET `add_time`    = '2021-11-15 17:29:12',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '广告位管理',
    `parent_id`   = 130,
    `level`       = 2,
    `key`         = 'cmsAdvertisingPositionList',
    `is_route`    = 0
WHERE `id` = 44;
UPDATE `admin_menu`
SET `add_time`    = '2023-07-04 18:03:08',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '小程序广告管理',
    `parent_id`   = 130,
    `level`       = 2,
    `key`         = 'cmsMiniAdvertisingList',
    `is_route`    = 0
WHERE `id` = 86;
UPDATE `admin_menu`
SET `add_time`    = '2024-07-30 16:30:06',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '高才海外广告管理',
    `parent_id`   = 130,
    `level`       = 2,
    `key`         = 'abroadAdvertising',
    `is_route`    = 0
WHERE `id` = 110;
UPDATE `admin_menu`
SET `add_time`    = '2024-11-11 18:41:23',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '高才博士后广告管理',
    `parent_id`   = 130,
    `level`       = 2,
    `key`         = 'cmsBoShiHouList',
    `is_route`    = 0
WHERE `id` = 116;

UPDATE `admin_menu`
SET `add_time`    = '2024-07-30 16:29:41',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '活动管理',
    `parent_id`   = 0,
    `level`       = 1,
    `key`         = 'abroad',
    `is_route`    = 0
WHERE `id` = 108;
UPDATE `admin_menu`
SET `add_time`    = '2024-07-30 16:29:54',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '活动查询',
    `parent_id`   = 108,
    `level`       = 2,
    `key`         = 'abroadActivity',
    `is_route`    = 0
WHERE `id` = 109;
UPDATE `admin_menu`
SET `add_time`    = '2025-03-25 18:43:15',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '专场查询',
    `parent_id`   = 108,
    `level`       = 2,
    `key`         = 'specialActivity',
    `is_route`    = 0
WHERE `id` = 118;

UPDATE `admin_menu`
SET `add_time`    = '2021-12-13 19:46:24',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '资讯管理',
    `parent_id`   = 131,
    `level`       = 2,
    `key`         = 'cmsNewsList',
    `is_route`    = 0
WHERE `id` = 53;
UPDATE `admin_menu`
SET `add_time`    = '2021-12-14 17:17:07',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '添加资讯',
    `parent_id`   = 131,
    `level`       = 2,
    `key`         = 'cmsNewsAdd',
    `is_route`    = 0
WHERE `id` = 54;
UPDATE `admin_menu`
SET `add_time`    = '2021-12-16 10:20:06',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '编辑资讯',
    `parent_id`   = 131,
    `level`       = 2,
    `key`         = 'cmsNewsEdit',
    `is_route`    = 0
WHERE `id` = 55;
UPDATE `admin_menu`
SET `add_time`    = '2021-12-17 09:47:16',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '话题管理',
    `parent_id`   = 131,
    `level`       = 2,
    `key`         = 'cmsTopic',
    `is_route`    = 0
WHERE `id` = 57;

UPDATE `admin_menu`
SET `add_time`    = '2023-09-14 16:21:34',
    `update_time` = '0000-00-00 00:00:00',
    `status`      = 1,
    `name`        = '友情链接',
    `parent_id`   = 75,
    `level`       = 2,
    `key`         = 'configurationFriendLinkConfig',
    `is_route`    = 0
WHERE `id` = 91;
