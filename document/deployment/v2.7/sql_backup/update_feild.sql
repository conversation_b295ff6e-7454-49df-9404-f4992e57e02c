UPDATE job_handle_log AS jhl
    left JOIN job AS j ON jhl.job_id = j.id
SET jhl.announcement_id = j.announcement_id;

UPDATE job_apply_record_extra AS ja
    left JOIN job AS j ON ja.job_id = j.id
SET ja.announcement_id = j.announcement_id;


UPDATE job AS j
    left JOIN company AS c ON j.company_id = c.id
SET j.is_job_cooperation = c.is_cooperation;

UPDATE announcement AS a
    left JOIN company AS c ON a.company_id = c.id
SET a.is_announcement_cooperation = c.is_cooperation;