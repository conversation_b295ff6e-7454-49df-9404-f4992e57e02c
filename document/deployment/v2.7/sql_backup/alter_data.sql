-- ========================
-- 1. 修改 announcement 表
-- ========================
ALTER TABLE `announcement`
    ADD COLUMN `is_announcement_cooperation` TINYINT(2)  NOT NULL DEFAULT 2 COMMENT '单位的合作情况;2非合作单位，1已合作单位',
    ADD COLUMN `audit_admin_id`              INT(11)     NOT NULL DEFAULT 0 COMMENT '审核人ID',
    ADD COLUMN `audit_admin_name`            VARCHAR(60) NOT NULL DEFAULT '' COMMENT '审核人名称',
    ADD COLUMN `apply_id_type`               TINYINT(2)  NOT NULL DEFAULT 2 COMMENT '申请人类型：1=单位，2=运营',
    ADD COLUMN `apply_id`                    INT(11)     NOT NULL DEFAULT 0 COMMENT '申请人ID',
    ADD COLUMN `apply_name`                  VARCHAR(60) NOT NULL DEFAULT '' COMMENT '申请人名称',
    ADD COLUMN `creator_name`                VARCHAR(60) NOT NULL DEFAULT '' COMMENT '创建人名称',
    ADD INDEX `idx_audit_admin_id` (`audit_admin_id`) USING BTREE,
    ADD INDEX `idx_apply_id` (`apply_id`) USING BTREE;

-- ========================
-- 2. 修改 job 表
-- ========================
ALTER TABLE `job`
    ADD COLUMN `is_job_cooperation` TINYINT(2)  NOT NULL DEFAULT 2 COMMENT '单位的合作情况;2非合作单位，1已合作单位',
    ADD COLUMN `audit_admin_id`     INT(11)     NOT NULL DEFAULT 0 COMMENT '审核人ID',
    ADD COLUMN `audit_admin_name`   VARCHAR(60) NOT NULL DEFAULT '' COMMENT '审核人名称',
    ADD COLUMN `apply_id_type`      TINYINT(2)  NOT NULL DEFAULT 2 COMMENT '申请人类型：1=单位，2=运营',
    ADD COLUMN `apply_id`           INT(11)     NOT NULL DEFAULT 0 COMMENT '申请人ID',
    ADD COLUMN `apply_name`         VARCHAR(60) NOT NULL DEFAULT '' COMMENT '申请人名称',
    MODIFY COLUMN `duty` VARCHAR(2300) NOT NULL DEFAULT '' COMMENT '岗位职责',
    MODIFY COLUMN `requirement` VARCHAR(2300) NOT NULL DEFAULT '' COMMENT '任职要求',
    MODIFY COLUMN `remark` VARCHAR(2300) NOT NULL DEFAULT '' COMMENT '其他说明',
    ADD INDEX `idx_real_refresh_time` (`real_refresh_time`),
    ADD INDEX `idx_audit_admin_id` (`audit_admin_id`) USING BTREE,
    ADD INDEX `idx_apply_id` (`apply_id`) USING BTREE,
    ADD INDEX `idx_company_is_cooperation` (`is_job_cooperation`, `status`, `audit_status`, `create_type`,
                                            `announcement_id`) USING BTREE;

-- ========================
-- 3. 修改 job_apply_record_extra 表
-- ========================
ALTER TABLE `job_apply_record_extra`
    ADD COLUMN `announcement_id` INT(11) NOT NULL DEFAULT 0 COMMENT '公告ID' AFTER `education_doctor`,
    ADD INDEX `idx_total` (`total`) USING BTREE,
    ADD INDEX `idx_announcement_id` (`announcement_id`) USING BTREE;

-- ========================
-- 4. 创建 job_log 表
-- ========================
CREATE TABLE IF NOT EXISTS `job_log`
(
    `id`              INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `update_time`     DATETIME         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `add_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_type`     TINYINT(2)       NOT NULL DEFAULT '1' COMMENT '类型1=运营；2=单位',
    `create_id`       INT(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_name`     VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '创建人名称：运营记录运营名称；单位记录单位名称',
    `remark`          VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `route`           VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '控制器路由',
    `type`            INT(11)          NOT NULL COMMENT '操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型；例如：刷新1001；批量刷新1002)：1001=添加；1002=批量添加；1101=编辑；',
    `job_id`          INT(11)          NOT NULL COMMENT '职位ID',
    `announcement_id` INT(11)          NOT NULL COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_create_id` (`create_id`) USING BTREE,
    KEY `idx_create_type` (`create_type`) USING BTREE,
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- ========================
-- 5. 创建 announcement_log 表
-- ========================
CREATE TABLE IF NOT EXISTS `announcement_log`
(
    `id`              INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `update_time`     DATETIME         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `add_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_type`     TINYINT(2)       NOT NULL DEFAULT '1' COMMENT '类型1=运营；2=单位',
    `create_id`       INT(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_name`     VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '创建人名称：运营记录运营名称；单位记录单位名称',
    `route`           VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '控制器路由',
    `type`            INT(11)          NOT NULL COMMENT '操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型)：1001=添加；1002=批量添加；1101=编辑；',
    `remark`          VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `announcement_id` INT(11)          NOT NULL COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_create_id` (`create_id`) USING BTREE,
    KEY `idx_create_type` (`create_type`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;