<?php

namespace miniApp\models;

use common\base\models\BaseChatMessage;
use common\base\models\BaseChatMessageFile;
use common\base\models\BaseFile;

class ChatMessage extends BaseChatMessage
{
    /**
     * 修改上传文件的名称
     * @param $fileId
     * @param $trueName
     * @return void
     */
    public static function changeFileName($fileId, $trueName)
    {
        $fileModel = BaseFile::findOne($fileId);
        if ($fileModel) {
            $fileModel->name = $trueName;
            $fileModel->save();
        }
        $chatMessageFileModel = BaseChatMessageFile::findOne(['file_id' => $fileId]);
        if ($chatMessageFileModel) {
            $chatMessageFileModel->file_name = $trueName;
            $chatMessageFileModel->save();
        }
    }

}