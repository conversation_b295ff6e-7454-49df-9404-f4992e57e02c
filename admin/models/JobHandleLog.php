<?php

namespace admin\models;

use common\base\models\BaseJobHandleLog;
use common\helpers\TimeHelper;
use Yii;
use yii\base\Exception;

class JobHandleLog extends BaseJobHandleLog
{
    /**
     * 根据条件获取职位操作列表
     * @param $keywords
     * @return array
     */
    public static function getJobHandleList($keywords): array
    {
        $select  = [
            'id',
            'add_time',
            'ip',
            'handler_name',
            'handle_before',
            'handle_after',
            'handle_type',
        ];
        $where   = ['and'];
        $where[] = ['job_id' => $keywords['job_id']];
        $query   = self::find()
            ->select($select)
            ->where($where);
        $query->andFilterCompare('handler_name', $keywords['handler_name'], 'like');
        if ($keywords['handle_time_start']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['handle_time_start']),
            ]);
        }
        if ($keywords['handle_time_end']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['handle_time_end']),
            ]);
        }
        $orderBy = ' add_time desc';

        $count         = $query->count();
        $pageSize      = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages         = self::setPage($count, $keywords['page'], $pageSize);
        $jobHandleList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($jobHandleList as $key => $list) {
            $jobHandleList[$key]['id']            = (int)$list['id'];
            $jobHandleList[$key]['handleBefore']  = json_decode('[' . $list['handle_before'] . ']', true);
            $jobHandleList[$key]['handleAfter']   = json_decode('[' . $list['handle_after'] . ']', true);
            $jobHandleList[$key]['handleTypeTitle'] = BaseJobHandleLog::HANDLE_TYPE_LIST[$list['handle_type']];
        }

        return [
            'list' => $jobHandleList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }
}
