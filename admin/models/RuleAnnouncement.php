<?php

namespace admin\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseResumeEducation;
use Exception;
use Faker\Provider\Base;

/**
 * 小程序公告规则调用
 * --------
 * 采用单个公告验证，验证规则如下
 * 2.1、合作单位的全量公告；
 * 2.2、除2.1以外的其他高质量公告（以下标准符合其一即可）：
 * ① 单位类型为双一流院校、普通本科院校、高职高专院校、党校与行政学院、机关单位、中小学、中专&职业中学&技师学院、中科院系统、人文社科研究机构、自然与应用科研机构、企业研发机构、医院全量公告；
 * ② 公告最低学历要求为“硕士研究生及以上” 或 “博士研究生及以上”；
 * ③ 有配置滚动、推荐、首页置顶、栏目置顶、首头1/2、栏目头条、焦点、首页属性的公告；
 * ④ 其他在运营平台手动标记的公告；运营平台标记优先级最高；
 */
class RuleAnnouncement
{
    const COMPANY_TYPE = [
        //双一流院校
        1,
        //普通本科院校
        2,
        //高职高专院校
        3,
        //党校与行政学院
        4,
        //机关单位
        5,
        //军队武警
        //        6 ,
        //事业单位
        //        7 ,
        //政府国有企业
        //        8,
        //知名企业
        //        9  ,
        //中小成长型企业
        //        10 ,
        //银行、信用社等金融机构
        //        11 ,
        //教育系统
        //        12 ,
        //中小学
        13,
        //中专&职业中学&技师学院
        14,
        //幼儿园
        //        15 ,
        //中科院系统
        16,
        //人文社科研究机构（事业单位类型）
        17,
        //自然与应用科研机构（事业单位类型）
        18,
        //企业研发机构
        19,
        //卫健委
        //20 ,
        //医院
        21,
        //其他医疗机构
        //22 ,
    ];
    //  滚动、推荐、首页置顶、栏目置顶、首头1/2、栏目头条、焦点、首页”
    const ATTR_ID = [
        //滚动
        BaseArticleAttribute::ATTRIBUTE_ROLLING,
        //推荐
        BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
        //首页置顶
        BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
        //栏目置顶
        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
        //首头1
        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
        //首头2
        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
        //首头3
        //        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_THREE,
        //首页
        BaseArticleAttribute::ATTRIBUTE_HOME,
        //栏目头条
        BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
        //焦点
        BaseArticleAttribute::ATTRIBUTE_FOCUS,
        //编制
        //        BaseArticleAttribute::ATTRIBUTE_COMPILE,
    ];
    private $announcementId;//公告ID
    private $announcementInfo;//公告信息
    private $announcementCompanyInfo;//公告所属单位信息

    /**
     * 公告校验是否允许小程序显示
     * @param $announcementId
     * @throws Exception
     */
    public function exec($announcementId)
    {
        //判断参数是否为空及合法性
        if (empty($announcementId) || $announcementId <= 0) {
            throw new Exception('参数错误,校验请传入公告ID!');
        }
        //获取公告信息
        $this->announcementId   = $announcementId;
        $this->announcementInfo = BaseAnnouncement::findOne($announcementId);
        if (empty($this->announcementInfo)) {
            throw new Exception('公告信息不存在!');
        }
        //职位所属单位信息
        $this->announcementCompanyInfo = BaseCompany::findOne($this->announcementInfo->company_id);
        //2.1、校验--合作单位的全量公告
        $step_1 = $this->verifyCooperation();
        if ($step_1) {
            return true;
        } else {
            //2.2、校验
            return $this->verifyHighQuality();
        }
    }

    /**
     * 校验公告是合作单位还是非合作单位
     * @return bool
     */
    private function verifyCooperation()
    {
        if ($this->announcementCompanyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 校验非合作单位职位
     * 2.2、除2.1以外的其他高质量公告（以下标准符合其一即可）：
     * ① 单位类型为双一流院校、普通本科院校、高职高专院校、党校与行政学院、政府国有企业、人文社科研究机构、自然与应用科研机构、中科院系统、企业研发机构、医院 等全量公告；
     * ② 公告最低学历要求为“硕士研究生及以上” 或 “博士研究生及以上”；
     * ③ 有配置“推荐”、“焦点”、“首页”、“滚动”、“首头1/2/3”、“首页置顶”、“栏目头条”、“栏目置顶”、编制属性的公告；
     * ④ 其他在运营平台手动标记的公告；运营平台标记优先级最高；
     * @return bool
     */
    private function verifyHighQuality()
    {
        //本科及以上
        $education_umd = [
            BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
            BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
            BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
        ];
        //硕博
        $education_md = [
            BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
            BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
        ];
        //① 单位类型为双一流院校、普通本科院校、高职高专院校、党校与行政学院、政府国有企业、人文社科研究机构、自然与应用科研机构、中科院系统、企业研发机构、医院 等全量公告；
        if (in_array($this->announcementCompanyInfo->type, self::COMPANY_TYPE)) {
            return true;
        }
        //② 公告最低学历要求为“硕士研究生及以上” 或 “博士研究生及以上”，需要下沉到职位层面进行判断；
        $job_data           = BaseJob::find()
            ->select('id,education_type')
            ->where(['announcement_id' => $this->announcementInfo->id])
            ->asArray()
            ->all();
        $job_education_bool = true;
        foreach ($job_data as $value) {
            if (!in_array($value['education_type'], $education_md)) {
                $job_education_bool = false;
                break;
            }
        }
        if ($job_education_bool) {
            return true;
        }
        //③ 有配置“推荐”、“焦点”、“首页”、“滚动”、“首头1/2/3”、“首页置顶”、“栏目头条”、“栏目置顶”、编制属性的公告；
        $announcement_attr = BaseArticleAttribute::find()
            ->select('id')
            ->where(['article_id' => $this->announcementInfo->article_id])
            ->andWhere([
                'in',
                'type',
                self::ATTR_ID,
            ])
            ->one();
        if ($announcement_attr) {
            return true;
        }

        //④其他在运营平台手动标记的公告；运营平台标记优先级最高；
        if ($this->announcementInfo->is_manual_tag == BaseAnnouncement::IS_MANUAL_TAG_YES) {
            return true;
        }

        return false;
    }
}