<?php

namespace admin\controllers;

use admin\models\Resume;
use common\libs\ResumeHandle;
use common\service\meilisearch\resume\ResumeLibraryDocumentService;
use Yii;

class ResumeController extends BaseAdminController
{
    /**
     * 简历下载
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDownload()
    {
        $adminId         = Yii::$app->user->id;
        $resumeId        = Yii::$app->request->get('resumeId');
        $attachmentToken = Yii::$app->request->get('attachmentToken');
        if (!$resumeId) {
            return $this->fail();
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $resumeDown = new ResumeHandle();
            $resumeDown->setOperator($adminId, ResumeHandle::OPERATOR_TYPE_ADMIN)
                ->setData($resumeId, $attachmentToken)
                ->download();

            $transaction->commit();
            exit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑标签
     * @return Response|\yii\web\Response
     */
    public function actionEditTag()
    {
        $params = Yii::$app->request->post();
        if (empty($params['resumeId'])) {
            return $this->fail('参数错误');
        }
        try {
            Resume::editTag($params['resumeId'], $params['tagIds']);

            return $this->success();
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionBatchAddResumeTag()
    {
        $params = Yii::$app->request->post();
        if (empty($params['resumeIds'])) {
            return $this->fail('参数错误');
        }
        try {
            Resume::addResumeTag($params['resumeIds'], $params['tagIds']);

            return $this->success();
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 新增标签
     * @return Response|\yii\web\Response
     */
    public function actionAddTag()
    {
        $params = Yii::$app->request->post();
        $tag    = trim($params['tag']);
        if (empty($tag)) {
            return $this->fail('请输入标签名称');
        }

        if (mb_strlen($tag) > 20) {
            return $this->fail('标签名称不能超过10个字');
        }

        try {
            Resume::addTag($tag, Yii::$app->user->id);

            return $this->success();
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // 调用meilisearch的模糊搜索
    public function actionSearchV2()
    {
        $service = new ResumeLibraryDocumentService();
        $return  = $service->setKeyword(Yii::$app->request->get('keyword'))
            ->originalSearch();

        $this->success(['list' => $return]);
    }

}