<?php

namespace admin\controllers;

use admin\models\CompanyDeliveryChangeLog;
use common\base\models\BaseCompany;
use common\helpers\ArrayHelper;
use yii\console\Response;

class CompanyDeliveryChangeLogController extends BaseAdminController
{
    /**
     * 修改单位投递类型生成单位投递类型修改日志
     * @return Response|\yii\web\Response
     */
    public function actionAdd(){
        try {
            return $this->success(CompanyDeliveryChangeLog::add(),'变更成功！');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }


    /**
     * 单位修改日志列表
     * @return Response|\yii\web\Response
     */
    public function actionList(){
        try {
            return $this->success(CompanyDeliveryChangeLog::list());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取筛选项配置
     * @return Response|\yii\web\Response
     */
    public function actionSearch(){
        //获取投递类型配置
        $deliveryArr=BaseCompany::DELIVERY_TYPE_NAME;
        $result=[
            'delivery_list'=>ArrayHelper::obj2Arr($deliveryArr)
        ];
        return $this->success($result);
    }
}