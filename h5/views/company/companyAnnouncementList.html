<link rel="stylesheet" href="/static/css/companyDetail.min.css?v=20241009" />
<script src="//img.gaoxiaojob.com/uploads/h5Static/lib/previewImage/previewImage.min.js"></script>

<?= h5\components\CompanyDetailBannerWidget::widget(['companyId'=>$info['companyId']]) ?>

<div class="content">
    <?= h5\components\CompanyDetailNavWidget::widget(['companyId'=>$info['companyId']]) ?>

    <div class="tab-content search-container notice is-active">
        <div class="select-nav">
            <div class="item">
                <span id="noticeArea">地区</span>
                <span id="noticeJob">职位类型</span>
                <span id="notice-majorId">专业</span>
            </div>
            <span class="clear-btn">清除筛选</span>
        </div>
        <ul class="position-style">
            <?php foreach($info['announcementList'] as $announcement):?>
            <li class="element <?php if($announcement['status'] == 2):?>position-invalid<?php endif?>">
                <a href="<?=$announcement['url']?>">
                    <div class="position">
                        <span class="job-name"><?=$announcement['title']?></span>
                    </div>
                    <div class="required">
                        <span>共<?=$announcement['jobAmount']?>个职位</span>
                        <span>招<?=$announcement['recruitAmount']?:0?>人</span>
                    </div>
                    <div class="time"><?= $announcement['refreshTime']?>发布</div>
                </a>
            </li>
            <?php endforeach?>
        </ul>
        <div class="nothing" style="display: <?=$info['announcementList']?'none':''?>;">
            <img src="/static/assets/layout/nothing.png" alt="" />
            <p>暂无相关内容</p>
        </div>
    </div>

    <div class="weui-loadmore" style="display: none">
        <i class="weui-loading"></i>
        <span class="weui-loadmore__tips">正在加载</span>
    </div>

<!--    <div class="weui-loadmore weui-loadmore_line" style="display: none">-->
<!--        <span class="weui-loadmore__tips">暂无数据</span>-->
<!--    </div>-->
</div>

<script>
    $(function () {
        /* userStatus: 0 -> 未登录; 1 -> 未完成简历前三步; 2 -> 可以投递 */
        var userStatus = <?=$info['userStatus'] ?>;
        var companyId = <?=$info['companyId']?>;

        function openSignupPopup() {
            window.signupPopup.show()
        }

        var $followBtn = $('.follow')

        var $nav = $('nav')
        var $content = $('.content')

        var $selectBtn = $('.select-nav span')
        var $tabContent = $('.tab-content')
        var $clearBtn = $('.clear-btn')

        var $header = $('.header-container')
        var $container = $('.main-container')
        var $loading = $('.weui-loadmore')
        var loading = false
        var $positionList = $('.position-style')
        var $nothing = $('.nothing')

        var contentMarginTop = $content.offset().top - $header.offset().height

        var noticeAreaSelector = new MobileSelect({
            trigger: '#noticeArea',
            title: '地区',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['announceCityList'] as $k=>$city):?>
                        {
                            id: '<?= $city["k"]?>',
                                value: '<?= $city["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.areaId = data.pop().id
                refetchData()
            }
        })

        var noticeJobSelector = new MobileSelect({
            trigger: '#noticeJob',
            title: '职业类型',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['announceJobCategoryList'] as $jobCategory):?>
                        {
                            id: '<?= $jobCategory["k"]?>',
                                value: '<?= $jobCategory["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.jobCategoryId = data.pop().id
                refetchData()
            }
        })

        var noticeMajorIdSelector = new MobileSelect({
            trigger: '#notice-majorId',
            title: '专业',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['majorList'] as $major):?>
                        {
                            id: '<?= $major["k"]?>',
                                value: '<?= $major["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.majorId = data.pop().id
                refetchData()
            }
        })

        // 公告
        var noticeQuery = {
            areaId: '',
            jobCategoryId: '',
            majorId: '',
            page: 1
        }

        function resetText(txt) {
            var indexLength = this.curIndexArr.length
            this.trigger.innerText = txt

            if (indexLength > 1) {
                this.checkRange(0, [])
            } else {
                this.setCurDistance(this.resetPosition(0, []))
            }
        }

        var defaultText = [
            {
                noticeArea: function () {
                    resetText.call(noticeAreaSelector, '地区')
                },
                noticeJob: function () {
                    resetText.call(noticeJobSelector, '职位类型')
                },
                noticeMajor: function () {
                    resetText.call(noticeMajorIdSelector, '专业')
                }
            }
        ]

        // tab栏吸顶 start
        $container.on('scroll', function () {
            var isFixed = $content.offset().top - $nav.height() <= 15

            if (isFixed) {
                $nav.addClass('nav-fixed')
            } else {
                $nav.removeClass('nav-fixed')
            }
        })
        // tab栏吸顶 end

        function renderList(data) {
            return data.reduce((previous, current) => {
                const { title, refreshTime, jobAmount, recruitAmount, url, status } = current
                const noticeStatus = status == 2 ? ' position-invalid' : ''

                return (previous += `
                            <li class="element${noticeStatus}">
                                <a href="${url}">
                                    <div class="position">
                                        <span class="job-name">${title}</span>
                                    </div>
                                    <div class="required">
                                        <span>共${jobAmount}个职位</span>
                                        <span>招${recruitAmount}人</span>
                                    </div>
                                    <div class="time">${refreshTime}发布</div>
                                </a>
                            </li>
                        `)
            }, '')
        }

        function fetchData(status) {
            var isReplace = status === true
            var operate = isReplace ? 'html' : 'append'

            var query = noticeQuery
            var api = '/company/get-announcement-list'
            var params = { companyId, ...query, page: isReplace ? query.page : query.page + 1 }

            loading = true
            $loading.hide()
            $loading.eq(0).show()
            httpGet(api, params).then((data) => {
                $loading.hide()
                if (data.length) {
                    $positionList.eq(0)[operate](renderList(data))
                    noticeQuery.page += 1
                } else {
                    $positionList.eq(0)[operate]('')
                    // $loading.eq(1).show()
                }
                loading = data.length === 0
                if ($positionList.eq(0).children().length < 1 && status) {
                    $nothing.show()
                } else {
                    $nothing.hide()
                }
            })
        }

        function refetchData() {
            loading = false
            noticeQuery.page = 1
            fetchData(true)
        }

        /* 触底加载 */
        $container.infinite().on('infinite', function () {
            if (loading) return

            fetchData()
        })

        /* 点击关注按钮 */
        $followBtn.on('click', function () {
            if (userStatus === 0) {
                openSignupPopup()
                return
            }

            var isActive = $(this).hasClass('is-follow')
            httpPost('/company/collect', { companyId }).then(() => {
                $(this).toggleClass('is-follow')
            })
        })

        // 清除筛选项 start
        $clearBtn.on('click', function () {
            var trigger = defaultText[0]

            Object.keys(trigger).forEach(function (item) {
                trigger[item]()
            })

            for (var item in noticeQuery) {
                noticeQuery[item] = ''
            }

            refetchData()
        })
        // 清除筛选项 end
    })
</script>
