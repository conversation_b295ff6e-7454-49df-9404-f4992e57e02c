<?=@h5\components\HomeNavItemWidget::Widget()?>
<?=@h5\components\WxMiniChatWidget::Widget()?>
<?=@h5\components\WxMiniActivityWidget::Widget()?>
<script>
    $(function () {
        // 是否登录状态
        var hasLogin =<?=$isLogin?>
        // 简历完善第几步
        var resumeStep = <?=$stepNum?>;
        var $newHome = $('.to-new-home')
        var $navCon = $('.nav-con')

        $newHome.on('click', function () {
            $navCon.toggle()
            $('.placeholder-bg').toggle()
        })


        function linkJump(link) {
            window.location.href = link
        }
        function jumpResult(name) {
            switch (name) {
                case 'home':
                    linkJump('/')
                    break;
                case 'notice':
                    linkJump('/search')
                    break;
                case 'job':
                    linkJump('/job/search?searchType=2')
                    break;
                case 'msg':
                    hasLogin ? $('#openMiniAppDialog').fadeIn(100) : window.signupPopup.show()
                    break;
                case 'online':
                    if (hasLogin) {
                        if (resumeStep < 3) {
                            // 已登录未完善简历前三步，打开【简历待完善步骤页】；
                            linkJump('/resume/index')
                        } else {
                            // 已登录已完善简历前三步，打开【编辑简历】页面；
                            linkJump('/resume/edit')
                        }
                    } else {
                        window.signupPopup.show()
                    }
                    break;
                case 'invite':
                    if (hasLogin) {
                        if (resumeStep < 3) {
                            // 已登录未完善简历前三步，打开【简历待完善步骤页】；
                            linkJump('/resume/index')
                        } else {
                            // 已登录已完善简历前三步，点击，跳转【我的投递】页面
                            linkJump('/person/on-site-apply-list')
                        }
                    } else {
                        window.signupPopup.show()
                    }
                    break;
                case 'job-fair':
                    $('#openMiniAppDialog1').fadeIn(100)
                    break
                default:
                    break;
            }
        }

        $navCon.find('li').on('click', function () {
            var idName = $(this).attr('id');
            jumpResult(idName)
        })
    })
</script>