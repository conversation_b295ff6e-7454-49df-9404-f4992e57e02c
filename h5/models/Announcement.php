<?php

namespace h5\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use frontendPc\models\Article;
use yii\base\ViewRenderer;
use yii\helpers\Url;

class Announcement extends BaseAnnouncement
{
    const DETAIL_CACHE_TIME = 1800;

    /**
     * 这里专用顶部搜索栏搜索，条件比较简单，单独拎出来
     * @return array
     */
    public static function getSearchKeywordList($searchData)
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->where([
                'ar.status' => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES]);

        //关键词查询
        $query->andFilterWhere([
            'like',
            'an.title',
            $searchData['keyword'],
        ]);

        $query->select([
            'an.title',
            'ar.click as clickAmount',
            'ar.status',
            'an.id',
            'ar.refresh_time',
            'c.sort',
            'an.establishment_type as establishmentType',
            'an.highlights_describe as highlightsDescribe',
            // 2.4追加返回公告亮点
        ]);
        //        $sort = 'refresh_date desc,c.sort desc';
        $sort = 'ar.status asc, refresh_date desc,an.is_first_release asc,cgss.score desc,an.id desc';

        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($sort)
            ->asArray()
            ->all();

        foreach ($list as $k => &$announcement) {
            //获取公告下的职位数量
            $announcement['jobAmount'] = Job::getAnnouncementJobAmount($announcement['id']);
            //获取公告的职位招聘数量
            $announcement['recruitAmount'] = Job::getAnnouncementJobRecruitAmount($announcement['id']);

            $announcement['url']             = Url::toRoute([
                'announcement/detail',
                'id' => $announcement['id'],
            ]);
            $announcement['refreshTime']     = date('Y-m-d', strtotime($announcement['refresh_time']));
            $announcement['isEstablishment'] = BaseAnnouncement::IS_ESTABLISHMENT_NO;
            if (in_array($announcement['establishmentType'], BaseAnnouncement::IS_ESTABLISHMENT_TYPE_LIST)) {
                $announcement['isEstablishment'] = BaseAnnouncement::IS_ESTABLISHMENT_YES;
            }
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $pages['page'],
            'totalNum'    => $count,

        ];
    }

    /**
     * 用于查询公告
     * @param $searchData
     * @return array
     */
    public static function getSearchList($searchData)
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->where(['ar.status' => BaseArticle::STATUS_ACTIVE])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES]);

        //关键词查询
        $query->andFilterWhere([
            'like',
            'an.title',
            $searchData['keyword'],
        ]);

        //统计需要子查询的数量
        $childrenQueryNum = 0;
        //工作地点查询
        if (!empty($searchData['areaId'])) {
            $announcememtIdArr1 = Job::find()
                ->where(['status' => Job::STATUS_ACTIVE])
                ->andWhere(['district_id' => $searchData['areaId']])
                ->andWhere(['is_article' => Job::IS_ARTICLE_YES])
                ->select('announcement_id')
                ->groupBy('announcement_id')
                ->asArray()
                ->column();
            $childrenQueryNum   += 1;
        }

        //需求专业查询
        if (!empty($searchData['majorId'])) {
            $majorId            = $searchData['majorId'];
            $announcememtIdArr2 = Job::find()
                ->where(['status' => Job::STATUS_ACTIVE])
                ->andWhere(['is_article' => Job::IS_ARTICLE_YES])
                ->andWhere("find_in_set($majorId,major_id)")
                ->select('announcement_id')
                ->groupBy('announcement_id')
                ->asArray()
                ->column();
            $childrenQueryNum   += 1;
        }
        //学历查询
        if (!empty($searchData['educationId'])) {
            $announcememtIdArr3 = Job::find()
                ->where(['status' => Job::STATUS_ACTIVE])
                ->andWhere(['education_type' => $searchData['educationId']])
                ->andWhere(['is_article' => Job::IS_ARTICLE_YES])
                ->select('announcement_id')
                ->groupBy('announcement_id')
                ->asArray()
                ->column();
            $childrenQueryNum   += 1;
        }

        //职能
        if (!empty($searchData['jobCategoryId'])) {
            $announcememtIdArr4 = Job::find()
                ->where(['status' => Job::STATUS_ACTIVE])
                ->andWhere(['job_category_id' => $searchData['jobCategoryId']])
                ->andWhere(['is_article' => Job::IS_ARTICLE_YES])
                ->select('announcement_id')
                ->groupBy('announcement_id')
                ->asArray()
                ->column();
            $childrenQueryNum   += 1;
        }

        //统计id列表数组数量
        $intersectNum      = 0;
        $announcememtIdArr = [];
        if (count($announcememtIdArr1) > 0) {
            $intersectNum        += 1;
            $announcememtIdArr[] = $announcememtIdArr1;
        }
        if (count($announcememtIdArr2) > 0) {
            $intersectNum        += 1;
            $announcememtIdArr[] = $announcememtIdArr2;
        }
        if (count($announcememtIdArr3) > 0) {
            $intersectNum        += 1;
            $announcememtIdArr[] = $announcememtIdArr3;
        }
        if (count($announcememtIdArr4) > 0) {
            $intersectNum        += 1;
            $announcememtIdArr[] = $announcememtIdArr4;
        }
        //如果数组数量大于1，取交集
        if ($intersectNum > 1) {
            //如果数组超过2组数组元素，取交集
            $announcememtIdArr = call_user_func_array('array_intersect', $announcememtIdArr);
        } elseif ($intersectNum == 1) {
            //如果刚好等于1，取第一个
            $announcememtIdArr = $announcememtIdArr[0];
        }
        //否则不做处理，即空数组

        //如果子查询数量大于0，说明有查询，此时需把条件加入查询
        if ($childrenQueryNum > 0) {
            //联合三个条件，查询用户id
            $query->andWhere([
                'in',
                'an.id',
                $announcememtIdArr,
            ]);
        } else {
            $query->andFilterWhere([
                'in',
                'an.id',
                $announcememtIdArr,
            ]);
        }

        $query->select([
            'an.title',
            'an.article_id as articleId',
            'an.id as announcementId',
            'ar.release_time as releaseTime',
            'ar.click as clickAmount',
            'an.id as announcementId',
        ]);
        $sort = 'releaseTime desc';

        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($sort)
            ->asArray()
            ->all();

        foreach ($list as $k => &$announcement) {
            //获取公告下的职位数量
            $announcement['jobAmount'] = Job::getAnnouncementJobAmount($announcement['announcementId']);
            //获取公告的职位招聘数量
            $announcement['recruitAmount'] = Job::getAnnouncementJobRecruitAmount($announcement['announcementId']);

            $announcement['url'] = Url::toRoute([
                'announcement/detail',
                'id' => $announcement['announcementId'],
            ]);
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $pages['page'],
            'totalNum'    => $count,

        ];
    }

    /**
     * 获取首页首头文章id，用于往期头条剔除处理
     * @param $limit
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getIndexHeadId($limit)
    {
        $info = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'an.article_id = a.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = a.id')
            ->where([
                'in',
                'aa.type',
                [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                ],
            ])
            ->andWhere(['a.status' => self::STATUS_ACTIVE])
            ->groupBy(['aa.article_id'])
            ->limit($limit)
            ->select([
                'a.id',
            ])
            ->orderBy('aa.sort_time desc')
            ->asArray()
            ->all();
        $ids  = [];
        if (!empty($info)) {
            foreach ($info as $item) {
                array_push($ids, $item['id']);
            }
        }

        return $ids;
    }

    /**
     * 获取往期头条，需剔除首头文章的几篇
     * @param $limit
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getIndexTop($limit, $reject)
    {
        $rejectIds = self::getIndexHeadId($reject);

        $query = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'a.id = an.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = a.id')
            ->where([
                'in',
                'aa.type',
                [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_THREE,
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_FOUR,
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP_FIVE,
                ],
            ])
            ->andWhere(['a.status' => BaseArticle::STATUS_ACTIVE])
            ->andWhere(['a.is_show' => BaseArticle::IS_SHOW_YES])
            ->andWhere(['a.is_delete' => BaseArticle::IS_DELETE_NO]);

        //排除首头的两条
        if (!empty($rejectIds)) {
            foreach ($rejectIds as $rejectId) {
                $query->andWhere([
                    '<>',
                    'a.id',
                    $rejectId,
                ]);
            }
        }

        $list = $query->groupBy(['aa.article_id'])
            ->limit($limit)
            ->select([
                'an.id',
                'a.title',
                'a.refresh_time',
                'a.content',
                'a.seo_description',
                'a.seo_description',
                'aa.sort_time',
            ])
            ->orderBy('aa.add_time desc')
            ->asArray()
            ->all();
        foreach ($list as $k => &$v) {
            $v['url']  = Url::toRoute([
                '/announcement/detail',
                'id' => $v['id'],
            ]);
            $v['date'] = date('Y-m-d', strtotime($v['refresh_time']));
        }

        return $list;
    }

    public static function getListNoPage($searchData)
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'an.article_id = a.id')
            ->where(['a.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['a.status' => BaseArticle::STATUS_ACTIVE])
            ->andWhere(['a.is_show' => BaseArticle::IS_SHOW_YES]);

        $limit = \Yii::$app->params['newestAnnouncementPageSize'];

        $offset = ($searchData['page'] - 1) * $limit;

        $list = $query->limit($limit)
            ->offset($offset)
            ->select([
                'an.id',
                'an.title',
                'a.seo_description as seoDescription',
                'a.click as clickAmount',
                'a.cover_thumb as coverThumb',
                'a.refresh_time as refreshTime',
            ])
            ->orderBy('refreshTime')
            ->asArray()
            ->all();

        foreach ($list as &$v) {
            $v['url'] = Url::toRoute([
                '/announcement/detail',
                'id' => $v['id'],
            ]);
        }

        return $list;
    }

    /**
     * 获取属性\栏目的等信息的公告文章列表（可传入属性id（可以是数组），分页信息）
     * @param $searchData
     * @return array
     */

    public static function getList($searchData, $needSearchData = false, $needList = true): array
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'an.article_id = a.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = a.id')
            ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id = a.id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = an.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->where(['a.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['a.status' => BaseArticle::STATUS_ACTIVE])
            ->andWhere(['a.is_show' => BaseArticle::IS_SHOW_YES]);

        //查询属性
        // if (is_array($searchData['attribute'])) {
        //     $query->andFilterWhere([
        //         'in',
        //         'aa.type',
        //         $searchData['attribute'],
        //     ]);
        // } else {
        //     $query->andFilterWhere([
        //         'aa.type' => $searchData['attribute'],
        //     ]);
        // }

        $query->andWhere(['aa.type' => $searchData['attribute']]);
        //栏目查询
        if (!empty($searchData['columnId'])) {
            //如果有栏目的话，判断当前栏目是一级还是二级
            $columnLevel = BaseHomeColumn::findOneVal(['id' => $searchData['columnId']], 'level');
            if ($columnLevel == BaseHomeColumn::LEVEL_FIRST) {
                //查询当前一级栏目下的二级栏目
                $secondLevelColumnList = BaseHomeColumn::find()
                    ->where(['parent_id' => $searchData['columnId']])
                    ->select('id')
                    ->asArray()
                    ->all();
                $columnList            = array_values($secondLevelColumnList);
                //加上一级栏目id
                array_push($columnList, $searchData['columnId']);
                $query->andFilterWhere([
                    'in',
                    'ac.column_id',
                    $columnList,
                ]);
            } elseif ($columnLevel == BaseHomeColumn::LEVEL_SECOND) {
                $query->andFilterWhere(['ac.column_id' => $searchData['columnId']]);
            }
        }

        //发布时间查询
        if ($searchData['startReleaseTime']) {
            $query->andWhere([
                '>=',
                'a.refresh_time',
                TimeHelper::dayToBeginTime($searchData['startReleaseTime']),
            ]);
        }
        if ($searchData['endReleaseTime']) {
            $query->andWhere([
                '<=',
                'a.refresh_time',
                TimeHelper::dayToEndTime($searchData['endReleaseTime']),
            ]);
        }

        $query->groupBy(['aa.article_id']);

        if ($needList) {
            $count    = $query->count();
            $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['newestAnnouncementPageSize'];
            $pages    = self::setPage($count, $searchData['page'], $pageSize);

            $list = $query->limit($pages['limit'])
                ->offset($pages['offset'])
                ->select([
                    'an.id',
                    'an.title',
                    'a.seo_description as seoDescription',
                    'a.click as clickAmount',
                    'aa.sort_time',
                    'c.full_name as companyName',
                    'c.logo_url',
                    'a.cover_thumb as coverThumb',
                    'a.refresh_time as refreshTime',
                    'sort_time as sortTime',
                ])
                ->orderBy('aa.sort_time desc,aa.id desc')
                ->asArray()
                ->all();
        } else {
            if ($searchData['pageSize']) {
                $query->limit($searchData['pageSize']);
            }
            $list = $query->select([
                'an.id',
                'an.title',
                'a.seo_description as seoDescription',
                'a.click as clickAmount',
                'aa.sort_time',
                'c.full_name as companyName',
                'c.logo_url',
                'a.cover_thumb as coverThumb',
                'a.refresh_time as refreshTime',
                'sort_time as sortTime',
            ])
                ->orderBy('aa.sort_time desc,aa.id desc')
                ->asArray()
                ->all();
        }

        $allCityIdText  = '';
        $allMajorIdText = '';
        foreach ($list as &$v) {
            $v['url'] = Url::toRoute([
                '/announcement/detail',
                'id' => $v['id'],
            ]);

            if ($needSearchData) {
                //需要筛选项，再进行查询
                $jobInfo = BaseJob::getAnnouncementJobInfo($v['id']);

                //获取公告下的职位数量
                $v['jobAmount'] = $jobInfo['jobAmount'];
                //获取公告下职位招聘数量
                $v['recruitAmount'] = $jobInfo['recruitAmount'] ?: "若干";
                //拼接专业和城市
                $allCityIdText  .= $jobInfo['cityText'] . ',';
                $allMajorIdText .= $jobInfo['majorText'] . ',';
            }

            $v['refreshTime'] = substr($v['refreshTime'], 0, 10);
            $v['sortTime']    = substr($v['sortTime'], 0, 10);
            $v['companyLogo'] = BaseCompany::getLogoFullUrl($v['logo_url']);
            $v['coverThumb']  = FileHelper::getFullUrl($v['coverThumb']);
        }
        $cityList  = [];
        $majorList = [];
        if ($needSearchData) {
            //拼接页面的工作地点和学科专业筛选项数据
            //去除多余逗号
            $allCityIdText  = substr($allCityIdText, 0, -1);
            $allMajorIdText = substr($allMajorIdText, 0, -1);
            $allCityIdArr   = array_unique(explode(',', $allCityIdText));
            $allMajorIdArr  = array_unique(explode(',', $allMajorIdText));

            foreach ($allCityIdArr as $cityId) {
                $cityList[] = [
                    'k' => $cityId,
                    'v' => BaseArea::getAreaName($cityId),
                ];
            }
            foreach ($allMajorIdArr as $majorId) {
                $majorList[] = [
                    'k' => $majorId,
                    'v' => BaseMajor::getMajorName($majorId),
                ];
            }
        }

        return [
            'list'      => $list,
            'cityList'  => $cityList,
            'majorList' => $majorList,
            'page'      => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    public static function getHomeList()
    {
        $key = Cache::H5_HOME_ANNOUNCEMENT_LIST_KEY;

        $data = Cache::get($key);

        $list = json_decode($data, true);

        foreach ($list as $k => $item) {
            $list[$k]['url'] = Url::toRoute([
                '/announcement/detail',
                'id' => $item['id'],
            ]);
        }

        return $list;
    }

    // 这个方法是完全复制一下上面的list,用于加快首页的加载速度
    public static function getTmpHomeList($searchData, $needSearchData = false): array
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'an.article_id = a.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = a.id')
            ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id = a.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->where(['a.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['a.status' => BaseArticle::STATUS_ACTIVE])
            ->andWhere(['a.is_show' => BaseArticle::IS_SHOW_YES]);

        //查询属性
        if (is_array($searchData['attribute'])) {
            $query->andFilterWhere([
                'in',
                'aa.type',
                $searchData['attribute'],
            ]);
        } else {
            $query->andFilterWhere([
                'aa.type' => $searchData['attribute'],
            ]);
        }
        //栏目查询
        if (!empty($searchData['columnId'])) {
            //如果有栏目的话，判断当前栏目是一级还是二级
            $columnLevel = BaseHomeColumn::findOneVal(['id' => $searchData['columnId']], 'level');
            if ($columnLevel == BaseHomeColumn::LEVEL_FIRST) {
                //查询当前一级栏目下的二级栏目
                $secondLevelColumnList = BaseHomeColumn::find()
                    ->where(['parent_id' => $searchData['columnId']])
                    ->select('id')
                    ->asArray()
                    ->all();
                $columnList            = array_values($secondLevelColumnList);
                //加上一级栏目id
                array_push($columnList, $searchData['columnId']);
                $query->andFilterWhere([
                    'in',
                    'ac.column_id',
                    $columnList,
                ]);
            } elseif ($columnLevel == BaseHomeColumn::LEVEL_SECOND) {
                $query->andFilterWhere(['ac.column_id' => $searchData['columnId']]);
            }
        }

        //发布时间查询
        if ($searchData['startReleaseTime']) {
            $query->andWhere([
                '>=',
                'a.refresh_time',
                TimeHelper::dayToBeginTime($searchData['startReleaseTime']),
            ]);
        }
        if ($searchData['endReleaseTime']) {
            $query->andWhere([
                '<=',
                'a.refresh_time',
                TimeHelper::dayToEndTime($searchData['endReleaseTime']),
            ]);
        }

        $query->groupBy(['aa.article_id']);

        $count    = $query->count();
        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['newestAnnouncementPageSize'];
        $pages    = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->limit($pages['limit'])
            ->offset($pages['offset'])
            ->select([
                'an.id',
                'an.title',
                'a.seo_description as seoDescription',
                'a.click as clickAmount',
                'aa.sort_time',
                'c.full_name as companyName',
                'c.logo_url',
                'a.cover_thumb as coverThumb',
            ])
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();

        $allCityIdText  = '';
        $allMajorIdText = '';
        foreach ($list as &$v) {
            if ($needSearchData) {
                //需要筛选项，再进行查询
                $jobInfo = BaseJob::getAnnouncementJobInfo($v['id']);

                //获取公告下的职位数量
                $v['jobAmount'] = $jobInfo['jobAmount'];
                //获取公告下职位招聘数量
                $v['recruitAmount'] = $jobInfo['recruitAmount'];
                //拼接专业和城市
                $allCityIdText  .= $jobInfo['cityText'] . ',';
                $allMajorIdText .= $jobInfo['majorText'] . ',';
            }

            $v['refreshTime'] = date('Y-m-d', $v['refreshTime']);
            $v['sortTime']    = date('Y-m-d', $v['sort_time']);
            $v['companyLogo'] = BaseCompany::getLogoFullUrl($v['logo_url']);
            $v['coverThumb']  = FileHelper::getFullUrl($v['coverThumb']);
        }
        $cityList  = [];
        $majorList = [];
        if ($needSearchData) {
            //拼接页面的工作地点和学科专业筛选项数据
            //去除多余逗号
            $allCityIdText  = substr($allCityIdText, 0, -1);
            $allMajorIdText = substr($allMajorIdText, 0, -1);
            $allCityIdArr   = array_unique(explode(',', $allCityIdText));
            $allMajorIdArr  = array_unique(explode(',', $allMajorIdText));

            foreach ($allCityIdArr as $cityId) {
                $cityList[] = [
                    'k' => $cityId,
                    'v' => BaseArea::getAreaName($cityId),
                ];
            }
            foreach ($allMajorIdArr as $majorId) {
                $majorList[] = [
                    'k' => $majorId,
                    'v' => BaseMajor::getMajorName($majorId),
                ];
            }
        }

        return [
            'list'      => $list,
            'cityList'  => $cityList,
            'majorList' => $majorList,
            'page'      => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    //    todo:暂时写死

    /**
     * 获取热门公告
     * @param $searchData
     * @return array
     */
    public static function getHotList($days, $count)
    {
        switch ($days) {
            case 3:
                $hotArticleList = BaseAnnouncement::getThreeDayTop();
                break;
            case 7:
                $hotArticleList = BaseAnnouncement::getWeekTop();
                break;
            case 15:
                $hotArticleList = BaseAnnouncement::getHalfMonthTop();
                break;
            case 30:
                $hotArticleList = BaseAnnouncement::getMonthTop();
        }
        $hotArticleList = array_slice($hotArticleList, 0, $count);

        $articleList = [];
        foreach ($hotArticleList as $hotArticle) {
            $articleInfo          = BaseArticle::find()
                ->where(['id' => $hotArticle['articleId']])
                ->select([
                    'title',
                ])
                ->asArray()
                ->one();
            $articleInfo['id']    = $hotArticle['announcementId'];
            $articleInfo['click'] = $hotArticle['click'];
            $articleInfo['url']   = Url::toRoute([
                '/announcement/detail',
                'id' => $hotArticle['announcementId'],
            ]);
            array_push($articleList, $articleInfo);
        }

        return $articleList;
    }

    /**
     * 获取公告详情
     * @param $id
     * @param $isDelCache 是否清除缓存重新设置
     * @return array|false|mixed|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function getDetailInfo($id, $isDelCache = false)
    {
        $cacheKey = Cache::H5_ANNOUNCEMENT_DETAIL_KEY . ':' . $id;
        //需要清空缓存重新设置
        if ($isDelCache) {
            Cache::delete($cacheKey);
        }
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            $info = json_decode($cacheData, true);
        } else {
            $info = self::find()
                ->alias('an')
                ->innerJoin(['a' => BaseArticle::tableName()], 'a.id=an.article_id')
                ->innerJoin(['c' => BaseCompany::tableName()], 'an.company_id = c.id')
                ->where(['an.id' => $id])
                ->select([
                    'article_id',
                    'is_show',
                    'an.template_id',
                    'an.id',
                    'an.title',
                    'an.file_ids',
                    'an.add_time',
                    'c.logo_url',
                    'c.full_name as companyName',
                    'c.id as companyId',
                    'c.type as companyType',
                    'c.nature as companyNature',
                    'c.scale as companyScale',
                    'c.is_cooperation as isCooperation',
                    'a.refresh_time',
                    'a.content',
                    'a.status',
                    'a.id as articleId',
                    'an.period_date as periodDate',
                    'a.refresh_time as refreshTime',
                    'a.seo_keywords',
                    'a.seo_description',
                    'a.home_column_id as homeColumnId',
                    'an.apply_type',
                    'an.delivery_way',
                    'an.delivery_type',
                    'is_cooperation',
                    'establishment_type as establishmentType',
                ])
                ->asArray()
                ->one();
            if (empty($info)) {
                return false;
            }

            //获取公告在招职位数量
            $onLineJobNum         = BaseJob::getAnnouncementJobAmount($id, BaseAnnouncement::STATUS_ONLINE);
            $info['allJobAmount'] = BaseJob::getAnnouncementJobAmount($id);
            $info['jobAmount']    = $onLineJobNum;
            if ($onLineJobNum > 99) {
                $info['jobListAmount'] = '99+';
            } else {
                $info['jobListAmount'] = $onLineJobNum;
            }
            //获取公告招聘人
            $info['jobRecruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($id);
            //获取公告下职位列表(全部)
            $searchData                = [
                'announcementId' => $id,
            ];
            $jobListData               = Job::getAnnouncementNextJobList($searchData);
            $info['jobList']           = $jobListData['list'];
            $info['jobListCount']      = $jobListData['count'];
            $totalJobListData          = Job::getAnnouncementNextJobList($searchData, false);
            $info['totalJobList']      = $totalJobListData['list'];
            $info['totalJobListCount'] = $totalJobListData['count'];

            //获取logo完整路径
            $info['logo'] = BaseCompany::getLogoFullUrl($info['logo_url']);
            //获取公告下的所有学历
            $info['educationText'] = trim(BaseJob::getAnnouncementJobEducationType($id));

            //获取公告下的专业列表
            $info['majorName'] = self::getAllMajorName($id, 'text');
            //获取公告下的所有城市
            $info['cityName'] = self::getAllCityName($id);
            $info['cityIds']  = self::getAllCityId($id);
            if ($info['periodDate'] == TimeHelper::ZERO_TIME) {
                $info['periodDate'] = '详见正文';
            } else {
                $info['periodDate'] = date('Y-m-d', strtotime($info['periodDate']));
            }
            if ($info['refreshTime'] == TimeHelper::ZERO_TIME) {
                $info['fullRefreshTime'] = $info['add_time'];
                $info['refreshTime']     = date('Y-m-d', strtotime($info['add_time']));;
            } else {
                $info['fullRefreshTime'] = $info['refreshTime'];
                $info['refreshTime']     = date('Y-m-d', strtotime($info['refreshTime']));
            }
            //单位性质、类型、规模
            $info['companyType']   = BaseDictionary::getCompanyTypeName($info['companyType']);
            $info['companyNature'] = BaseDictionary::getCompanyNatureName($info['companyNature']);
            $info['companyScale']  = BaseDictionary::getCompanyScaleName($info['companyScale']);
            //单位url
            $info['companyUrl'] = UrlHelper::createCompanyDetailPath($info['companyId']);

            //获取报名方式
            ////获取公告职位信息
            $job_list        = BaseJob::find()
                ->select('delivery_way,delivery_type,apply_type,apply_address')
                ->andWhere(['announcement_id' => $info['id']])
                ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
                ->andWhere([
                    'status' => [
                        BaseJob::STATUS_OFFLINE,
                        BaseJob::STATUS_ONLINE,
                    ],
                ])
                ->asArray()
                ->all();
            $jobApplyTypeArr = [];
            //处理一下没有跟公告就不拿
            $announcement_bool = false;
            foreach ($job_list as $item) {
                if (!empty($item['delivery_type'])) {
                    if ($item['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                        $applyTypeList = explode(',', $item['apply_type']);
                        foreach ($applyTypeList as $type_item) {
                            array_push($jobApplyTypeArr, BaseDictionary::getSignUpName($type_item));
                        }
                    } else {
                        array_push($jobApplyTypeArr, '站内投递');
                    }
                } else {
                    if (!$announcement_bool) {
                        $announcement_bool = true;
                    }
                }
            }
            $announcementApplyTypeArr = [];
            if ($info['delivery_type'] > 0 && $announcement_bool) {
                if ($info['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                    $announcementApplyTypeTxt = BaseJob::getApplyTypeName($info['apply_type']);
                    $announcementApplyTypeArr = explode(',', $announcementApplyTypeTxt);
                } else {
                    $announcementApplyTypeArr = ['站内投递'];
                }
            }
            $applyTypeArr          = array_merge($announcementApplyTypeArr, $jobApplyTypeArr);
            $applyTypeArr          = array_unique($applyTypeArr);
            $info['applyTypeText'] = implode(',', $applyTypeArr);
            //双会特殊处理
            if ($info['template_id'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                $info['applyTypeText'] = '站内报名';
            }
            $info['establishmentTypeText'] = '';
            if ($info['establishmentType']) {
                $info['establishmentTypeText'] = self::ESTABLISHMENT_TEXT_LIST[$info['establishmentType']];
            }

            Cache::set($cacheKey, json_encode($info), self::DETAIL_CACHE_TIME);
        }

        return $info;
    }

    /**
     * 获取公告的报名方式，公告+职位的报名方式汇总
     * @param $announcementId
     * @return false|string
     * @throws \Exception
     */
    public static function getSignUpList($announcementId)
    {
        $jobList   = Job::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere(['status' => Job::STATUS_ONLINE])
            ->select('apply_type')
            ->asArray()
            ->all();
        $applyText = '';
        if (!empty($jobList)) {
            foreach ($jobList as $job) {
                if (!empty($job['apply_type'])) {
                    $applyText .= $job['apply_type'] . ',';
                }
            }
        }
        $announcementApplyType = self::findOneVal(['id' => $announcementId], 'apply_type');
        if (!empty($applyText)) {
            if (!empty($announcementInfo)) {
                $applyText .= $announcementApplyType;
            }
        } else {
            $applyText = $announcementApplyType;
        }
        $applyArr = explode(',', $applyText);
        $applyArr = array_unique(array_filter($applyArr));

        $applyTypeText = '';
        foreach ($applyArr as $applyType) {
            $applyTypeText .= BaseDictionary::getSignUpName($applyType) . ',';
        }
        $applyTypeText = substr($applyTypeText, '0', '-1');

        return $applyTypeText;
    }

    /**
     * 获取栏目/首页下面的最新公告 (逻辑是差不多的,每一页9个,然后按照公告的刷新时间来排序),有一个200条的限制
     * @param $page
     * @param $columnId
     */
    public static function getNewestList($page, $columnId = 0)
    {
        if ($page > 22) {
            return [];
        }
        $defaultSize = 10;//默认一页显示数量

        // 这里其实只需要公告的ID和标题
        $query = Article::find()
            ->alias('a')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'a.type'      => [
                    Article::TYPE_ANNOUNCEMENT,
                    Article::TYPE_NEWS,
                ],
            ]);

        if ($columnId) {
            $query->innerJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id = a.id')
                ->andWhere(['c.column_id' => $columnId]);
        }
        ////获取五条置顶公告数据,不足用不同数据去补，在这里获取是要在其他页码数时候进行剔除操作
        $top_query = clone $query;
        $top_data  = $top_query->innerJoin(['aa' => BaseArticleAttribute::tableName()], 'a.id=aa.article_id')
            ->select('a.id,a.title,a.type,a.refresh_time,a.click as clickAmount,aa.type as notice_type')
            ->orderBy('aa.sort_time desc')
            ->andWhere(['aa.type' => BaseArticleAttribute::ATTRIBUTE_HOME_TOP])
            ->limit(10)
            ->offset(0)
            ->asArray()
            ->all();

        //最新更新第一页的处理逻辑
        if ($page == 1) {
            //第一页数据前10条要进行进行置顶
            //因为存在不足10条情况，具体几条需要看制定拿到的数据
            $defaultSize -= count($top_data);
        }
        $top_id_arr = array_column($top_data, 'id');
        //防止空条件操作
        $query->andFilterWhere([
            'not in',
            'b.id',
            $top_id_arr,
        ]);
        $offset = ($page - 1) * $defaultSize;//偏移数量
        $list   = $query->select('a.id,a.title,a.type,a.refresh_time,a.click as clickAmount,company_id')
            // ->orderBy('a.refresh_time desc')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'b.article_id = a.id')
            ->innerJoin(['d' => Company::tableName()], 'd.id = b.company_id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'd.group_score_system_id = cgss.id')
            //            ->orderBy('refresh_date desc,d.sort desc')
            ->orderBy('refresh_date desc,b.is_first_release asc,cgss.score desc,b.id desc')
            ->limit($defaultSize)
            ->offset($offset)
            ->asArray()
            ->all();
        //第一页需要合并，其他只要剔除不需要合并
        if ($page == 1) {
            foreach ($top_data as &$top_item) {
                $top_item['is_top'] = 1;
            }
            $list = array_merge($top_data, $list);
        }
        foreach ($list as &$item) {
            $item['url']           = BaseHomePosition::getDetailUrlNew($item['type'], $item['id']);
            $item['jobAmount']     = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
            $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: '若干';
            $item['refreshTime']   = TimeHelper::short($item['refresh_time']);
            // $item['refreshTime']   = substr($item['refresh_time'], 0, 10);
            //            if ($item['cover_thumb']) {
            //                $item['cover_thumb'] = FileHelper::getFullUrl($item['cover_thumb']);
            //                // coverThumb
            //                // companyLogo
            //                // companyName
            //                $company             = BaseCompany::find()
            //                    ->where($item['company_id'])
            //                    ->select('full_name,logo_url')
            //                    ->asArray()
            //                    ->one();
            //                $item['companyName'] = $company['full_name'];
            //                $item['companyLogo'] = FileHelper::getFullUrl($item['logo_url']);
            //            }
            ///title
            // jobAmount
            // recruitAmount
            // clickAmount
            // refreshTime
            // url

        }

        return $list;
    }

    /**
     * 拼接公告热门搜索数据
     * @return array
     */
    public static function getHotKeywordList()
    {
        $hotList = \Yii::$app->params['hotSearchList']['announcement'];
        $list    = [];
        foreach ($hotList as $k => $item) {
            $list[$k]['name'] = $item;
            $list[$k]['url']  = Url::toRoute([
                'home/search-result',
                'keyword' => $item,
                'type'    => 1,
            ]);
        }

        return $list;
    }

    public static function getRecommendList($searchData)
    {
        $surplusAmount = $searchData['count'];
        //先查id合集，再查字段

        //公共查询条件一
        $where           = [
            'a.is_show'   => BaseArticle::IS_SHOW_YES,
            'a.status'    => BaseArticle::STATUS_ONLINE,
            'a.is_delete' => BaseArticle::IS_DELETE_NO,
            'a.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
        ];
        $where2          = [
            '<>',
            'b.id',
            $searchData['announcementId'],
        ];
        $announcementIds = [];
        //1、优先展示该单位最新发布的在线公告，最多6条。
        $sameCompanyAnnouncementList = Article::find()
            ->alias('a')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'b.article_id = a.id')
            ->where($where)
            ->andWhere($where2)
            ->andWhere(['b.company_id' => $searchData['companyId']])
            ->select('b.id')
            ->orderBy('a.refresh_time desc')
            ->asArray()
            ->indexBy('b.id')
            ->limit($surplusAmount)
            ->column();
        if (!empty($sameCompanyAnnouncementList)) {
            $surplusAmount             = $surplusAmount - count($sameCompanyAnnouncementList);
            $sameCompanyAnnouncementId = array_keys($sameCompanyAnnouncementList);
            $announcementIds           = $sameCompanyAnnouncementId;
        }

        //2、展示该公告所属栏目下，相同工作地点，会员类型为高级会员的合作单位，最新发布的在线公告，最多6条。
        if ($surplusAmount > 0) {
            //查询工作地点相同的职位的公告
            $jobAnnouncementList = self::getAnnouncementIdListByCity($searchData['cityIds']);
            $jobAnnouncementIds  = array_keys($jobAnnouncementList);
            if ($jobAnnouncementIds) {
                //去除第一条件中已经去除的id
                foreach ($jobAnnouncementIds as $k => $id) {
                    if (in_array($id, $announcementIds)) {
                        unset($jobAnnouncementIds[$k]);
                    }
                }

                $seniorCompanyAnnouncementList = Article::find()
                    ->alias('a')
                    ->innerJoin(['b' => BaseAnnouncement::tableName()], 'b.article_id = a.id')
                    ->leftJoin(['c' => Company::tableName()], 'c.id=b.company_id')
                    ->where($where)
                    ->andWhere($where2)
                    ->andWhere([
                        'a.home_column_id' => $searchData['homeColumnId'],
                        'c.package_type'   => 2,
                        'b.id'             => $jobAnnouncementIds,
                    ])
                    ->select('b.id')
                    ->orderBy('a.refresh_time desc')
                    ->asArray()
                    ->indexBy('b.id')
                    ->limit($surplusAmount)
                    ->column();

                if (!empty($seniorCompanyAnnouncementList)) {
                    $surplusAmount               = $surplusAmount - count($seniorCompanyAnnouncementList);
                    $seniorCompanyAnnouncementId = array_keys($seniorCompanyAnnouncementList);
                    $announcementIds             = array_merge($announcementIds, $seniorCompanyAnnouncementId);
                }
            }
        }

        if ($surplusAmount > 0) {
            //如果还没有填满6个，继续查找
            //如果不存在根据地点查找的公告id列表，则查询一次，否则直接使用
            $jobAnnouncementList = self::getAnnouncementIdListByCity($searchData['cityIds']);
            $jobAnnouncementIds  = array_keys($jobAnnouncementList);
            if ($jobAnnouncementIds) {
                if ($jobAnnouncementIds) {
                    //去除第一条件中已经去除的id
                    foreach ($jobAnnouncementIds as $k => $id) {
                        if (in_array($id, $announcementIds)) {
                            unset($jobAnnouncementIds[$k]);
                        }
                    }
                }
                $otherCompanyAnnouncementList = Article::find()
                    ->alias('a')
                    ->innerJoin(['b' => BaseAnnouncement::tableName()], 'b.article_id = a.id')
                    ->leftJoin(['c' => Company::tableName()], 'c.id=b.company_id')
                    ->where($where)
                    ->andWhere([
                        'a.home_column_id' => $searchData['homeColumnId'],
                        'b.id'             => $jobAnnouncementIds,
                    ])
                    ->andWhere($where2)
                    ->orderBy('a.refresh_time desc')
                    ->asArray()
                    ->indexBy('b.id')
                    ->limit($surplusAmount)
                    ->select('b.id')
                    ->column();

                if (!empty($otherCompanyAnnouncementList)) {
                    $otherCompanyAnnouncementId = array_keys($otherCompanyAnnouncementList);
                    $announcementIds            = array_merge($announcementIds, $otherCompanyAnnouncementId);
                }
            }
        }

        //判断3个数组的id是否存在，存在的话，要做拼接，用来排序
        $sortNum = 1;
        $sortSql = '';
        if (!empty($sameCompanyAnnouncementId)) {
            $sameCompanyAnnouncementIdText = (implode(',', $sameCompanyAnnouncementId));
            $sortSql                       = "(CASE  WHEN b.id in ($sameCompanyAnnouncementIdText) THEN $sortNum ";
        }
        if (!empty($seniorCompanyAnnouncementId)) {
            $seniorCompanyAnnouncementIdText = (implode(',', $seniorCompanyAnnouncementId));
            if (!empty($sortSql)) {
                $sortNum += 1;
                $sortSql .= "WHEN b.id in ($seniorCompanyAnnouncementIdText) THEN $sortNum ";
            } else {
                $sortSql = "(CASE  WHEN b.id in ($seniorCompanyAnnouncementIdText) THEN $sortNum ";
            }
        }
        if (!empty($otherCompanyAnnouncementId)) {
            $otherCompanyAnnouncementIdText = (implode(',', $otherCompanyAnnouncementId));
            if (!empty($sortSql)) {
                $sortNum += 1;
                $sortSql .= "WHEN b.id in ($otherCompanyAnnouncementIdText) THEN $sortNum ";
            } else {
                $sortSql = "(CASE  WHEN b.id in ($otherCompanyAnnouncementIdText) THEN $sortNum ";
            }
        }
        if (!empty($sortSql)) {
            $sortSql .= " END )";
        } else {
            $sortSql = '';
        }

        if (!empty($announcementIds)) {
            //最后根据3个条件获取到的公告id，查询完整的列表出来
            $list = Article::find()
                ->alias('a')
                ->innerJoin(['b' => BaseAnnouncement::tableName()], 'b.article_id = a.id')
                ->where($where)
                ->andWhere(['b.id' => $announcementIds])
                ->select([
                    'b.id',
                    'b.title',
                    'a.refresh_time',
                    " $sortSql as sortNum",
                ])
                ->orderBy('sortNum asc,a.refresh_time desc')
                ->asArray()
                ->all();

            foreach ($list as &$announcement) {
                //获取公告下的职位数量
                $announcement['jobAmount'] = Job::getAnnouncementJobAmount($announcement['id']);
                //获取公告的职位招聘数量
                $announcement['recruitAmount'] = Job::getAnnouncementJobRecruitAmount($announcement['id']);
                //获取公告下的最低学历要求
                $announcement['education'] = trim(BaseJob::getAnnouncementJobEducationType($announcement['id']));
                //获取公告地点
                $announcement['city'] = self::getOnlineNewCityName($announcement['id']);
                //获取url
                $announcement['url'] = self::getDetailUrl($announcement['id']);
            }

            return $list;
        } else {
            return [];
        }
    }

    private static function getAnnouncementIdListByCity($cityIds)
    {
        if (!empty($cityIds)) {
            //查询工作地点相同的职位的公告
            $jobAnnouncementList = Job::find()
                ->where([
                    'status'  => Job::STATUS_ACTIVE,
                    'city_id' => $cityIds,
                ])
                ->andWhere([
                    '<>',
                    'announcement_id',
                    0,
                ])
                ->groupBy('announcement_id')
                ->select('announcement_id')
                ->asArray()
                ->indexBy('announcement_id')
                ->column();

            return $jobAnnouncementList;
        } else {
            return [];
        }
    }

}